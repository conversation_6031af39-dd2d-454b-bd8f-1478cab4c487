﻿#include "KDataInclude.h"
#include "bmruntime_interface.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KCondition.h"
#include "algorithm/KAIEngine.h"
#include "KControllerAIEngineManager.h"
#include "module/ImageProcess/KImageProcess.h"

enum ImageProcessType
{
    ImageProcessType_KeepRatioResize, // 图像固定比例缩放模块测试
    ImageProcessType_Resize,          // 图像缩放模块测试
    ImageProcessType_Filter,          // 图像滤波模块测试
    ImageProcessType_Enhance,         // 图像增强模块测试
};

int main(int argc, char *argv[])
{
    system("mkdir -p saveimages");
    system("rm -rf saveimages/*");
    ImageProcessType imageProcessType = ImageProcessType_Enhance;

    cv::Mat img = cv::imread("test.png");
    if (img.empty())
    {
        std::cout << "Failed to load image" << std::endl;
        return -1;
    }
    std::cout << "img w = " << img.cols << " h = " << img.rows << std::endl;
    cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
    KImage2Data image2In(img);

    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed;
    switch (imageProcessType)
    {
    case ImageProcessType_KeepRatioResize:
    {
        KKeepRatioResizeModule mKeepRatioResizeModule;
        mKeepRatioResizeModule.setId("mKeepRatioResizeModule");
        KInt widthorheight, dstheight, dstwidth;
        widthorheight.setValue(1);
        dstheight.setValue(1024);
        dstwidth.setValue(1024);

        mKeepRatioResizeModule.getParamData("image2In")->bind(&image2In);

        mKeepRatioResizeModule.getParamData("widthorheight")->bind(&widthorheight);
        mKeepRatioResizeModule.getParamData("dstheight")->bind(&dstheight);
        mKeepRatioResizeModule.getParamData("dstwidth")->bind(&dstwidth);

        start = std::chrono::high_resolution_clock::now();
        mKeepRatioResizeModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像固定比例缩放模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mKeepRatioResizeModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case ImageProcessType_Resize:
    {
        KImageResizeModule mImageResizeModule;
        mImageResizeModule.setId("mImageResizeModule");
        KInt interpolationType, dstheight, dstwidth;
        interpolationType.setValue(1);
        dstheight.setValue(1024);
        dstwidth.setValue(1024);

        mImageResizeModule.getParamData("image2In")->bind(&image2In);

        mImageResizeModule.getParamData("interpolationType")->bind(&interpolationType);
        mImageResizeModule.getParamData("dstheight")->bind(&dstheight);
        mImageResizeModule.getParamData("dstwidth")->bind(&dstwidth);

        start = std::chrono::high_resolution_clock::now();
        mImageResizeModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像缩放模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mImageResizeModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case ImageProcessType_Filter:
    {
        KImageFilterModule mImageFilterModule;
        mImageFilterModule.setId("mImageFilterModule");
        KInt filterType;
        filterType.setValue(1);
        KInt gaussianKernel, medianKernel, meanWidthKernel, meanHeightKernel;
        gaussianKernel.setValue(3);
        medianKernel.setValue(3);
        meanWidthKernel.setValue(3);
        meanHeightKernel.setValue(3);

        mImageFilterModule.getParamData("image2In")->bind(&image2In);
        mImageFilterModule.getParamData("filterType")->bind(&filterType);
        mImageFilterModule.getParamData("gaussianKernel")->bind(&gaussianKernel);
        mImageFilterModule.getParamData("medianKernel")->bind(&medianKernel);
        mImageFilterModule.getParamData("meanWidthKernel")->bind(&meanWidthKernel);
        mImageFilterModule.getParamData("meanHeightKernel")->bind(&meanHeightKernel);

        start = std::chrono::high_resolution_clock::now();
        mImageFilterModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像滤波模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mImageFilterModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case ImageProcessType_Enhance:
    {
        KInt kfilttertype;
        kfilttertype.setValue(1);

        KImageEnhancedModule mEnhanceModule;
        mEnhanceModule.setId("mEnhanceModule");

        mEnhanceModule.getParamData("image2In")->bind(&image2In);
        std::string model = "aimodels/Zero_DCE++_single_output.bmodel";

        KAIEngineInferEnity *e = KControllerAIEngineManager::singleInstance()->createEnity(model);
        ((KSmartParamAIData *)mEnhanceModule.getParamData("smartAi"))->smartAiData()->setEnity(e);
        mEnhanceModule.getParamData("filterType")->bind(&kfilttertype);

        start = std::chrono::high_resolution_clock::now();
        mEnhanceModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "图像增强模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mEnhanceModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    default:
    {
    }
    break;
    }
    return 0;
}