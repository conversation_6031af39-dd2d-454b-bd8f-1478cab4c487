cmake_minimum_required(VERSION 3.10)

project(KRecogizeModule-Test)

file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)

set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-rpath-link,/home/<USER>/Release:/home/<USER>/Release/module:/usr/local/Trolltech/Qt-5.12.0-arm/lib -Wl,-rpath,'$ORIGIN'")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(CMAKE_CXX_FLAGS_RELEASE "-O2 -Wall")
# 启用详细编译信息输出
set(CMAKE_VERBOSE_MAKEFILE ON)
# 添加调试符号
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g")
# 在Release模式下保留调试信息
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -g")

# 包含目录
include_directories(
    ${CMAKE_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/deps
    ${CMAKE_SOURCE_DIR}/deps/framework
)

include_directories(${OpenCV_INCLUDE_DIRS})
link_directories(${OpenCV_LIBRARY_DIRS})

include_directories(${OpenSSL_INCLUDE_DIRS})
link_directories(${OpenSSL_LIBRARY_DIRS})

include_directories(${ZXING_INCLUDE_DIRS})
link_directories(${ZXING_LIBRARY_DIRS})

include_directories(${ZBAR_INCLUDE_DIRS})
link_directories(${ZBAR_LIBRARY_DIRS})

link_directories(${CMAKE_SOURCE_DIR}/bin/Release)
link_directories(${CMAKE_SOURCE_DIR}/bin/Release/module)

# message(STATUS "OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
# message(STATUS "OpenCV_LIBRARY_DIRS: ${OpenCV_LIBRARY_DIRS}")
# message(STATUS "OpenCV_LIBRARIES: ${OpenCV_LIBRARIES}")

find_package(Qt5 REQUIRED COMPONENTS Core Network Widgets Sql Concurrent)


# 设置源文件
set(SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../module/ImageProcess/KImageProcess.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../module/Recogize/KRecogize.cpp
)

# 设置头文件
set(HEADERS
)
set(CMAKE_AUTOMOC ON)
# 创建库
# add_library(${PROJECT_NAME} SHARED ${SOURCES} ${HEADERS})
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})
# 链接依赖库
target_link_libraries(${PROJECT_NAME}
    ${OpenCV_LIBRARIES}
    ${OpenSSL_LIBRARIES}
    ${ZXING_LIBRARIES}
    ${ZBAR_LIBRARIES}
    Qt5::Core
    Qt5::Network
    Qt5::Widgets
    Qt5::Sql
    Qt5::Concurrent
    -lfreetype
    -lKFramework
    -lKBitlandAIEngine
    -lKAlgorithm
)