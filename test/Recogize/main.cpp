﻿#include "KDataInclude.h"
#include "bmruntime_interface.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KCondition.h"
#include "algorithm/KAIEngine.h"
#include "KControllerAIEngineManager.h"
#include "module/ImageProcess/KImageProcess.h"
#include "module/Recogize/KRecogize.h"

#include <dirent.h>
#include <sys/types.h>

enum RecogizeProcessType
{
    RecogizeType_Code,              // 二维码识别模块测试
    RecogizeType_Code_ImageEnhanced // 增强二维码识别模块测试
};

int main(int argc, char *argv[])
{
    system("mkdir -p saveimages");
    system("rm -rf saveimages/*");

    RecogizeProcessType recogizeProcessType = RecogizeType_Code_ImageEnhanced;

    std::string folderPath = "images";
    DIR *dir = opendir(folderPath.c_str());
    if (dir == nullptr)
    {
        std::cerr << "无法打开文件夹: " << folderPath << std::endl;
        return -1;
    }

    struct dirent *entry;

    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed;
    switch (recogizeProcessType)
    {
    case RecogizeType_Code:
    {
        KCodeRecognizeModule mKCodeRecognizeModule;
        mKCodeRecognizeModule.setId("mKCodeRecognizeModuleID");

        KInt downSampleNum, Type;
        downSampleNum.setValue(1);
        Type.setValue(0);
        mKCodeRecognizeModule.getParamData("downSampleNum")->bind(&downSampleNum);
        mKCodeRecognizeModule.getParamData("Type")->bind(&Type);

        KImage2Data image2Out;
        image2Out.bind(mKCodeRecognizeModule.getResultData("image2Out"));

        while ((entry = readdir(dir)) != nullptr)
        {
            std::string filename = entry->d_name;
            if (filename.length() > 4 && (filename.substr(filename.length() - 4) == ".png" || filename.substr(filename.length() - 4) == ".jpg"))
            {
                std::string filepath = folderPath + "/" + filename;
                std::cout << "filepath: " << filepath << std::endl;
                cv::Mat img = cv::imread(filepath);
                if (img.empty())
                {
                    std::cout << "Failed to load image: " << filepath << std::endl;
                    continue;
                }
                cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
                KImage2Data image2In(img);
                mKCodeRecognizeModule.getParamData("image2In")->bind(&image2In);

                start = std::chrono::high_resolution_clock::now();
                mKCodeRecognizeModule.run();
                end = std::chrono::high_resolution_clock::now();
                elapsed = end - start;
                std::cout << "识别模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

                cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
                std::string outname = "saveimages/" + filename.substr(0, filename.length() - 4) + "_result.jpg";
                cv::imwrite(outname, image2Out.Image2().src());
            }
        }
        std::cout << "二维码识别模块识别次数: " << mKCodeRecognizeModule.count << std::endl;
    }
    break;
    case RecogizeType_Code_ImageEnhanced:
    {
        // 增强模块
        KInt kfilttertype, enhanceNum;
        kfilttertype.setValue(0);
        enhanceNum.setValue(1);

        KImageEnhancedModule mEnhanceModule;
        mEnhanceModule.setId("mEnhanceModule");

        std::string model = "aimodels/Zero_DCE++_single_output.bmodel";

        KAIEngineInferEnity *e = KControllerAIEngineManager::singleInstance()->createEnity(model);
        ((KSmartParamAIData *)mEnhanceModule.getParamData("smartAi"))->smartAiData()->setEnity(e);
        mEnhanceModule.getParamData("filterType")->bind(&kfilttertype);
        mEnhanceModule.getParamData("enhanceNum")->bind(&enhanceNum);

        KImage2Data image2In_enhane;
        mEnhanceModule.getParamData("image2In")->bind(&image2In_enhane);

        KImage2Data image2Out_enhance;
        image2Out_enhance.bind(mEnhanceModule.getResultData("image2Out"));

        // 识别模块
        KCodeRecognizeModule mKCodeRecognizeModule;
        mKCodeRecognizeModule.setId("mKCodeRecognizeModuleID");

        KInt downSampleNum, Type;
        downSampleNum.setValue(1);
        Type.setValue(0);
        mKCodeRecognizeModule.getParamData("downSampleNum")->bind(&downSampleNum);
        mKCodeRecognizeModule.getParamData("Type")->bind(&Type);

        KImage2Data image2In_recogize;
        mKCodeRecognizeModule.getParamData("image2In")->bind(&image2In_recogize);

        KImage2Data image2Out_recogize;
        image2Out_recogize.bind(mKCodeRecognizeModule.getResultData("image2Out"));

        while ((entry = readdir(dir)) != nullptr)
        {
            std::string filename = entry->d_name;
            if (filename.length() > 4 && (filename.substr(filename.length() - 4) == ".png" || filename.substr(filename.length() - 4) == ".jpg"))
            {
                std::string filepath = folderPath + "/" + filename;
                std::cout << "filepath: " << filepath << std::endl;
                cv::Mat img = cv::imread(filepath);
                if (img.empty())
                {
                    std::cout << "Failed to load image: " << filepath << std::endl;
                    continue;
                }
                // 检查图像宽度是否为64的倍数
                int width = img.cols;
                int height = img.rows;

                if (width % 64 != 0)
                {
                    // 计算调整后的宽度，确保是64的倍数
                    int newWidth = (width / 64 + 1) * 64;

                    // 保持宽高比例调整高度
                    float ratio = (float)newWidth / width;
                    int newHeight = height * ratio;

                    // 调整图像大小
                    cv::resize(img, img, cv::Size(newWidth, newHeight), 0, 0, cv::INTER_LINEAR);
                }
                cv::cvtColor(img, img, cv::COLOR_BGR2RGB);

                image2In_enhane.Image2().src() = img;
                start = std::chrono::high_resolution_clock::now();
                mEnhanceModule.run();
                end = std::chrono::high_resolution_clock::now();
                elapsed = end - start;
                std::cout << "图像增强模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

                std::string outname_enhance = "enhance/" + filename.substr(0, filename.length() - 4) + "_enhance.png";
                cv::Mat img_enhance = image2Out_enhance.Image2().src();
                cv::imwrite(outname_enhance, img_enhance);
                // cv::cvtColor(img_enhance, img_enhance, cv::COLOR_BGR2RGB);
                image2In_recogize.Image2().src() = img_enhance;

                start = std::chrono::high_resolution_clock::now();
                mKCodeRecognizeModule.run();
                end = std::chrono::high_resolution_clock::now();
                elapsed = end - start;
                std::cout << "识别模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
                cv::Mat img_recogize = image2Out_recogize.Image2().src();
                cv::cvtColor(img_recogize, img_recogize, cv::COLOR_RGB2BGR);
                std::string outname = "saveimages/" + filename.substr(0, filename.length() - 4) + "_result.jpg";
                cv::imwrite(outname, img_recogize);
            }
        }
        std::cout << "识别模块识别次数: " << mKCodeRecognizeModule.count << std::endl;
    }
    break;
    default:
    {
    }
    break;
    }
    closedir(dir);
    return 0;
}