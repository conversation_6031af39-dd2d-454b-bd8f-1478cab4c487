﻿#include "KDataInclude.h"
#include "bmruntime_interface.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KCondition.h"
#include "algorithm/KAIEngine.h"
#include "KControllerAIEngineManager.h"
#include "module/ColorProcess/KColorProcess.h"

enum ColorProcessType
{
    ColorProcessType_Cvt,         // 颜色空间转换模块测试
    ColorProcessType_Measurement, // 颜色测量模块测试
    ColorProcessType_Extraction,  // 颜色提取模块测试
};

int main(int argc, char *argv[])
{
    // 检查并创建saveimages文件夹
    system("mkdir -p saveimages");
    system("rm -rf saveimages/*");
    ColorProcessType colorProcessType = ColorProcessType_Cvt;

    cv::Mat img = cv::imread("test.png");
    if (img.empty())
    {
        std::cout << "Failed to load image" << std::endl;
        return -1;
    }
    std::cout << "img w = " << img.cols << " h = " << img.rows << std::endl;
    cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
    KImage2Data image2In(img);

    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed;

    switch (colorProcessType)
    {
    case ColorProcessType_Cvt:
    {
        KInt convterType;
        KInt rgb2graytype;
        convterType.setValue(3);
        rgb2graytype.setValue(0);

        KImageColorCvtModule mKImageColorCvtModule;
        mKImageColorCvtModule.setId("mKImageColorCvtModuleID");
        mKImageColorCvtModule.getParamData("convterType")->bind(&convterType);
        mKImageColorCvtModule.getParamData("rgb2graytype")->bind(&rgb2graytype);

        mKImageColorCvtModule.getParamData("image2In")->bind(&image2In);
        start = std::chrono::high_resolution_clock::now();
        mKImageColorCvtModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "颜色空间转换模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out, image2OutChannel1, image2OutChannel2, image2OutChannel3;
        image2Out.bind(mKImageColorCvtModule.getResultData("image2Out"));
        image2OutChannel1.bind(mKImageColorCvtModule.getResultData("image2OutChannel1"));
        image2OutChannel2.bind(mKImageColorCvtModule.getResultData("image2OutChannel2"));
        image2OutChannel3.bind(mKImageColorCvtModule.getResultData("image2OutChannel3"));
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
        cv::imwrite("saveimages/resultChannel1.jpg", image2OutChannel1.Image2().src());
        cv::imwrite("saveimages/resultChannel2.jpg", image2OutChannel2.Image2().src());
        cv::imwrite("saveimages/resultChannel3.jpg", image2OutChannel3.Image2().src());
    }
    break;
    case ColorProcessType_Measurement:
    {
        KImageColorMeasurementModule mKImageColorMeasurementModule;
        mKImageColorMeasurementModule.setId("mKImageColorMeasurementModuleID");
        mKImageColorMeasurementModule.getParamData("image2In")->bind(&image2In);

        KInt channelMaxValue[3], channelMinValue[3];
        KDouble channelMeanValue[3], channelStdValue[3];
        KImage2Data image2OutHist[3];
        channelMaxValue[0].bind(mKImageColorMeasurementModule.getResultData("channelMaxValue0"));
        channelMaxValue[1].bind(mKImageColorMeasurementModule.getResultData("channelMaxValue1"));
        channelMaxValue[2].bind(mKImageColorMeasurementModule.getResultData("channelMaxValue2"));
        channelMinValue[0].bind(mKImageColorMeasurementModule.getResultData("channelMinValue0"));
        channelMinValue[1].bind(mKImageColorMeasurementModule.getResultData("channelMinValue1"));
        channelMinValue[2].bind(mKImageColorMeasurementModule.getResultData("channelMinValue2"));
        channelMeanValue[0].bind(mKImageColorMeasurementModule.getResultData("channelMeanValue0"));
        channelMeanValue[1].bind(mKImageColorMeasurementModule.getResultData("channelMeanValue1"));
        channelMeanValue[2].bind(mKImageColorMeasurementModule.getResultData("channelMeanValue2"));
        channelStdValue[0].bind(mKImageColorMeasurementModule.getResultData("channelStdValue0"));
        channelStdValue[1].bind(mKImageColorMeasurementModule.getResultData("channelStdValue1"));
        channelStdValue[2].bind(mKImageColorMeasurementModule.getResultData("channelStdValue2"));
        image2OutHist[0].bind(mKImageColorMeasurementModule.getResultData("image2OutHist0"));
        image2OutHist[1].bind(mKImageColorMeasurementModule.getResultData("image2OutHist1"));
        image2OutHist[2].bind(mKImageColorMeasurementModule.getResultData("image2OutHist2"));

        start = std::chrono::high_resolution_clock::now();
        mKImageColorMeasurementModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "颜色测量模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
        cv::imwrite("saveimages/resultHist0.jpg", image2OutHist[0].Image2().src());
        cv::imwrite("saveimages/resultHist1.jpg", image2OutHist[1].Image2().src());
        cv::imwrite("saveimages/resultHist2.jpg", image2OutHist[2].Image2().src());
        std::cout << "channelMaxValue0: " << channelMaxValue[0].iValue() << std::endl;
        std::cout << "channelMaxValue1: " << channelMaxValue[1].iValue() << std::endl;
        std::cout << "channelMaxValue2: " << channelMaxValue[2].iValue() << std::endl;
        std::cout << "channelMinValue0: " << channelMinValue[0].iValue() << std::endl;
        std::cout << "channelMinValue1: " << channelMinValue[1].iValue() << std::endl;
        std::cout << "channelMinValue2: " << channelMinValue[2].iValue() << std::endl;
        std::cout << "channelMeanValue0: " << channelMeanValue[0].dValue() << std::endl;
        std::cout << "channelMeanValue1: " << channelMeanValue[1].dValue() << std::endl;
        std::cout << "channelMeanValue2: " << channelMeanValue[2].dValue() << std::endl;
        std::cout << "channelStdValue0: " << channelStdValue[0].dValue() << std::endl;
        std::cout << "channelStdValue1: " << channelStdValue[1].dValue() << std::endl;
        std::cout << "channelStdValue2: " << channelStdValue[2].dValue() << std::endl;
    }
    break;
    case ColorProcessType_Extraction:
    {
        KImageColorExtractionModule mKImageColorExtractionModule;
        mKImageColorExtractionModule.setId("mKImageColorExtractionModuleID");
        mKImageColorExtractionModule.getParamData("image2In")->bind(&image2In);

        KInt channelMaxValue[3], channelMinValue[3];
        channelMaxValue[0].setValue(255);
        channelMaxValue[1].setValue(255);
        channelMaxValue[2].setValue(255);
        channelMinValue[0].setValue(100);
        channelMinValue[1].setValue(0);
        channelMinValue[2].setValue(0);
        mKImageColorExtractionModule.getParamData("channelMaxValue0")->bind(&channelMaxValue[0]);
        mKImageColorExtractionModule.getParamData("channelMaxValue1")->bind(&channelMaxValue[1]);
        mKImageColorExtractionModule.getParamData("channelMaxValue2")->bind(&channelMaxValue[2]);
        mKImageColorExtractionModule.getParamData("channelMinValue0")->bind(&channelMinValue[0]);
        mKImageColorExtractionModule.getParamData("channelMinValue1")->bind(&channelMinValue[1]);
        mKImageColorExtractionModule.getParamData("channelMinValue2")->bind(&channelMinValue[2]);

        KImage2Data image2Out;
        image2Out.bind(mKImageColorExtractionModule.getResultData("image2Out"));
        KInt ColorRegionArea;
        ColorRegionArea.bind(mKImageColorExtractionModule.getResultData("ColorRegionArea"));

        start = std::chrono::high_resolution_clock::now();
        mKImageColorExtractionModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "颜色提取模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
        std::cout << "ColorRegionArea: " << ColorRegionArea.iValue() << std::endl;
    }
    break;
    default:
    {
    }
    break;
    }

    return 0;
}