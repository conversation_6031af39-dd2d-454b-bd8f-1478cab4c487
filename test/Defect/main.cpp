﻿#include "KDataInclude.h"
#include "bmruntime_interface.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KCondition.h"
#include "algorithm/KAIEngine.h"
#include "KControllerAIEngineManager.h"
#include "module/Defect/KDefect.h"
enum DefectProcessType
{
    DefectType_Surface, // 表面缺陷滤波模块测试
};

int main(int argc, char *argv[])
{
    system("mkdir -p saveimages");
    system("rm -rf saveimages/*");
    DefectProcessType defectProcessType = DefectType_Surface;

    cv::Mat img = cv::imread("test.png");
    if (img.empty())
    {
        std::cout << "Failed to load image" << std::endl;
        return -1;
    }
    std::cout << "img w = " << img.cols << " h = " << img.rows << std::endl;
    cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
    KImage2Data image2In(img);

    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed;
    switch (defectProcessType)
    {
    case DefectType_Surface:
    {
        KInt ksize, numKernels;
        KFloat sigma, defectThreshold;
        ksize.setValue(7);
        numKernels.setValue(6);
        sigma.setValue(5);
        defectThreshold.setValue(100.0);

        KSurfaceDefectFilterModule mKDefectModule;
        mKDefectModule.setId("mKDefectModuleID");

        mKDefectModule.getParamData("ksize")->bind(&ksize);
        mKDefectModule.getParamData("numKernels")->bind(&numKernels);
        mKDefectModule.getParamData("sigma")->bind(&sigma);
        mKDefectModule.getParamData("defectThreshold")->bind(&defectThreshold);

        mKDefectModule.getParamData("image2In")->bind(&image2In);
        start = std::chrono::high_resolution_clock::now();
        mKDefectModule.generateKernels();
        // mKDefectModule.visualizeKernels();
        mKDefectModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "表面缺陷滤波模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mKDefectModule.getResultData("image2Out"));
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    default:
    {
    }
    break;
    }
    return 0;
}