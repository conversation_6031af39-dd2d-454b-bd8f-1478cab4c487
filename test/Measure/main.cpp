﻿#include "KDataInclude.h"
#include "bmruntime_interface.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KCondition.h"
#include "algorithm/KAIEngine.h"
#include "KControllerAIEngineManager.h"
#include "module/Measure/KMeasure.h"
enum MeasureProcessType
{
    MeasureType_PointTolineDist,  // 点线距离测量模块测试
    MeasureType_PointTopointDist, // 点点距离测量模块测试
    MeasureType_LineTolineDist,   // 线线距离测量模块测试
    MeasureType_Brightness,       // 亮度测量模块测试
    MeasureType_LineEdge,         // 直线边缘测量检测模块测试
    MeasureType_CircleEdge,       // 圆边缘测量检测模块测试
};

int main(int argc, char *argv[])
{
    system("mkdir -p saveimages");
    system("rm -rf saveimages/*");
    MeasureProcessType measureProcessType = MeasureType_LineEdge;

    cv::Mat img = cv::imread("line.png");
    if (img.empty())
    {
        std::cout << "Failed to load image" << std::endl;
        return -1;
    }
    std::cout << "img w = " << img.cols << " h = " << img.rows << std::endl;
    cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
    KImage2Data image2In(img);

    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed;
    switch (measureProcessType)
    {
    case MeasureType_PointTolineDist:
    {
        KPointTolineDistModule mKPointTolineDistModule;
        mKPointTolineDistModule.getParamData("image2In")->bind(&image2In);
        start = std::chrono::high_resolution_clock::now();
        mKPointTolineDistModule.setInputPoint(cv::Point2d(477, 250));
        mKPointTolineDistModule.setInputLine(cv::Point2d(120, 210), cv::Point2d(212, 868));
        mKPointTolineDistModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "点线距离测量模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
        KImage2Data image2Out;
        image2Out.bind(mKPointTolineDistModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case MeasureType_PointTopointDist:
    {
        KPointTopointDistModule mKPointTopointDistModule;
        mKPointTopointDistModule.getParamData("image2In")->bind(&image2In);
        start = std::chrono::high_resolution_clock::now();
        mKPointTopointDistModule.setInputPoint(cv::Point2d(477, 250), cv::Point2d(120, 210));
        mKPointTopointDistModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "点点距离测量模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
        KImage2Data image2Out;
        image2Out.bind(mKPointTopointDistModule.getResultData("image2Out"));
        KDouble distance;
        distance.bind(mKPointTopointDistModule.getResultData("distance"));
        std::cout << "distance = " << distance.dValue() << std::endl;
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case MeasureType_LineTolineDist:
    {
        KLineIntersectionModule mKLineIntersectionModule;
        mKLineIntersectionModule.getParamData("image2In")->bind(&image2In);
        start = std::chrono::high_resolution_clock::now();
        mKLineIntersectionModule.setInputLine(cv::Point2d(256, 568), cv::Point2d(548, 153), cv::Point2d(760, 249), cv::Point2d(410, 713));
        mKLineIntersectionModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "线线距离测量模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
        KImage2Data image2Out;
        image2Out.bind(mKLineIntersectionModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());

        KDouble distance, angle;
        distance.bind(mKLineIntersectionModule.getResultData("distance"));
        std::cout << "distance = " << distance.dValue() << std::endl;
        angle.bind(mKLineIntersectionModule.getResultData("angle"));
        std::cout << "angle = " << angle.dValue() << std::endl;
    }
    break;
    case MeasureType_Brightness:
    {
        KBrightnessDetectModule mKBrightnessDetectModule;
        mKBrightnessDetectModule.getParamData("image2In")->bind(&image2In);
        KRectData roi;
        roi.setRect(cv::Rect(330, 222, 452, 443));
        mKBrightnessDetectModule.getParamData("roi")->bind(&roi);
        start = std::chrono::high_resolution_clock::now();
        mKBrightnessDetectModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "亮度测量模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mKBrightnessDetectModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());

        KDouble minGrayPixel, maxGrayPixel, avgGrayPixel, standGrayPixel, contrastGrayPixel;
        minGrayPixel.bind(mKBrightnessDetectModule.getResultData("minGrayPixel"));
        maxGrayPixel.bind(mKBrightnessDetectModule.getResultData("maxGrayPixel"));
        avgGrayPixel.bind(mKBrightnessDetectModule.getResultData("avgGrayPixel"));
        standGrayPixel.bind(mKBrightnessDetectModule.getResultData("standGrayPixel"));
        contrastGrayPixel.bind(mKBrightnessDetectModule.getResultData("contrastGrayPixel"));
        std::cout << "minGrayPixel = " << minGrayPixel.dValue() << std::endl;
        std::cout << "maxGrayPixel = " << maxGrayPixel.dValue() << std::endl;
        std::cout << "avgGrayPixel = " << avgGrayPixel.dValue() << std::endl;
        std::cout << "standGrayPixel = " << standGrayPixel.dValue() << std::endl;
        std::cout << "contrastGrayPixel = " << contrastGrayPixel.dValue() << std::endl;
    }
    break;
    case MeasureType_LineEdge:
    {
        KEdgeDetectLineModule mKEdgeDetectLineModule;
        mKEdgeDetectLineModule.getParamData("image2In")->bind(&image2In);
        KRectData roiRect;
        roiRect.setRect(cv::Rect(350, 100, 500, 100));
        mKEdgeDetectLineModule.getParamData("roiRect")->bind(&roiRect);

        KInt edgeDirection, edgePolarity, threshold, interval, detectThreshold;
        edgeDirection.setValue(0);
        edgePolarity.setValue(1);
        threshold.setValue(50);
        interval.setValue(5);
        detectThreshold.setValue(5);
        mKEdgeDetectLineModule.getParamData("edgeDirection")->bind(&edgeDirection);
        mKEdgeDetectLineModule.getParamData("edgePolarity")->bind(&edgePolarity);
        mKEdgeDetectLineModule.getParamData("threshold")->bind(&threshold);
        mKEdgeDetectLineModule.getParamData("interval")->bind(&interval);
        mKEdgeDetectLineModule.getParamData("detectThreshold")->bind(&detectThreshold);

        start = std::chrono::high_resolution_clock::now();
        mKEdgeDetectLineModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "直线边缘测量检测模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
        KImage2Data image2Out;
        image2Out.bind(mKEdgeDetectLineModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case MeasureType_CircleEdge:
    {
        KEdgeDetectCircleModule mKEdgeDetectCircleModule;
        mKEdgeDetectCircleModule.getParamData("image2In")->bind(&image2In);
        KRectData roiRect;
        roiRect.setRect(cv::Rect(0, 0, img.cols, img.rows));
        mKEdgeDetectCircleModule.getParamData("roiRect")->bind(&roiRect);

        KInt threshold, thresholdType;
        threshold.setValue(120);
        thresholdType.setValue(1);
        mKEdgeDetectCircleModule.getParamData("threshold")->bind(&threshold);
        mKEdgeDetectCircleModule.getParamData("thresholdType")->bind(&thresholdType);

        start = std::chrono::high_resolution_clock::now();
        mKEdgeDetectCircleModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "圆边缘测量检测模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mKEdgeDetectCircleModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    default:
    {
    }
    break;
    }
    return 0;
}