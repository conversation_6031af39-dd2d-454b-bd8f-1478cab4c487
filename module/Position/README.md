# KPositionModule 定位模块

该模块主要包含定位算子，包括模板匹配、位置修正、Blob分析、圆查找、直线查找和字符定位等功能。

## 模块概述

### 模板匹配模块 (KMatchModule)
- 使用**模板匹配**技术在输入图像中查找与模板图像最相似的区域
- 支持多种匹配方法（默认使用归一化相关系数法TM_CCOEFF_NORMED）
- 提供旋转匹配功能，可在不同角度下查找目标
- 支持图像金字塔下采样，提高匹配效率
- 可视化匹配结果，绘制矩形框或旋转框
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "downSampleNum": 下采样倍数
  - "rotateEnable": 是否启用旋转匹配
  - "matchMethod": 匹配方法
  - "angleStep": 旋转角度步长
  - "minAngle": 最小旋转角度
  - "maxAngle": 最大旋转角度
- 输出参数:
  - "image2Out": 输出图像（带有标记的结果）
  - "maxMatchscore": 最大匹配得分
  - "bestAngle": 最佳匹配角度（仅当rotateEnable为true时有效）

### 位置修正模块 (PositionCorrectKModule)
- 实现坐标变换和尺度缩放
- 支持单个ROI或多个ROI的位置修正
- 通过左上角坐标(_leftTopX, _leftTopY)和缩放比例(_xScale, _yScale)进行变换
- 可选择性启用单矩形修正(_enableMaxRect)或多矩形修正(_enableAllRect)
#### 参数配置


### Blob分析模块 (KBlobModule)
- 对二值图进行连通区域分析
- 提取轮廓信息和外接矩形
- 支持根据面积(_minArea)、宽度(_minWidth)、高度(_minHeight)等条件筛选目标
- 可找出最大区域或多个符合条件的区域
- 支持指定分析的分割结果索引(_segmentResultIndex)和阈值类型(_thresholdType)
- 输出轮廓信息、矩形框和最大轮廓索引

### 圆查找模块 (KFindCircleModule)
- 在图像中检测并定位圆形目标
- 通过轮廓提取和最小包围圆算法实现
- 自动选择面积最大的轮廓进行圆拟合
- 在输出图像上绘制检测到的圆形

### 直线查找模块 (KFindLineModule)
- 在指定ROI区域内检测并拟合直线
- 使用Canny算子提取边缘点
- 支持多种拟合方法：
  - 最小二乘法(DIST_L2)
  - HUBER鲁棒拟合
  - RANSAC随机抽样一致性算法
- 参数控制:
  - 边缘极性(_edgePolarType)
  - 二值化阈值(_binaryThreshold)
  - 最小点数(_minPoints)
  - 拟合方法(_fitMethod)
- 输出直线角度(_angle)和拟合结果(_ret)
- 可视化显示边缘点和拟合直线

### 字符定位模块 (KCharPositionDLModule)
- 使用深度学习模型检测字符区域
- 支持AI推理和后处理
- 输出字符区域的矩形框 

## 使用说明

所有模块均继承自KModule基类，通过REGISTER_MODULE宏进行注册。各模块遵循统一的接口规范：

1. 通过params()方法注册输入参数
2. 通过result()方法注册输出结果
3. 实现run()方法作为主要执行入口
4. 通过enable()判断模块是否启用
5. 返回值KCV_OK表示成功，其他值表示不同类型的错误

## 技术特性

- 基于OpenCV实现核心图像处理算法
- 模块化设计，支持灵活组合和扩展
- 参数化配置，便于调整和优化
- 支持ROI区域处理，提高处理效率
- 自动轮廓排序和筛选功能

## 应用场景

- 工业视觉检测与定位
- 产品质量控制与缺陷检测
- 视觉引导与测量
- 装配位置验证
- 形状识别与尺寸测量
