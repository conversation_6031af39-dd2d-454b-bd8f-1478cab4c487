# KColorProcessModule 颜色处理模块

该模块主要包含颜色处理功能，包括颜色空间转换、颜色测量和颜色提取等。

## 模块概述

### 颜色空间转换模块 (KImageColorCvtModule)
- RGB到灰度图转换
  - 支持通用转换
  - 支持单通道提取（R、G或B通道）
- RGB到HSV转换，并分离H、S、V三个通道
- RGB到HUV转换（自定义色彩空间）
  - 计算色调(H)
  - 计算均匀度(U)
  - 计算亮度值(V)
#### 参数配置
- 输入参数:
  - "convterType": 转换类型，0: RGB到灰度图转换，1: RGB到HSV转换，2: RGB到HSI转换，3: RGB到YUV转换
  - "rgb2graytype": 灰度图转换类型，0: 通用转换，1: 红色通道，2: 绿色通道，3: 蓝色通道
  - "image2In": 输入图像
- 输出参数:
  - "image2Out": 输出图像
  - "image2OutChannel1": 输出图像1
  - "image2OutChannel2": 输出图像2
  - "image2OutChannel3": 输出图像3

### 颜色测量模块 (KImageColorMeasurementModule)
- 计算每个通道的统计特征：
  - 最大值和最小值
  - 均值和标准差
- 生成每个通道的颜色直方图
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
- 输出参数:
  - "channelMaxValue0": 通道0最大值
  - "channelMaxValue1": 通道1最大值
  - "channelMaxValue2": 通道2最大值
  - "channelMinValue0": 通道0最小值
  - "channelMinValue1": 通道1最小值
  - "channelMinValue2": 通道2最小值
  - "channelMeanValue0": 通道0均值
  - "channelMeanValue1": 通道1均值
  - "channelMeanValue2": 通道2均值
  - "channelStdValue0": 通道0标准差
  - "channelStdValue1": 通道1标准差
  - "channelStdValue2": 通道2标准差
  - "image2OutHist0": 输出直方图0（通道0）
  - "image2OutHist1": 输出直方图1（通道1）
  - "image2OutHist2": 输出直方图2（通道2）
### 颜色提取模块 (KImageColorExtractionModule)
- 根据颜色范围提取图像中特定颜色区域
- 可以设置三个通道的最大值和最小值范围
- 输出提取区域的面积和二值掩码图像
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "channelMaxValue0": 通道0最大值
  - "channelMaxValue1": 通道1最大值
  - "channelMaxValue2": 通道2最大值
  - "channelMinValue0": 通道0最小值
  - "channelMinValue1": 通道1最小值
  - "channelMinValue2": 通道2最小值
- 输出参数:
  - "ColorRegionArea": 颜色区域面积
  - "image2Out": 输出图像

## 使用说明

所有模块均继承自KModule基类，通过REGISTER_MODULE宏进行注册。各模块遵循统一的接口规范：

1. 通过参数系统定义输入输出
2. 实现run()方法作为主要执行入口
3. 通过enable()判断模块是否启用
4. 返回值0表示成功，其他值表示不同类型的错误

## 技术特性

- 基于OpenCV实现核心图像处理功能
- 模块化设计，支持灵活组合和扩展
- 参数化配置，便于调整和优化
- 支持多种图像格式和颜色空间

## 应用场景

- 颜色分析与特征提取
- 为深度学习和机器视觉算法提供输入数据准备
- 工业视觉检测的基础处理
