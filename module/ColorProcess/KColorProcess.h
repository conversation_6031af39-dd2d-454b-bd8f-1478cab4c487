
#pragma once
#include "KModuleDefine.h"

#pragma region 颜色空间转换模块
class KImageColorCvtModule : public KModule
{
public:
    KImageColorCvtModule();
    int run() override;
    int rgb2gray();
    int rgb2hsv();
    int rgb2hsi();
    int rgb2yuv();

private:
    KInt _convterType;
    KImage2Data _image2In;
    KImage2Data _image2Out;
    KInt _rgb2graytype;
    KImage2Data _image2OutChannel1;
    KImage2Data _image2OutChannel2;
    KImage2Data _image2OutChannel3;
};
#pragma endregion

#pragma region 颜色测量模块
class KImageColorMeasurementModule : public KModule
{
public:
    KImageColorMeasurementModule();
    int run() override;

private:
    KInt _channelMaxValue[3];
    KInt _channelMinValue[3];
    KDouble _channelMeanValue[3];
    KDouble _channelStdValue[3];
    KImage2Data _image2In;
    KImage2Data _image2OutHist[3];
};
#pragma endregion

#pragma region 颜色提取模块
class KImageColorExtractionModule : public KModule
{
public:
    KImageColorExtractionModule();
    int run() override;

private:
    KInt _channelMaxValue[3];
    KInt _channelMinValue[3];

    KImage2Data _image2In;

    KInt _ColorRegionArea;
    KImage2Data _image2Out;
};
#pragma endregion
