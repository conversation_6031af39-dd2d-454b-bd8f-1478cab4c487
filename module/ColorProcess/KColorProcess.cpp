#include "KColorProcess.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/KImageMarker.h"
#include "KFactory.h"

#pragma region 颜色空间转换模块
KImageColorCvtModule::KImageColorCvtModule()
    : KModule()
{
    /*输入参数*/
    params().add("convterType", &_convterType);
    params().add("image2In", &_image2In);
    params().add("rgb2graytype", &_rgb2graytype);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("image2OutChannel1", &_image2OutChannel1);
    result().add("image2OutChannel2", &_image2OutChannel2);
    result().add("image2OutChannel3", &_image2OutChannel3);
}

REGISTER_MODULE(KImageColorCvtModule);
int KImageColorCvtModule::rgb2gray()
{
    if (_rgb2graytype.iValue() == 0) // 通用转换
    {
        cv::cvtColor(_image2In.KImage2Ptr()->src(), _image2Out.KImage2Ptr()->src(), cv::COLOR_RGB2GRAY);
    }
    else
    {
        std::vector<cv::Mat> channels;
        cv::split(_image2In.KImage2Ptr()->src(), channels);
        cv::Mat red_channel = channels[0]; // 默认系统进来是RGB顺序
        cv::Mat green_channel = channels[1];
        cv::Mat blue_channel = channels[2];

        switch (_rgb2graytype.iValue())
        {
        case 1: // rgb2gray 红色通道
            _image2Out.KImage2Ptr()->src() = red_channel;
            break;
        case 2: // rgb2gray 绿色通道
            _image2Out.KImage2Ptr()->src() = green_channel;
            break;
        case 3: // rgb2gray 蓝色通道
            _image2Out.KImage2Ptr()->src() = blue_channel;
            break;
        default:
            _image2Out.Image2().src() = _image2In.Image2().src().clone();
            break;
        }
    }
    return 0;
}

int KImageColorCvtModule::rgb2hsv()
{

    cv::cvtColor(_image2In.KImage2Ptr()->src(), _image2Out.KImage2Ptr()->src(), cv::COLOR_RGB2HSV);

    std::vector<cv::Mat> channels;
    split(_image2Out.Image2().src(), channels);
    channels[0].copyTo(_image2OutChannel1.KImage2Ptr()->src()); // 色相
    channels[1].copyTo(_image2OutChannel2.KImage2Ptr()->src()); // 饱和度
    channels[2].copyTo(_image2OutChannel3.KImage2Ptr()->src()); // 明度

    return 0;
}

int KImageColorCvtModule::rgb2hsi()
{
    CV_Assert(_image2In.Image2().src().type() == CV_8UC3);                              // 确保输入是 8UC3 格式
    cv::Mat hsi(_image2In.Image2().src().rows, _image2In.Image2().src().cols, CV_8UC3); // 输出 HSI 图像
    cv::Mat r, g, b;
    std::vector<cv::Mat> channels;
    split(_image2In.Image2().src(), channels);
    r = channels[0];
    g = channels[1];
    b = channels[2];
    for (int i = 0; i < _image2In.Image2().src().rows; i++)
    {
        for (int j = 0; j < _image2In.Image2().src().cols; j++)
        {
            float r_val = r.at<uchar>(i, j) / 255.0f;
            float g_val = g.at<uchar>(i, j) / 255.0f;
            float b_val = b.at<uchar>(i, j) / 255.0f;

            float intensity = (r_val + g_val + b_val) / 3.0f;
            float saturation = 0;
            if (intensity > 1e-6) // 避免除以零
            {
                saturation = 1.0f - (std::min({r_val, g_val, b_val}) / intensity);
            }
            float hue = 0;
            if (saturation > 1e-6) // 只有当饱和度不为0时，色相才有意义
            {
                float numerator = 0.5f * ((r_val - g_val) + (r_val - b_val));
                float denominator = sqrt(pow(r_val - g_val, 2) + (r_val - b_val) * (g_val - b_val));

                if (denominator > 1e-6) // 避免除以零
                {
                    float theta = acos(numerator / denominator); // 结果是弧度
                    if (b_val > g_val)
                    {
                        hue = (2 * CV_PI - theta) * (180.0 / CV_PI); // 转换为角度 [0, 360]
                    }
                    else
                    {
                        hue = theta * (180.0 / CV_PI); // 转换为角度 [0, 360]
                    }
                }
            }
            hsi.at<cv::Vec3b>(i, j)[0] = static_cast<uchar>(hue / 2.0f);
            hsi.at<cv::Vec3b>(i, j)[1] = static_cast<uchar>(saturation * 255.0f);
            hsi.at<cv::Vec3b>(i, j)[2] = static_cast<uchar>(intensity * 255.0f);
        }
    }
    hsi.copyTo(_image2Out.Image2().src());
    split(hsi, channels);
    channels[0].copyTo(_image2OutChannel1.KImage2Ptr()->src());
    channels[1].copyTo(_image2OutChannel2.KImage2Ptr()->src());
    channels[2].copyTo(_image2OutChannel3.KImage2Ptr()->src());
    return 0;
}

int KImageColorCvtModule::rgb2yuv()
{
    cv::cvtColor(_image2In.KImage2Ptr()->src(), _image2Out.KImage2Ptr()->src(), cv::COLOR_RGB2YUV);

    std::vector<cv::Mat> channels;
    split(_image2Out.Image2().src(), channels);
    channels[0].copyTo(_image2OutChannel1.KImage2Ptr()->src());
    channels[1].copyTo(_image2OutChannel2.KImage2Ptr()->src());
    channels[2].copyTo(_image2OutChannel3.KImage2Ptr()->src());

    return 0;
}

int KImageColorCvtModule::run()
{
    if (!enable())
        return KCV_OK;

    switch (_convterType.iValue())
    {
    case 0: // rgb2gray
        rgb2gray();
        break;
    case 1: // rgb2hsv
        rgb2hsv();
        break;
    case 2: // rgb2hsi
        rgb2hsi();
        break;
    case 3: // rgb2yuv
        rgb2yuv();
        break;
    default:
        _image2Out.Image2().src() = _image2In.Image2().src().clone();
        break;
    }
    return KCV_OK;
}
#pragma endregion

#pragma region 颜色测量模块
KImageColorMeasurementModule::KImageColorMeasurementModule()
    : KModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);

    /*输出参数*/
    result().add("channelMaxValue0", &_channelMaxValue[0]);
    result().add("channelMaxValue1", &_channelMaxValue[1]);
    result().add("channelMaxValue2", &_channelMaxValue[2]);
    result().add("channelMinValue0", &_channelMinValue[0]);
    result().add("channelMinValue1", &_channelMinValue[1]);
    result().add("channelMinValue2", &_channelMinValue[2]);

    result().add("channelMeanValue0", &_channelMeanValue[0]);
    result().add("channelMeanValue1", &_channelMeanValue[1]);
    result().add("channelMeanValue2", &_channelMeanValue[2]);

    result().add("channelStdValue0", &_channelStdValue[0]);
    result().add("channelStdValue1", &_channelStdValue[1]);
    result().add("channelStdValue2", &_channelStdValue[2]);

    result().add("image2OutHist0", &_image2OutHist[0]);
    result().add("image2OutHist1", &_image2OutHist[1]);
    result().add("image2OutHist2", &_image2OutHist[2]);
}

REGISTER_MODULE(KImageColorMeasurementModule);

int KImageColorMeasurementModule::run()
{
    if (!enable())
        return KCV_OK;

    std::vector<cv::Mat> channels;
    cv::split(_image2In.Image2().src(), channels); // 分离通道
    // 计算每个通道最小值和最大值
    for (int i = 0; i < channels.size(); ++i)
    {
        double minVal, maxVal;
        cv::Point minLoc, maxLoc;
        cv::minMaxLoc(channels[i], &minVal, &maxVal, &minLoc, &maxLoc);

        _channelMinValue[i].setValue(minVal);
        _channelMaxValue[i].setValue(maxVal);
    }
    // 计算每个通道的均值和均方差

    cv::Scalar mean, stddev;
    cv::meanStdDev(_image2In.Image2().src(), mean, stddev);
    for (int i = 0; i < channels.size(); ++i)
    {
        _channelMeanValue[i].setValue(mean[i]);
        _channelStdValue[i].setValue(stddev[i]);
    }

    // 计算单通道直方图
    cv::Mat bHist, gHist, rHist;
    int histSize = 256;
    float range[] = {0, 256};
    const float *histRange = {range};
    cv::calcHist(&channels[0], 1, 0, cv::Mat(), bHist, 1, &histSize, &histRange);
    cv::calcHist(&channels[1], 1, 0, cv::Mat(), gHist, 1, &histSize, &histRange);
    cv::calcHist(&channels[2], 1, 0, cv::Mat(), rHist, 1, &histSize, &histRange);
    bHist.copyTo(_image2OutHist[0].Image2().src());
    gHist.copyTo(_image2OutHist[1].Image2().src());
    rHist.copyTo(_image2OutHist[2].Image2().src());

    return KCV_OK;
}
#pragma endregion

#pragma region 颜色提取模块
KImageColorExtractionModule::KImageColorExtractionModule()
    : KModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("channelMaxValue0", &_channelMaxValue[0]);
    params().add("channelMaxValue1", &_channelMaxValue[1]);
    params().add("channelMaxValue2", &_channelMaxValue[2]);
    params().add("channelMinValue0", &_channelMinValue[0]);
    params().add("channelMinValue1", &_channelMinValue[1]);
    params().add("channelMinValue2", &_channelMinValue[2]);

    /*输出参数*/
    result().add("ColorRegionArea", &_ColorRegionArea);
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KImageColorExtractionModule);

int KImageColorExtractionModule::run()
{
    if (!enable())
        return KCV_OK;

    std::cout << "KImageColorExtractionModule " << __FUNCTION__ << std::endl;

    std::vector<cv::Mat> channels;
    std::vector<cv::Mat> masks;
    cv::split(_image2In.Image2().src(), channels); // 分离通道

    for (int i = 0; i < channels.size(); ++i)
    {
        double minVal, maxVal;

        cv::Mat mask;
        cv::inRange(channels[i], _channelMinValue[i].iValue(), _channelMaxValue[i].iValue(), mask);
        masks.push_back(mask);
    }

    for (int i = 1; i < masks.size(); ++i)
    {
        masks[i] = masks[i - 1] & masks[i];
    }
    masks[masks.size() - 1].copyTo(_image2Out.Image2().src());

    _ColorRegionArea.setValue(cv::countNonZero(masks[masks.size() - 1]));
    return KCV_OK;
}
#pragma endregion