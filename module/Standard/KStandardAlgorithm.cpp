﻿#include "KStandardAlgorithm.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/KImageMarker.h"
#include "KFactory.h"

#pragma region 分割模块
KSegmentModule::KSegmentModule()
    : KModule()
{
    // params().add("inferData", &_inferData);
    // params().add("finalRectIn", &_finalRectIn);
    // params().add("binaryImages", &_binaryImages);
    // params().add("finalRectOut", &_finalRectOut);
    // params().add("sensor", &_sensor);
}

REGISTER_MODULE(KSegmentModule);

int KSegmentModule::run()
{
    if (!enable())
        return KCV_OK;
    // ai参数是否设置
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    if (!_smartAi.smartAiData())
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !_smartAi.smartAiData() return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    KAIEngineInferEnity *pEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *benity = dynamic_cast<KBitlandAIEngineInferEnity *>(pEnity);
    if (!benity)
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());

        return KCV_OK;
    }
    // 是否为首次推理
    if (_isFirstInfer.bValue()) // first module, clear data first
    {
        _bmOutImage.setBmImage(nullptr);
    }
    bool rectValid = _finalRectIn.width() > 0 && _finalRectIn.height() > 0; // 判断roi是否有效
    bm_image *bmImage = (bm_image *)_bmInImage.bmImagePtr();                // 获取传递的bmimage指针
    if (rectValid && _cutLocate.bValue())                                   // 切图推理
    {
        if (bmImage)
            benity->infer(bmImage, _finalRectIn.rect());
        else
        {
            benity->infer(_image2.KImage2Ptr(), _finalRectIn.rect());
        }
    }
    else
    {
        if (bmImage)
            benity->infer(bmImage);
        else
        {
            benity->infer(_image2.KImage2Ptr());
            _bmOutImage.setBmImage((void *)benity->bmImagePtr());
        }
    }
    // 取推理结果
    KBitlandAIEngineInferData *rltData = (KBitlandAIEngineInferData *)(benity->data());
    int outsize = rltData->OutputDim[0].Channel;
    int outwidth = rltData->OutputDim[0].Width, outheight = rltData->OutputDim[0].Height;
    if (outsize == 0)
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 模型类别输出错误 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    else
    {
        _channelNum.setValue(outsize);
        _outputWidth.setValue(outwidth);
        _outputHeight.setValue(outheight);
    }
    _binaryImages.resize(outsize);
    double wbilv, hbilv;
    if (_cutLocate.bValue())
    {
        wbilv = 1.0 * _finalRectIn.rect().width / outwidth;
        hbilv = 1.0 * _finalRectIn.rect().height / outheight;
    }
    else
    {
        wbilv = 1.0 * _image2.Image2().width() / outwidth;
        hbilv = 1.0 * _image2.Image2().height() / outheight;
    }
    _scaleWidth.setValue(wbilv);
    _scaleHeight.setValue(hbilv);
    if (_locateSingleObject.bValue())
    {
        cv::Mat binary = cv::Mat(outheight, outwidth, CV_32FC1, (float *)rltData->rltdata) > 0.5;
        KImage2 res(binary);
        KContours kcons = res.contours();
        if (kcons.count() == 0)
        {
            return Algo_Return_Segment;
        }
        int maxidx = kcons.maxArea();
        KRect rc = cv::boundingRect(kcons.coutours[maxidx]);
        _finalRectOut.rect().x = rc.x * wbilv + _finalRectIn.rect().x;
        _finalRectOut.rect().y = rc.y * hbilv + _finalRectIn.rect().y;
        _finalRectOut.rect().width = rc.width * wbilv;
        _finalRectOut.rect().height = rc.height * hbilv;
    }
    else
    {
        for (int i = 0; i < outsize; i++)
        {
            cv::Mat binary = cv::Mat(outheight, outwidth, CV_32FC1, (float *)rltData->rltdata + i * outheight * outwidth) > 0.5;

            if (_resizeToOrigin.bValue())
            {
                cv::resize(binary, binary, cv::Size(binary.cols * wbilv, binary.rows * hbilv));
            }
            _binaryImages.addImage2Data(i, binary);
        }
    }
    return KCV_OK;
}

#pragma endregion

#pragma region 目标检测模块
KObjectDetectModule::KObjectDetectModule()
    : KModule()
{
}

REGISTER_MODULE(KObjectDetectModule);

struct BOX
{
    KRectData rc;
    double prob;
};

bool bbSortByProb(BOX &b1, BOX &b2)
{
    return b1.prob > b2.prob;
}

bool sortRectByArea(KRect &r1, KRect &r2)
{
    return r1.width * r1.height > r2.width * r2.height;
}

bool sortRectByX(KRect &r1, KRect &r2)
{
    return r1.x < r2.x;
}

bool sortRectByY(KRect &r1, KRect &r2)
{
    return r1.y < r2.y;
}

int bInBox(KRect1DData &rcs, KRectData &rc)
{
    int bIn = 0, num = rcs.rect1D().size();
    for (int i = 0; i < num; i++)
        if ((rcs.rect1D()[i] & rc.rect()).area() * 1.0 / (rcs.rect1D()[i] | rc.rect()).area() > 0.5)
        {
            bIn = 1;
            break;
        }
    return bIn;
}

int KObjectDetectModule::run()
{
    if (!enable())
        return KCV_OK;
    // ai参数是否设置
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    if (!_smartAi.smartAiData())
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !_smartAi.smartAiData() return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    KAIEngineInferEnity *pEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *benity = dynamic_cast<KBitlandAIEngineInferEnity *>(pEnity);
    if (!benity)
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());

        return KCV_OK;
    }
    // 是否为首次推理
    if (_isFirstInfer.bValue()) // first module, clear data first
    {
        _finalRectIn.rect() = KRect();
        _bmOutImage.setBmImage(nullptr);
    }

    bool rectValid = _finalRectIn.width() > 0 && _finalRectIn.height() > 0; // 判断roi是否有效
    bm_image *bmImage = (bm_image *)_bmInImage.bmImagePtr();                // 获取传递的bmimage指针
    if (rectValid && _cutLocate.bValue())                                   // 切图推理
    {
        if (bmImage)
            benity->infer(bmImage, _finalRectIn.rect());
        else
        {
            benity->infer(_image2.KImage2Ptr(), _finalRectIn.rect());
        }
    }
    else
    {
        if (bmImage)
            benity->infer(bmImage);
        else
        {
            benity->infer(_image2.KImage2Ptr());
            _bmOutImage.setBmImage((void *)benity->bmImagePtr());
        }
    }
    // 取推理结果

    KBitlandAIEngineInferData *data = (KBitlandAIEngineInferData *)benity->data();
    if (!data)
        return Algo_Return_Segment;
    float wbilv, hbilv;
    if (rectValid && _cutLocate.bValue())
    {
        wbilv = 1.0 * _finalRectIn.width() / data->InputDim[0].Width;
        hbilv = 1.0 * _finalRectIn.height() / data->InputDim[0].Height;
    }
    else
    {
        wbilv = 1.0 * _image2.Image2().width() / data->InputDim[0].Width;
        hbilv = 1.0 * _image2.Image2().height() / data->InputDim[0].Height;
    }
    int w = data->OutputDim[0].Width, h = data->OutputDim[0].Height;
    float *p = (float *)data->rltdata;
    BOX bb;
    std::vector<BOX> bbs;
    for (int i = 0; i < h; i++, p += 5)
    {
        bb.prob = p[w - 1];
        if (bb.prob > 1.0 * _sensor.iValue() / 100.0f)
        {
            int x1 = max(0, int(p[0])) * wbilv, x2 = max(0, int(p[2])) * wbilv;
            int y1 = max(0, int(p[1])) * hbilv, y2 = max(0, int(p[3])) * hbilv;
            bb.rc = KRectData(cv::Point(x1, y1), cv::Point(x2, y2));
            // 过滤条件
            if (_enableLimitWidth.bValue() && bb.rc.width() < _minWidth.iValue())
            {
                continue;
            }
            if (_enableLimitHeight.bValue() && bb.rc.height() < _minHeight.iValue())
            {
                continue;
            }
            bbs.push_back(bb);
        }
    }
    sort(bbs.begin(), bbs.end(), bbSortByProb);
    for (int i = 0; i < bbs.size(); i++)
    {
        if (!bInBox(_kRects, bbs[i].rc))
        {
            _kRects.push_back(bbs[i].rc);
        }
    }
    if (_kRects.rect1D().size() == 0)
    {
        return Algo_Return_Segment;
    }
    else
    {
        sort(_kRects.rect1D().begin(), _kRects.rect1D().end(), sortRectByArea);
        _finalRectOut = _kRects.rect1D()[0];
    }

    return 0;
}

#pragma endregion

#pragma region 分类模块
KClassifyModule::KClassifyModule()
    : KModule()
{
    // params().add("targetType", &_targetType);
    // params().add("image2", &_image2);
    // params().add("inferData", &_inferData);
    // params().add("imageMarker", &_imageMarker);
}

REGISTER_MODULE(KClassifyModule);

int KClassifyModule::run()
{
    if (!enable())
        return KCV_OK;
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    if (!_smartAi.smartAiData())
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !_smartAi.smartAiData() return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    KAIEngineInferEnity *pEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *benity = dynamic_cast<KBitlandAIEngineInferEnity *>(pEnity);
    if (!benity)
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());

        return KCV_OK;
    }
    // 是否为首次推理
    if (_isFirstInfer.bValue()) // first module, clear data first
    {
        _bmOutImage.setBmImage(nullptr);
    }

    bool rectValid = _finalRectIn.width() > 0 && _finalRectIn.height() > 0; // 判断roi是否有效
    if (_cutLocate.bValue() && !rectValid)                                  // 获取的roi区域有错误
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 输入切图区域错误 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    bm_image *bmImage = (bm_image *)_bmInImage.bmImagePtr(); // 获取传递的bmimage指针
    if (_cutLocate.bValue())                                 // 切图推理
    {
        if (bmImage)
            benity->infer(bmImage, _finalRectIn.rect());
        else
        {
            benity->infer(_image2.KImage2Ptr(), _finalRectIn.rect());
        }
    }
    else
    {
        if (bmImage)
            benity->infer(bmImage);
        else
        {
            benity->infer(_image2.KImage2Ptr());
            _bmOutImage.setBmImage((void *)benity->bmImagePtr());
        }
    }
    // 取推理结果
    KBitlandAIEngineInferData *data = (KBitlandAIEngineInferData *)(benity->data());
    int size = data->OutputDim.begin()->Channel;
    if (size < 1)
        return Algo_Return_ClassifyType;
    _classNumber.setValue(size);
    std::vector<float> outData;
    outData.resize(size, 0);
    for (int i = 0; i < size; ++i)
        outData[i] = *(((float *)data->rltdata) + i);
    _scores = outData;
    int iclass = std::max_element(outData.begin(), outData.end()) - outData.begin();
    _maxClassIndex.setValue(iclass);
    int minclass = std::min_element(outData.begin(), outData.end()) - outData.begin();
    _minClassIndex.setValue(minclass);
    if (marker && _enableDraw.bValue())
    {
        char text[256];
        sprintf(text, "lb : %d, score : %.2f", iclass, outData[iclass]);
        KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
        marker->addText(text, KPoint(_image2.Image2().width() / 2 - 50, 100), KCV_BLUE);
    }

    return KCV_OK;
}
#pragma endregion

#pragma region 异常检测模块
KAbnormalModule::KAbnormalModule()
    : KModule()
{

    params().add("threshold", &_threshold);
    params().add("finalRect", &_finalRect);
    params().add("image2", &_image2);
    params().add("inferData", &_inferData);
    params().add("imageMarker", &_imageMarker);
}

REGISTER_MODULE(KAbnormalModule);

int KAbnormalModule::run()
{
    if (!enable())
        return KCV_OK;
    KBitlandAIEngineInferData *dataEnity = (KBitlandAIEngineInferData *)(_inferData.inferData());
    if (!dataEnity)
    {
        printf("KAbnormalModule::run() !dataEnity return \n");
        return KCV_OK;
    }
    cv::Mat outPutImg(dataEnity->OutputDim[0].Height, dataEnity->OutputDim[0].Width, CV_32FC1, (float *)dataEnity->rltdata);
    double minv, maxv;
    cv::Point ptmin, ptmax;
    cv::minMaxLoc(outPutImg, &minv, &maxv, &ptmin, &ptmax);
    std::string str = std::to_string(maxv).substr(0, 4);
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    marker->addText(str.substr(0, 5).c_str(), cv::Point(10, 300), KCV_GREEN);
    cv::Mat defectImgbinary = outPutImg > _threshold.dValue();
    KImage2 dstDefectImage2(defectImgbinary);
    KRegions &&defectRegions = dstDefectImage2.regions();
    KRect1D &&defectRects = defectRegions.rects();

    bool rectValid = _finalRect.width() > 0 && _finalRect.height() > 0;

    bool isOk = true;
    float resizePara_H = 1.0 * _finalRect.height() / dataEnity->OutputDim[0].Height;
    float resizePara_W = 1.0 * _finalRect.width() / dataEnity->OutputDim[0].Width;
    if (defectRects.size() > 0)
    {
        for (int i = 0; i < defectRects.size(); ++i)
        {

            KRect &rect = defectRects.at(i);
            rect.x = rect.x * resizePara_W + (rectValid ? _finalRect.rect().x : 0);
            rect.width = rect.width * resizePara_W;
            rect.y = rect.y * resizePara_H + (rectValid ? _finalRect.rect().y : 0);
            rect.height = rect.height * resizePara_H;
            if (isOk)
                isOk = false;
            //            printf("KAbnormalModule add rect: x = %d, y = %d, w = %d, h = %d \n", rect.x, rect.y,
            //                   rect.width, rect.height);
            marker->addRect(rect, 0xff0000);
        }
        if (!isOk)
        {
            marker->addText(("样品区域异常"), KPoint(10, 50), 0xFF0000);
            return Algo_Return_Abnormal;
        }
    }
    //    printf("KAbnormalModule expand rect x = %d, y = %d , w = %d, h = %d \n",
    //           _finalRect.rect().x, _finalRect.rect().y, _finalRect.width(), _finalRect.height());
    //    printf("KAbnormalModule 处理结束，即将返回ok\n");
    return KCV_OK;
}
#pragma endregion

#pragma region 缺陷检测模块
KDefectModule::KDefectModule()
    : KModule()
{
    // params().add("sensor", &_sensor);
    // params().add("inferDataIndex", &_inferDataIndex);
    // params().add("inferImages", &_inferImages);
    // params().add("inferData", &_inferData);
    // params().add("finalRect", &_finalRect);
    // params().add("image2", &_image2);
    // params().add("imageMarker", &_imageMarker);
    // _sensor.setValue(50);
}

REGISTER_MODULE(KDefectModule);

int KDefectModule::run()
{
    if (!enable())
        return KCV_OK;
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    if (!_smartAi.smartAiData())
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !_smartAi.smartAiData() return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    KAIEngineInferEnity *pEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *benity = dynamic_cast<KBitlandAIEngineInferEnity *>(pEnity);
    if (!benity)
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());

        return KCV_OK;
    }
    // 是否为首次推理
    if (_isFirstInfer.bValue()) // first module, clear data first
    {
        _bmOutImage.setBmImage(nullptr);
    }

    bool rectValid = _finalRectIn.width() > 0 && _finalRectIn.height() > 0; // 判断roi是否有效
    if (_cutLocate.bValue() && !rectValid)                                  // 获取的roi区域有错误
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 输入切图区域错误 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    bm_image *bmImage = (bm_image *)_bmInImage.bmImagePtr(); // 获取传递的bmimage指针
    if (_cutLocate.bValue())                                 // 切图推理
    {
        if (bmImage)
            benity->infer(bmImage, _finalRectIn.rect());
        else
        {
            benity->infer(_image2.KImage2Ptr(), _finalRectIn.rect());
        }
    }
    else
    {
        if (bmImage)
            benity->infer(bmImage);
        else
        {
            benity->infer(_image2.KImage2Ptr());
            _bmOutImage.setBmImage((void *)benity->bmImagePtr());
        }
    }
    // 取推理结果
    KBitlandAIEngineInferData *data = (KBitlandAIEngineInferData *)(benity->data());
    double wbilv, hbilv;
    if (_cutLocate.bValue()) // 切图推理
    {
        wbilv = 1.0 * _finalRectIn.width() / data->OutputDim[0].Width;
        hbilv = 1.0 * _finalRectIn.height() / data->OutputDim[0].Height;
    }
    else
    {
        wbilv = 1.0 * _image2.Image2().width() / data->OutputDim[0].Width;
        hbilv = 1.0 * _image2.Image2().height() / data->OutputDim[0].Height;
    }
    _xScale.setValue(wbilv);
    _yScale.setValue(hbilv);
    int outsize = data->OutputDim[0].Channel;
    _channelNum.setValue(outsize);
    for (int i = 0; i < outsize; i++)
    {
        cv::Mat binary = cv::Mat(data->OutputDim[0].Height, data->OutputDim[0].Width, CV_32FC1,
                                 (float *)data->rltdata + i * data->OutputDim[0].Height * data->OutputDim[0].Width) > 0.5;
        _binaryImages.addImage2Data(i, binary);
    }

    return KCV_OK;
}
#pragma endregion

#pragma region 缺陷后处理模块
KDefectPostModule::KDefectPostModule()
    : KModule()
{
}

REGISTER_MODULE(KDefectPostModule);

int KDefectPostModule::run()
{
    if (!enable())
        return KCV_OK;
    if (_defectIndex.iValue() > _channelNum.iValue())
    { // 设置类别数错误，超出类别总数
        return Algo_Return_Segment;
    }
    // 获取轮廓信息，定位框信息
    KImage2 img = _binaryImages.Image2Data(_defectIndex.iValue()).Image2();
    KContours kcons = img.contours();
    if (kcons.count() == 0)
    {
        return Algo_Return_Segment;
    }
    for (int i = 0; i < kcons.count(); i++)
    {
        cv::Rect rc = cv::boundingRect(kcons.coutours[i]);
        rc.x = rc.x * _xScale.dValue() + _finalRectIn.rect().x;
        rc.y = rc.y * _yScale.dValue() + _finalRectIn.rect().y;
        rc.width = rc.width * _xScale.dValue();
        rc.height = rc.height * _yScale.dValue();

        if (_enableLimitWidth.bValue() && rc.width < _minWidth.iValue())
        {
            continue;
        }
        if (_enableLimitHeight.bValue() && rc.height < _minHeight.iValue())
        {
            continue;
        }
        _KRectOut.push_back(rc);
    }
    if (_KRectOut.rect1D().size() > 0)
    {
        _isOk.setValue(false);
    }
    else
    {
        _isOk.setValue(true);
    }
    return 0;
}
#pragma endregion

#pragma region 脏污检测模块
KDirtyModule::KDirtyModule()
    : KModule()
{
    params().add("sensor", &_sensor);
    params().add("inferDataIndex", &_inferDataIndex);
    params().add("inferImages", &_inferImages);
    params().add("inferData", &_inferData);
    params().add("finalRect", &_finalRect);
    params().add("image2", &_image2);
    params().add("imageMarker", &_imageMarker);

    result().add("dirtyRects", &_dirtyRects);

    _sensor.setValue(50);
}

REGISTER_MODULE(KDirtyModule);

int KDirtyModule::run()
{
    if (!enable())
        return KCV_OK;
    _dirtyRects.rect1D().clear();

    bool rectValid = _finalRect.width() > 0 && _finalRect.height() > 0;
    KBitlandAIEngineInferData *rltData = (KBitlandAIEngineInferData *)(_inferData.inferData());
    float ih = 1.0 * _finalRect.height() / rltData->OutputDim[0].Height;
    float iw = 1.0 * _finalRect.width() / rltData->OutputDim[0].Width;
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    //    cv::imwrite("./KDirtyModule-pre.png", _inferImage.Image2().src());
    auto inferImage = _inferImages.Image2Data(_inferDataIndex.iValue());
    cv::Mat rlt = inferImage.Image2().src() > (_sensor.iValue() / 100.0f / rltData->output_scale);
    KImage2 resultImg(rlt);
    KRegions &&dirtyRegions = resultImg.regions();
    KRect1D &&rects = dirtyRegions.rects();
    //    cv::imwrite("./KDirtyModule.png", rlt);
    if (!rects.size())
    {
        //        printf("KDirtyModule dirty rect size = 0, return ok! \n");
        return KCV_OK; ///  detect is ok , need not to do next .
    }
    else
    {

        int drawHeidian = 0;
        char text[256];
        sprintf(text, "Dirty count : %d", static_cast<int>(rects.size()));
        marker->addText(text, KPoint(30, _finalRect.height() - 80), KCV_RED);
        //        printf("KDirtyModule dirty rect size = %d! \n", rects.size());
        for (KRect &rect : rects)
        {
            //            printf("KDirtyModule before add rect: x = %d, y = %d, w = %d, h = %d iw = %f, ih = %f"
            //                   ", _finalRect.height() = %d, _finalRect.width() = %d\n",
            //                   rect.x, rect.y,
            //                   rect.width, rect.height, iw, ih, _finalRect.height(), _finalRect.width());
            rect.x *= iw;
            rect.y *= ih;
            rect.width *= iw;
            rect.height *= ih;
            if (drawHeidian >= 10)
                break;
            KRect dirtyArea;
            int cx = (rect).x + (rect).width / 2;
            int cy = (rect).y + (rect).height / 2;
            dirtyArea.x = (cx - 64) > 0 ? (cx - 64) : 0;
            dirtyArea.y = (cy - 64) > 0 ? (cy - 64) : 0;
            dirtyArea.width = 128;
            dirtyArea.height = 128;
            if ((dirtyArea.x + dirtyArea.width) > _finalRect.width())
            {
                dirtyArea.x = _finalRect.width() - dirtyArea.width;
            }
            if ((dirtyArea.y + dirtyArea.height) > _finalRect.height())
            {
                dirtyArea.y = _finalRect.height() - dirtyArea.height;
            }
            dirtyArea.x += (rectValid ? _finalRect.rect().x : 0);
            dirtyArea.y += (rectValid ? _finalRect.rect().y : 0);
            marker->addRect(dirtyArea, KCV_RED, 2);

            //            printf("KDirtyModule add rect: x = %d, y = %d, w = %d, h = %d \n", dirtyArea.x, dirtyArea.y,
            //                   dirtyArea.width, dirtyArea.height);
            _dirtyRects.rect1D().emplace_back(dirtyArea);
        }
    }
    if (_dirtyRects.rect1D().size())
    {
        marker->addText("污点检出", KPoint(10, 50), KCV_RED);
        return Algo_Return_Dirty;
    }
    return KCV_OK;
}
#pragma endregion

#pragma region 脏污面积检测模块
KDirtyAreaModule::KDirtyAreaModule()
    : KModule()
{
}

REGISTER_MODULE(KDirtyAreaModule);

int KDirtyAreaModule::run()
{
    if (!enable())
    {
        printf("KDirtyAreaModule::run() !enable return!\n");
        return KCV_OK;
    }
    // ai参数是否设置
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    if (!_smartAi.smartAiData())
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !_smartAi.smartAiData() return, m id = %s\n", id().c_str());
        return KCV_OK;
    }
    KAIEngineInferEnity *pEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *benity = dynamic_cast<KBitlandAIEngineInferEnity *>(pEnity);
    if (!benity)
    {
        if (marker)
        {
            KObject *obj = (KObject *)this;
            std::string s = "模块 " + obj->name() + " 没有模型 !";
            marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
            printf("KInferModule add text : %s \n", s.c_str());
        }
        printf(" KInferModule::run() !benity return, m id = %s\n", id().c_str());

        return KCV_OK;
    }
    // 前序模块无检出
    if (!_KRectIn.rect1D().size())
    {
        printf("KDirtyAreaModule::run() !_dirtyRects.rect1D().size() return!\n");
        _isOk.setValue(true);
        return KCV_OK;
    }
    // 前序模块检出多
    if (_KRectIn.rect1D().size() > _dirtyNums.iValue())
    {
        marker->addText("污点过多", KPoint(10, 50), KCV_RED);
        _isOk.setValue(false);
        return Algo_Return_Dirty;
    }
    bm_image *bmImage = (bm_image *)_bmInImage.bmImagePtr(); // 获取传递的bmimage指针
    KBitlandAIEngineInferData *rltData = (KBitlandAIEngineInferData *)(benity->data());
    int outwidth = rltData->OutputDim[0].Width, outheight = rltData->OutputDim[0].Height;
    int totalreaCount = 0, totalarea = 0;
    for (const auto &rect : _KRectIn.rect1D())
    {
        // 中心点外扩
        KRect rc;
        cv::Point cen;
        if (rect.x < outwidth / 2)
        {
            cen.x = outwidth / 2;
        }
        if (rect.x > _image2.Image2().width() - outwidth / 2)
        {
            cen.x = _image2.Image2().width() - outwidth / 2;
        }
        if (rect.y < outheight / 2)
        {
            cen.y = outheight / 2;
        }
        if (rect.y > _image2.Image2().height() - outheight / 2)
        {
            cen.y = _image2.Image2().height() - outheight / 2;
        }
        rc.x = cen.x;
        rc.y = cen.y;
        rc.width = outwidth;
        rc.height = outheight;
        if (bmImage)
        {
            benity->infer(bmImage, rc);
        }
        else
        {
            benity->infer(_image2.KImage2Ptr(), rc);
        }
        cv::Mat binary = cv::Mat(outheight, outwidth, CV_32FC1, (float *)rltData->rltdata) > 0.5;
        int area = cv::countNonZero(binary);
        if (area > _areaThreshold.iValue())
        {
            _isOk.setValue(false);
            _KRectOut.push_back(rc);
            // 补充绘制内容
        }
        totalarea += area;
    }
    // 总面积大于阈值
    if (totalarea > _dirtyCountArea.iValue())
    {
        _isOk.setValue(false);
    }
    return KCV_OK;
}
#pragma endregion

#pragma region 多帧检测模块
KMultiFrameModule::KMultiFrameModule()
    : KModule()
{

    params().add("imageMarker", &_imageMarker);
    params().add("image2", &_image2);
}

REGISTER_MODULE(KMultiFrameModule);

void KMultiFrameModule::setIndexPtr(KInt *index)
{
    _index = index;
}
#include <chrono>
int KMultiFrameModule::run()
{
    KImageMarker *marker = (KImageMarker *)(_imageMarker.imageMarker());
    //    auto start = std::chrono::steady_clock::now();
    for (int i = 0; i < _frameObj.size(); ++i)
    {
        auto pre = std::chrono::steady_clock::now();
        if (!_frameObj[i])
            return KCV_NG_DataError;
        _index->setValue(i << 16);
        marker->setCurFrameIndex(i);
        _result[i] = _frameObj[i]->run();
        marker->setImage(&_image2.Image2().src());
        marker->drawImage();
        auto cur = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(cur - pre);
        std::cout << "运行耗时: " << duration.count() << " 毫秒" << std::endl;
        printf("绘制图像结束 \n");

        cv::Mat &retMat = _image2.Image2().src();
        cv::cvtColor(retMat, retMat, CV_RGB2BGR);
        std::string name = "./result-" + std::to_string(i) + ".png";
        bool wRet = cv::imwrite(name, retMat);
        printf("程序结束，保存图像结果index = %d, ret = %d \n", i, wRet);
    }
    //    auto total = std::chrono::steady_clock::now();
    //    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(total - start);
    //    std::cout << "运行耗时total: " << duration.count() << " 毫秒" << std::endl;

    for (int ret : _result)
        if (!ret)
            return ret;
    return KCV_OK;
}

void KMultiFrameModule::resize(int frameCount)
{
    if (_frameObj.size() != frameCount)
    {
        _frameObj.resize(frameCount);
        _result.resize(frameCount);
    }
}

void KMultiFrameModule::addFrameRunObj(int frameIndex, KObject *obj)
{
    if (frameIndex >= _frameObj.size())
        return;
    _frameObj[frameIndex] = obj;
}
#pragma endregion
