# KStandardModule 标准模块

该模块主要包含深度学习算子处理，包括分类、检测、分割、异常检测、缺陷检测以及后处理等。

## 模块概述

### 分割模块 (KSegmentModule)
- 实现图像分割功能，支持多类别分割
- 处理AI推理结果并输出二值图
- 支持切图推理和全图推理
- 支持单目标定位或多目标轮廓提取

### 目标检测模块 (KObjectDetectModule)
- 检测图像中的目标位置
- 提供多目标检测结果
- 支持灵敏度调节和区域限制条件

### 分类模块 (KClassifyModule)
- 对图像进行类别判断
- 输出各类别概率分数
- 提供最高分和最低分类别索引

### 异常检测模块 (KAbnormalModule)
- 对图像区域进行异常分析
- 基于阈值进行判断

### 缺陷检测系列模块
- **缺陷检测模块(KDefectModule)**: 检测图像中的缺陷区域
- **缺陷后处理模块(KDefectPostModule)**: 对检测结果进行筛选和处理
- **脏污区域模块(KDirtyAreaModule/KDirtyModule)**: 针对脏污类缺陷进行检测和处理

### 多帧处理模块 (KMultiFrameModule)
- 支持多帧图像顺序处理
- 管理多个处理对象的运行

## 使用说明

所有模块均继承自KModule基类，通过REGISTER_MODULE宏进行注册。各模块遵循统一的接口规范：

1. 通过参数系统定义输入输出
2. 实现run()方法作为主要执行入口
3. 通过enable()判断模块是否启用
4. 返回值0表示成功，其他值表示不同类型的错误

## 技术特性

- 支持BM加速器硬件推理
- 集成图像标记功能，便于结果可视化
- 模块化设计，支持灵活组合和扩展
- 支持ROI区域处理，提高处理效率
- 参数化配置，便于调整和优化

## 应用场景

- 工业视觉检测
- 产品质量控制
- 缺陷检测与分类
- 视觉定位与测量
