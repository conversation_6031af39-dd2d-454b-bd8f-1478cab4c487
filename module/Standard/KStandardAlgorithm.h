﻿#pragma once

#include "KModuleDefine.h"

// 模块，每一种操作代表一种模块，可以是具体的算法，io操作，分支流程等

#pragma region 分割模块
// 对推理算子结果后处理，输出二值图，考虑多类别情况
class KSegmentModule : public KModule
{
public:
    KSegmentModule();
    int run() override;

private:
    // 输入
    KRectData _finalRectIn;  // 输入的roi
    KImage2Data _image2;     // 输入的kimage图像
    KBmImageData _bmInImage; // 输入的bmimage图像

    // 输出
    KImage2DataVec _binaryImages; // 分割结果二值图
    KInt _channelNum;             // 输出类别数            //需根据模型信息设置合理的参数范围
    KInt _outputWidth;            // 输出二值图宽度        //需根据模型信息设置合理的参数范围
    KInt _outputHeight;           // 输出二值图高度        //需根据模型信息设置合理的参数范围
    KDouble _scaleWidth;          // 二值图宽度缩放比例
    KDouble _scaleHeight;         // 二值图高度缩放比例
    KRectData _finalRectOut;      // 输出的roi

    // 设置
    KSmartParamAIData _smartAi;       // 输入模型
    KBool _isFirstInfer;              // 首次推理
    KBool _cutLocate = false;         // 是否为切图推理
    KBool _resizeToOrigin = true;     // 是否变换为原尺寸          //暂时默认开启，blob分析在原图比例下进行，后续需加速再优化
    KBool _locateSingleObject = true; // 是否输出单目标定位信息    //仅提供定位功能,关闭时输出轮廓等信息
    KBmImageData _bmOutImage;         // 传递的bmimage图像
    KImageMarkerData _imageMarker;    // 绘制工具
};

#pragma endregion

#pragma region 目标检测模块
// 目标检测算子 暂时只考虑单类别目标框
class KObjectDetectModule : public KModule
{
public:
    KObjectDetectModule();
    int run() override;

private:
    // 输入
    KRectData _finalRectIn;  // 输入的roi
    KImage2Data _image2;     // 输入的kimage图像
    KBmImageData _bmInImage; // 输入的bmimage图像

    // 输出
    KRect1DData _kRects;     // 多目标时使用
    KRectData _finalRectOut; // 输出分析区域

    // 设置
    KSmartParamAIData _smartAi;    // 输入模型
    KBool _isFirstInfer;           // 首次推理
    KBmImageData _bmOutImage;      // 传递的bmimage图像
    KImageMarkerData _imageMarker; // 绘制工具
    KBool _cutLocate = false;      // 是否为切图推理
    KBool _enableMark = true;      // 是否绘制目标区域
    KInt _sensor;                  // 灵敏度
    KInt _blobNumber;              // 查找最大个数，1个时找最大的
    KBool _enableLimitWidth;       // 最小宽度限制
    KInt _minWidth;                // 区域最小宽度
    KBool _enableLimitHeight;      // 最小高度限制
    KInt _minHeight;               // 区域最小高度
};

#pragma endregion

#pragma region 分类模块
class KClassifyModule : public KModule
{
public:
    KClassifyModule();
    int run() override;

private:
    // 输入
    KRectData _finalRectIn;  // 输入的roi
    KImage2Data _image2;     // 输入的kimage图像
    KBmImageData _bmInImage; // 输入的bmimage图像

    // 输出
    KInt _classNumber;     // 类别数
    vector<float> _scores; // 各类别概率
    KInt _maxClassIndex;   // 最大得分类别id
    KInt _minClassIndex;   // 最小得分类别id

    // 设置
    KSmartParamAIData _smartAi;    // 输入模型
    KBool _isFirstInfer;           // 首次推理
    KBmImageData _bmOutImage;      // 传递的bmimage图像
    KImageMarkerData _imageMarker; // 绘制工具
    KBool _cutLocate = false;      // 是否为切图推理
    KBool _enableDraw = true;      // 是否绘制分类结果
};

#pragma endregion

#pragma region 异常检测模块
class KAbnormalModule : public KModule
{
public:
    KAbnormalModule();
    int run() override;

private:
    // 输入
    KAiInferData _inferData;
    KRectData _finalRect;
    KImage2Data _image2;
    KImageMarkerData _imageMarker;

    // 输出

    // 设置
    KDouble _threshold;
};

#pragma endregion

#pragma region 缺陷检测模块
class KDefectModule : public KModule
{
public:
    KDefectModule();
    int run() override;

private:
    // 输入
    KRectData _finalRectIn;  // 输入的roi
    KImage2Data _image2;     // 输入的kimage图像
    KBmImageData _bmInImage; // 输入的bmimage图像

    // 输出
    KImage2DataVec _binaryImages; // 结果二值图
    KInt _channelNum;             // 输出类别数             //需根据模型信息设置合理的参数范围
    KInt _outputWidth;            // 输出二值图宽度         //需根据模型信息设置合理的参数范围
    KInt _outputHeight;           // 输出二值图高度         //需根据模型信息设置合理的参数范围
    KDouble _xScale;              // x方向伸缩尺度
    KDouble _yScale;              // x方向伸缩尺度

    // 设置
    KSmartParamAIData _smartAi;    // 输入模型
    KBool _isFirstInfer;           // 首次推理
    KBmImageData _bmOutImage;      // 传递的bmimage图像
    KImageMarkerData _imageMarker; // 绘制工具
    KBool _cutLocate = false;      // 是否为切图推理
};

#pragma endregion

#pragma region 缺陷后处理模块
class KDefectPostModule : public KModule
{
public:
    KDefectPostModule();
    int run() override;

private:
    // 输入
    KImage2Data _image2;          // 输入图像                     //rgb图像
    KImage2DataVec _binaryImages; // 分割结果二值图
    KRectData _finalRectIn;       // 分析区域
    KInt _channelNum;             // 分割算子输出类别数            //需根据模型信息设置合理的参数范围
    KDouble _xScale;              // x方向伸缩尺度
    KDouble _yScale;              // x方向伸缩尺度

    // 输出
    KBool _isOk;           // 是否检出缺陷
    KRect1DData _KRectOut; // 检出位置信息

    // 设置
    KInt _defectIndex;        // 当前处理类别id
    KBool _enableLimitWidth;  // 最小宽度限制
    KInt _minWidth;           // 区域最小宽度
    KBool _enableLimitHeight; // 最小高度限制
    KInt _minHeight;          // 区域最小高度
};

#pragma endregion

#pragma region 脏污检测模块
class KDirtyModule : public KModule
{
public:
    KDirtyModule();
    int run() override;

private:
    KInt _sensor;
    KInt _inferDataIndex;
    KImage2DataVec _inferImages;
    KRectData _finalRect;
    KImage2Data _image2;
    KAiInferData _inferData;
    KImageMarkerData _imageMarker;
    KRect1DData _dirtyRects;
};

#pragma endregion

#pragma region 脏污面积检测模块
class KDirtyAreaModule : public KModule
{
public:
    KDirtyAreaModule();
    int run() override;

private:
    // 输入
    KImage2Data _image2;     // 输入的kimage图像
    KBmImageData _bmInImage; // 输入的bmimage图像
    KRect1DData _KRectIn;    // 检出位置信息
    KInt _defectIndex;       // 类别id

    // 输出
    KBool _isOk;           // 是否检出缺陷
    KRect1DData _KRectOut; // 检出位置信息

    // 设置
    KSmartParamAIData _smartAi;    // 输入模型
    KInt _areaThreshold;           // 黑点面积阈值，大于直接报错
    KInt _dirtyNums;               // 黑点数量              //后面看是传给软件还是模块内判断
    KInt _dirtyCountArea;          // 黑点总面积            //后面看是传给软件还是模块内判断
    KImageMarkerData _imageMarker; // 绘制工具
};

#pragma endregion

#pragma region 多帧检测模块
class KMultiFrameModule : public KModule
{
public:
    KMultiFrameModule();
    void setIndexPtr(KInt *index);
    int run() override;
    void resize(int frameCount);
    void addFrameRunObj(int frameIndex, KObject *obj);

private:
    KInt *_index = nullptr;
    KImage2Data _image2;
    KImageMarkerData _imageMarker;
    std::vector<KObject *> _frameObj;
    std::vector<int> _result;
};
#pragma endregion