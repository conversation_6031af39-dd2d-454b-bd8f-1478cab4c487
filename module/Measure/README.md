# KMeasureModule 测量模块

该模块主要用于工业视觉领域中的测量和分析，包括点线距离、点点距离、线线相交、亮度测量、边缘检测等功能。

## 模块概述

### 点线距离测量模块 (KPointTolineDistModule)
- 计算点到直线的垂直距离
- 输出垂足坐标和距离值
- 支持可视化显示距离和垂足
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "inputPoint": 输入点
  - "startPoint": 直线起始点
  - "endPoint": 直线终止点
- 输出参数:
  - "image2Out": 输出图像
  - "distance": 距离值

### 点点距离测量模块 (KPointTopointDistModule)
- 计算两点之间的欧氏距离
- 输出中点坐标和距离值
- 适用于简单距离测量任务
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "firstPoint": 输入点1
  - "secondPoint": 输入点2
- 输出参数:
  - "image2Out": 输出图像
  - "distance": 距离值

### 线线测量模块 (KLineIntersectionModule)
- 计算两条直线的交点坐标
- 测量两直线之间的夹角
- 计算两直线之间的平均距离
- 支持可视化显示交点和夹角
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "startPoint_1": 直线1起始点
  - "endPoint_1": 直线1终止点
  - "startPoint_2": 直线2起始点
  - "endPoint_2": 直线2终止点
- 输出参数:
  - "image2Out": 输出图像
  - "distance": 距离值
  - "angle": 夹角值

### 亮度测量模块 (KBrightnessDetectModule)
- 在指定ROI区域内进行灰度值统计分析
- 计算最小值、最大值、平均值、标准差和对比度
- 生成图像直方图进行可视化分析
- 适用于表面质量和光学特性检测
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "roi": 查找ROI区域
- 输出参数:
  - "image2Out": 输出图像
  - "minGrayPixel": 最小灰度值
  - "maxGrayPixel": 最大灰度值
  - "avgGrayPixel": 平均灰度值
  - "standGrayPixel": 标准差灰度值
  - "contrastGrayPixel": 对比度灰度值

### 直线边缘测量检测模块 (KEdgeDetectLineModule)
- 支持四种边缘检测方向：上到下、下到上、左到右、右到左
- 支持黑到白和白到黑两种边缘极性检测
- 使用RANSAC算法拟合直线，提高鲁棒性
- 检测边缘线上的突出部分，适用于凹凸缺陷分析
- 输出缺陷的宽度、面积和深度
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "roiRect": 检测区域
  - "edgeDirection": 边缘查找方向 --- 0-上到下（比较），1-下到上， 2-左到右， 3-右到左
  - "edgePolarity": 边缘极性	 --- 0-黑到白， 1-白到黑
  - "threshold": 阈值
  - "interval": 搜索间隔
  - "detectThreshold": 检测阈值
- 输出参数:
  - "image2Out": 输出图像
  - "edgepoints": 边缘点集

### 圆边缘测量检测模块 (KEdgeDetectCircleModule)
- 检测圆形目标的轮廓和半径
- 使用最小包围圆算法定位圆心
- 沿径向检测实际边缘点，分析边缘偏离情况
- 检测圆周上的凹凸缺陷，计算缺陷面积和深度
- 支持可视化标记缺陷区域
#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "roiRect": 检测区域
  - "threshold": 阈值
  - "thresholdType": 阈值类型 --- 0-THRESH_BINARY，1-THRESH_BINARY_INV
- 输出参数:
  - "image2Out": 输出图像
## 使用说明

所有模块均继承自KModule基类，通过REGISTER_MODULE宏进行注册。各模块遵循统一的接口规范：

1. 通过参数系统定义输入输出
2. 实现run()方法作为主要执行入口
3. 通过enable()判断模块是否启用
4. 返回值0表示成功，其他值表示不同类型的错误

## 应用场景

- 工业部件尺寸测量：长度、宽度、距离、角度等
- 表面质量检测：边缘缺陷、轮廓偏差、突起凹坑等
- 装配精度验证：位置偏差、装配间隙测量
- 光学特性分析：亮度、对比度、均匀性测量
- 圆形零件检测：圆度、同心度、边缘缺陷分析


