﻿#pragma once

#include "KModuleDefine.h"

// 模块，每一种操作代表一种模块，可以是具体的算法，io操作，分支流程等
#pragma region 点线距离测量模块
class KPointTolineDistModule : public KModule
{
public:
    KPointTolineDistModule();
    int run() override;
    void setInputPoint(cv::Point2d inputPoint);
    void setInputLine(cv::Point2d startPoint, cv::Point2d endPoint);

private:
    // 输入
    KImage2Data _image2In;   // 输入图像
    cv::Point2d _inputPoint; // 输入点坐标
    cv::Point2d _startPoint; // 直线起始点
    cv::Point2d _endPoint;   // 直线终止点

    // 输出
    KImage2Data _image2Out; // 结果图
    cv::Point2d _footPoint; // 垂足点
    KDouble distance;       // 测量点到垂足点距离
};

#pragma endregion

#pragma region 点点距离测量模块
class KPointTopointDistModule : public KModule
{
public:
    KPointTopointDistModule();
    int run() override;
    void setInputPoint(cv::Point2d firstPoint, cv::Point2d secondPoint);

private:
    // 输入
    KImage2Data _image2In;    // 输入图像
    cv::Point2d _firstPoint;  // 输入点1坐标
    cv::Point2d _secondPoint; // 输入点2坐标

    // 输出
    KImage2Data _image2Out; // 结果图
    KDouble _distance;      // 两点距离
    cv::Point2d _midPoint;  // 中点坐标
};

#pragma endregion

#pragma region 线线测量模块
class KLineIntersectionModule : public KModule
{
public:
    KLineIntersectionModule();
    int run() override;

    void setInputLine(cv::Point2d startPoint_1, cv::Point2d endPoint_1, cv::Point2d startPoint_2, cv::Point2d endPoint_2);
    cv::Point2d calculateIntersection(cv::Point2d pt1, cv::Point2d pt2, cv::Point2d pt3, cv::Point2d pt4);
    double calculateAngle(cv::Point2d pt1, cv::Point2d pt2, cv::Point2d pt3, cv::Point2d pt4, bool acute);
    std::vector<cv::Point> getLinePointsBresenham(cv::Point pt1, cv::Point pt2);

private:
    // 输入
    KImage2Data _image2In;     // 输入图像
    cv::Point2d _startPoint_1; // 直线起始点
    cv::Point2d _endPoint_1;   // 直线终止点
    cv::Point2d _startPoint_2; // 直线起始点
    cv::Point2d _endPoint_2;   // 直线终止点

    // 输出
    KImage2Data _image2Out;    // 结果图
    cv::Point2d _intersection; // 交点坐标
    KDouble _angle;            // 两直线夹角
    KDouble _distance;         // 两直线平均距离
};
#pragma endregion

#pragma region 亮度测量模块
class KBrightnessDetectModule : public KModule
{
public:
    KBrightnessDetectModule();
    int run() override;

private:
    // 输入
    KImage2Data _image2In; // 输入图像

    KRectData _roi; // 查找ROI区域

    // 输出
    KImage2Data _image2Out;     // 结果图
    KDouble _minGrayPixel;      // 最小灰度
    KDouble _maxGrayPixel;      // 最大灰度
    KDouble _avgGrayPixel;      // 灰度平均值
    KDouble _standGrayPixel;    // 灰度标准差
    KDouble _contrastGrayPixel; // 对比度
    cv::Mat histogram;          // 直方图
};

#pragma endregion

#pragma region 直线边缘测量检测模块
class KEdgeDetectLineModule : public KModule
{
public:
    KEdgeDetectLineModule();
    int run() override;

    int findEdge(cv::Mat bin, int edgedirection, int edgepolarity, int interval, vector<cv::Point> &points);
    int fitlinefrompoints(vector<cv::Point> points, cv::Point &startp, cv::Point &endp);

private:
    // 输入
    KImage2Data _image2In; // 输入图像

    KRectData _roiRect;        // 检测区域
    KInt _edgeDirection = 2;   // 边缘查找方向 --- 0-上到下，1-下到上， 2-左到右， 3-右到左
    KInt _edgePolarity = 1;    // 边缘极性	 --- 0-黑到白， 1-白到黑
    KInt _threshold = 50;      // 阈值
    KInt _interval = 5;        // 搜索间隔
    KInt _detectThreshold = 5; // 检测阈值  突变的阈值

    // 输出
    KImage2Data _image2Out;       // 结果图
    vector<cv::Point> edgepoints; // 边缘点集
};
#pragma endregion

#pragma region 圆边缘测量检测模块
class KEdgeDetectCircleModule : public KModule
{
public:
    KEdgeDetectCircleModule();
    int run() override;

    cv::Point2f computeCentroid(const std::vector<cv::Point> &points);
    void sortPointsClockwise(std::vector<cv::Point> &points);

private:
    // 输入
    KImage2Data _image2In;   // 输入图像
    KRectData _roiRect;      // 检测区域
    KInt _threshold = 100;   // 阈值
    KInt _thresholdType = 0; // 阈值类型 0-THRESH_BINARY，1-THRESH_BINARY_INV

    // 输出
    KImage2Data _image2Out; // 结果图
};
#pragma endregion

#pragma region 线圆距离测量模块
class KLineCircleDistModule : public KModule
{
public:
    KLineCircleDistModule();
    int run() override;

    void setInputLine(cv::Point2d startPoint, cv::Point2d endPoint);
    void setInputCircle(cv::Point2d center, double radius);

private:
    // 输入
    KImage2Data _image2In;   // 输入图像
    cv::Point2d _startPoint; // 直线起始点
    cv::Point2d _endPoint;   // 直线终止点

    cv::Point2d _center; // 圆心
    double _radius;      // 半径

    // 输出
    KImage2Data _image2Out; // 结果图
    KDouble _distance;      // 距离
};
#pragma endregion

#pragma region 圆圆距离测量模块
class KCircleCircleDistModule : public KModule
{
public:
    KCircleCircleDistModule();
    int run() override;

    void setInputCircle(cv::Point2d center1, double radius1, cv::Point2d center2, double radius2);

private:
    // 输入
    KImage2Data _image2In; // 输入图像
    cv::Point2d _center1;  // 圆心1
    cv::Point2d _center2;  // 圆心2
    double _radius1;       // 半径1
    double _radius2;       // 半径2

    // 输出
    KImage2Data _image2Out; // 结果图
    KDouble _distance;      // 距离
};
#pragma endregion