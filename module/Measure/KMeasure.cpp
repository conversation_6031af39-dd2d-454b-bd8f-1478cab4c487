#include "KMeasure.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/KImageMarker.h"
#include "KFactory.h"

cv::Point2d calDistance(cv::Point2d input, cv::Point2d start, cv::Point2d end, double &dist)
{
    // 1. 计算方向向量
    cv::Point2d direction = end - start;
    // 2. 计算点到直线起点的向量
    cv::Point2d toPoint = input - start;
    // 3. 计算直线长度的平方
    double lengthSq = direction.x * direction.x + direction.y * direction.y;
    // 4. 处理直线退化的情况
    if (lengthSq < std::numeric_limits<double>::epsilon())
    {
        return cv::Point2d(0, 0);
    }
    // 5. 计算投影参数 t
    double t = (toPoint.x * direction.x + toPoint.y * direction.y) / lengthSq;
    // 6. 计算垂足坐标
    cv::Point2d footpoint = start + t * direction;
    // 7. 计算距离
    dist = cv::norm(input - footpoint);
    return footpoint;
}

#pragma region 点线距离测量模块
KPointTolineDistModule::KPointTolineDistModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("distance", &distance);
}

REGISTER_MODULE(KPointTolineDistModule);

int KPointTolineDistModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    double dist = 0;
    cv::line(output, _startPoint, _endPoint, cv::Scalar(0, 255, 0), 2);
    _footPoint = calDistance(_inputPoint, _startPoint, _endPoint, dist);
    // 绘制
    circle(output, _footPoint, 5, cv::Scalar(255, 255, 0), 1);
    circle(output, _inputPoint, 5, cv::Scalar(255, 255, 0), 1);
    cv::line(output, _footPoint, _inputPoint, cv::Scalar(0, 0, 255), 2);
    cv::putText(output, "dist = " + to_string(dist).substr(0, 5), cv::Point(10, 80), 2, 2, cv::Scalar(0, 0, 255), 2);
    _image2Out.Image2().src() = output;
    distance.setValue(dist);
    return 0;
}
void KPointTolineDistModule::setInputPoint(cv::Point2d inputPoint)
{
    _inputPoint = inputPoint;
}
void KPointTolineDistModule::setInputLine(cv::Point2d startPoint, cv::Point2d endPoint)
{
    _startPoint = startPoint;
    _endPoint = endPoint;
}
#pragma endregion

#pragma region 点点距离测量模块
KPointTopointDistModule::KPointTopointDistModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("distance", &_distance);
}

REGISTER_MODULE(KPointTopointDistModule);

int KPointTopointDistModule::run()
{
    double dist = 0;
    dist = cv::norm(_firstPoint - _secondPoint);
    _midPoint = (_firstPoint + _secondPoint) / 2;
    _distance.setValue(dist);

    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    cv::line(output, _firstPoint, _secondPoint, cv::Scalar(0, 255, 0), 2);
    cv::circle(output, _firstPoint, 5, cv::Scalar(255, 255, 0), 1);
    cv::circle(output, _secondPoint, 5, cv::Scalar(255, 255, 0), 1);
    cv::putText(output, "dist = " + to_string(dist).substr(0, 5), cv::Point(10, 80), 2, 2, cv::Scalar(0, 0, 255), 2);
    _image2Out.Image2().src() = output;
    return 0;
}

void KPointTopointDistModule::setInputPoint(cv::Point2d firstPoint, cv::Point2d secondPoint)
{
    _firstPoint = firstPoint;
    _secondPoint = secondPoint;
}

#pragma endregion

#pragma region 线线测量模块
KLineIntersectionModule::KLineIntersectionModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("angle", &_angle);
    result().add("distance", &_distance);
}

REGISTER_MODULE(KLineIntersectionModule);

int KLineIntersectionModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();

    cv::line(output, _startPoint_1, _endPoint_1, cv::Scalar(0, 255, 0), 2);
    cv::line(output, _startPoint_2, _endPoint_2, cv::Scalar(0, 255, 0), 2);
    _intersection = calculateIntersection(_startPoint_1, _endPoint_1, _startPoint_2, _endPoint_2);
    if (_intersection == cv::Point2d(-1, -1))
    {
        return 1; // 平行线
    }
    cv::putText(output, "(" + to_string(_intersection.x).substr(0, 5) + "," + to_string(_intersection.y).substr(0, 5) + ")", _intersection, 1, 1, cv::Scalar(0, 0, 255), 1);
    double angle = calculateAngle(_startPoint_1, _endPoint_1, _startPoint_2, _endPoint_2, true);
    if (angle == -1.0)
        return 1;
    cv::putText(output, to_string(angle).substr(0, 5) + "deg", cv::Point(10, 50), 2, 2, cv::Scalar(0, 255, 0), 2);
    // 绘制
    cv::Point2d p1 = calculateIntersection(_startPoint_1, _endPoint_1, cv::Point2d(0, 0), cv::Point2d(0, input.rows));
    cv::Point2d p2 = calculateIntersection(_startPoint_1, _endPoint_1, cv::Point2d(input.cols, 0), cv::Point2d(input.cols, input.rows));
    cv::line(output, p1, p2, cv::Scalar(255, 255, 0));
    cv::Point2d p3 = calculateIntersection(_startPoint_2, _endPoint_2, cv::Point2d(0, 0), cv::Point2d(input.cols, 0));
    cv::Point2d p4 = calculateIntersection(_startPoint_2, _endPoint_2, cv::Point2d(0, input.rows), cv::Point2d(input.cols, input.rows));
    cv::line(output, p3, p4, cv::Scalar(255, 255, 0));

    // 平均距离
    // 获取线段1上所有点集合
    std::vector<cv::Point> points = getLinePointsBresenham(_startPoint_1, _endPoint_1);
    double totalDist = 0.0;
    for (int i = 0; i < points.size(); i++)
    {
        double dist;
        calDistance(points[i], _startPoint_2, _endPoint_2, dist);
        totalDist += dist;
        // 绘制
        cv::circle(output, points[i], 5, cv::Scalar(0, 0, 255), 1);
    }
    double distance = totalDist / points.size();
    cv::putText(output, "dist = " + to_string(distance).substr(0, 5), cv::Point(10, 100), 2, 2, cv::Scalar(0, 255, 0), 2);
    _distance.setValue(distance);
    _angle.setValue(angle);
    _image2Out.Image2().src() = output;

    return 0;
}

void KLineIntersectionModule::setInputLine(cv::Point2d startPoint_1, cv::Point2d endPoint_1, cv::Point2d startPoint_2, cv::Point2d endPoint_2)
{
    _startPoint_1 = startPoint_1;
    _endPoint_1 = endPoint_1;
    _startPoint_2 = startPoint_2;
    _endPoint_2 = endPoint_2;
}

cv::Point2d KLineIntersectionModule::calculateIntersection(cv::Point2d pt1, cv::Point2d pt2,
                                                           cv::Point2d pt3, cv::Point2d pt4)
{
    // 计算分母
    float denominator = (pt2.x - pt1.x) * (pt3.y - pt4.y) - (pt2.y - pt1.y) * (pt3.x - pt4.x);
    // 处理平行线（分母接近0）
    if (fabs(denominator) < 1e-6)
    {
        return cv::Point2d(-1, -1); // 返回无效点表示无交点
    }
    // 计算分子
    float numerator = (pt3.x - pt1.x) * (pt3.y - pt4.y) - (pt3.y - pt1.y) * (pt3.x - pt4.x);
    // 计算参数 t
    float t = numerator / denominator;
    // 计算交点坐标
    float x = pt1.x + t * (pt2.x - pt1.x);
    float y = pt1.y + t * (pt2.y - pt1.y);
    return cv::Point2d(x, y);
}

double KLineIntersectionModule::calculateAngle(cv::Point2d pt1, cv::Point2d pt2,
                                               cv::Point2d pt3, cv::Point2d pt4,
                                               bool acute = true)
{
    // 计算方向向量
    cv::Point2d vec1 = pt2 - pt1;
    cv::Point2d vec2 = pt4 - pt3;
    // 计算点积和模长
    double dotProduct = vec1.x * vec2.x + vec1.y * vec2.y;
    double norm1 = std::sqrt(vec1.x * vec1.x + vec1.y * vec1.y);
    double norm2 = std::sqrt(vec2.x * vec2.x + vec2.y * vec2.y);
    // 处理零向量
    if (norm1 < 1e-6 || norm2 < 1e-6)
    {
        return -1.0; // 无效值
    }
    // 计算余弦值（限制在[-1,1]范围内）
    double cosTheta = dotProduct / (norm1 * norm2);
    cosTheta = std::max(-1.0, std::min(1.0, cosTheta)); // 避免浮点误差
    // 计算角度（弧度转角度）
    double angle = std::acos(cosTheta) * 180.0 / CV_PI;
    // 返回锐角（0-90度）或实际角度（0-180度）
    return acute ? (angle > 90 ? 180 - angle : angle) : angle;
}

std::vector<cv::Point> KLineIntersectionModule::getLinePointsBresenham(cv::Point pt1, cv::Point pt2)
{
    std::vector<cv::Point> points;

    int x1 = pt1.x, y1 = pt1.y;
    int x2 = pt2.x, y2 = pt2.y;

    // 计算增量
    int dx = std::abs(x2 - x1);
    int dy = std::abs(y2 - y1);

    // 确定方向
    int sx = (x1 < x2) ? 1 : -1;
    int sy = (y1 < y2) ? 1 : -1;

    int err = dx - dy;

    while (true)
    {
        points.push_back(cv::Point(x1, y1));

        // 到达终点
        if (x1 == x2 && y1 == y2)
            break;

        int e2 = 2 * err;

        if (e2 > -dy)
        {
            err -= dy;
            x1 += sx;
        }

        if (e2 < dx)
        {
            err += dx;
            y1 += sy;
        }
    }

    return points;
}

#pragma endregion

#pragma region 亮度测量模块
KBrightnessDetectModule::KBrightnessDetectModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("roi", &_roi);
    // _roi.setValue(cv::Rect(345, 60, 550, 250));

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("minGrayPixel", &_minGrayPixel);
    result().add("maxGrayPixel", &_maxGrayPixel);
    result().add("avgGrayPixel", &_avgGrayPixel);
    result().add("standGrayPixel", &_standGrayPixel);
    result().add("contrastGrayPixel", &_contrastGrayPixel);
}

REGISTER_MODULE(KBrightnessDetectModule);

int KBrightnessDetectModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    cv::rectangle(output, _roi.rect(), cv::Scalar(0, 255, 0), 2);
    double minGrayPixel = 0, maxGrayPixel = 0;
    double avgGrayPixel = 0, standGrayPixel = 0, contrastGrayPixel = 0;
    cv::Mat histogram;

    cv::Mat gray = input(_roi.rect()).clone();
    if (gray.channels() == 3)
    {
        cv::cvtColor(gray, gray, cv::COLOR_BGR2GRAY);
    }
    // 计算最小值和最大值
    cv::minMaxLoc(gray, &minGrayPixel, &maxGrayPixel);
    // 计算均值和标准差
    cv::Scalar mean, stddev;
    cv::meanStdDev(gray, mean, stddev);
    avgGrayPixel = mean[0];
    standGrayPixel = stddev[0];
    // 计算对比度 (RMS对比度)
    contrastGrayPixel = standGrayPixel;
    // 计算直方图
    int histSize = 256; // 0-255
    float range[] = {0, 256};
    const float *histRange = {range};
    cv::calcHist(&gray, 1, 0, cv::Mat(), histogram, 1, &histSize, &histRange);
    histogram = histogram.reshape(1, 1); // 转换为行向量

    _image2Out.Image2().src() = histogram;
    _minGrayPixel.setValue(minGrayPixel);
    _maxGrayPixel.setValue(maxGrayPixel);
    _avgGrayPixel.setValue(avgGrayPixel);
    _standGrayPixel.setValue(standGrayPixel);
    _contrastGrayPixel.setValue(contrastGrayPixel);
    _image2Out.Image2().src() = output;
    return 0;
}
#pragma endregion

#pragma region 直线边缘测量检测模块
#include <random>
KEdgeDetectLineModule::KEdgeDetectLineModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("roiRect", &_roiRect);
    // _roiRect.setRect(cv::Rect(350, 100, 250, 100));

    params().add("edgeDirection", &_edgeDirection);
    params().add("edgePolarity", &_edgePolarity);
    params().add("threshold", &_threshold);
    params().add("interval", &_interval);
    params().add("detectThreshold", &_detectThreshold);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KEdgeDetectLineModule);

int KEdgeDetectLineModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;
    cv::rectangle(output, _roiRect.rect(), cv::Scalar(0, 255, 0), 2);
    cv::Mat gray = input(_roiRect.rect()).clone();
    if (gray.channels() == 3)
    {
        cv::cvtColor(gray, gray, cv::COLOR_BGR2GRAY);
    }
    // 二值化
    cv::threshold(gray, gray, _threshold.iValue(), 255, cv::THRESH_BINARY);
    // 查找边缘，输出点集
    int ret = findEdge(gray, _edgeDirection.iValue(), _edgePolarity.iValue(), _interval.iValue(), edgepoints);
    if (edgepoints.size() < 10)
    {
        std::cout << " 点数过少,edgepoints.size() = " << edgepoints.size() << std::endl;
        return KCV_NG;
    }
    for (int i = 0; i < edgepoints.size(); i++)
    {
        edgepoints[i].x = edgepoints[i].x + _roiRect.rect().x;
        edgepoints[i].y = edgepoints[i].y + _roiRect.rect().y;
        cv::circle(output, edgepoints[i], 2, cv::Scalar(255, 255, 0), -1);
    }
    // 拟合直线
    cv::Point start_point, end_point;
    ret = fitlinefrompoints(edgepoints, start_point, end_point);
    cv::circle(output, start_point, 5, cv::Scalar(0, 255, 255), 2);
    cv::circle(output, end_point, 5, cv::Scalar(0, 255, 255), 2);
    cv::line(output, start_point, end_point, cv::Scalar(0, 255, 255), 1);
    // 计算偏离直线突出部分
    vector<cv::Point> bad;
    vector<vector<cv::Point>> allbad;
    bool start = false;
    int defectLength = -1000;
    vector<int> lens;
    for (int i = 0; i < edgepoints.size() - 1; i++)
    {
        double distance1 = 0.0, distance2 = 0.0;
        cv::Point footPoint1 = calDistance(edgepoints[i], start_point, end_point, distance1);
        cv::Point footPoint2 = calDistance(edgepoints[i + 1], start_point, end_point, distance2);
        if (distance1 < _detectThreshold.iValue() && distance2 > _detectThreshold.iValue())
        {
            // 第一次进入突变区域
            start = true;
            bad.clear();
            defectLength = -1000;
            bad.push_back(edgepoints[i]);
        }
        if (start)
        {
            bad.push_back(edgepoints[i + 1]);
            if (defectLength < distance2)
            {
                defectLength = distance2;
            }
        }
        if (distance1 > _detectThreshold.iValue() && distance2 < _detectThreshold.iValue())
        {
            // 离开突变区域
            start = false;
            allbad.push_back(bad);
            lens.push_back(defectLength);
        }
    }
    for (int i = 0; i < allbad.size(); i++)
    {
        vector<cv::Point> points = allbad[i];
        cv::drawContours(output, allbad, i, cv::Scalar(255, 0, 0), -1);
        int area = cv::contourArea(points);
        int width = points.size() * _interval.iValue();
        if (_edgeDirection.iValue() == 0)
        {
            cv::putText(output, "w: " + to_string(width), cv::Point(points[0].x, points[0].y + 20), 1, 1, cv::Scalar(255, 0, 0));
            cv::putText(output, "a: " + to_string(area), cv::Point(points[0].x, points[0].y + 40), 1, 1, cv::Scalar(255, 0, 0));
            cv::putText(output, "L: " + to_string(lens[i]), cv::Point(points[0].x, points[0].y - 20), 1, 1, cv::Scalar(255, 0, 0));
        }
        if (_edgeDirection.iValue() == 2)
        {
            cv::putText(output, "w:" + to_string(width), cv::Point(points[0].x + 10, points[0].y), 1, 1, cv::Scalar(255, 0, 0));
            cv::putText(output, "a:" + to_string(area), cv::Point(points[0].x + 10 + 60, points[0].y), 1, 1, cv::Scalar(255, 0, 0));
            cv::putText(output, "L:" + to_string(lens[i]), cv::Point(points[0].x + 10 + 120, points[0].y), 1, 1, cv::Scalar(255, 0, 0));
        }
    }

    return KCV_OK;
}

int KEdgeDetectLineModule::findEdge(cv::Mat bin, int edgedirection, int edgepolarity, int interval, vector<cv::Point> &points)
{
    // 0-上到下
    if (edgedirection == 0)
    {
        if (bin.rows < interval)
        {
            cout << " 间隔选择错误 " << endl;
            return 1;
        }
        for (int x = 0; x < bin.cols; x = x + interval)
        {
            for (int y = 0; y < bin.rows - 1; y++)
            {
                int pix = bin.at<uchar>(y, x);
                int pix2 = bin.at<uchar>(y + 1, x);
                if (edgepolarity == 0)
                { // 0-黑到白
                    if (pix2 > pix)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else if (edgepolarity == 1) // 1-白到黑
                {
                    if (pix2 < pix)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else
                { // 默认黑到白
                    if (pix2 > pix)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
            }
        }
    }
    else if (edgedirection == 1) // 从下到上
    {
        if (bin.rows < interval)
        {
            cout << " 间隔选择错误 " << endl;
            return 1;
        }
        for (int x = 0; x < bin.cols; x = x + interval)
        {
            for (int y = bin.rows - 2; y > 0; y--)
            {
                int pix = bin.at<uchar>(y, x);
                int pix2 = bin.at<uchar>(y + 1, x);
                if (edgepolarity == 0)
                { // 0-黑到白
                    if (pix > pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else if (edgepolarity == 1) // 1-白到黑
                {
                    if (pix < pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else
                { // 默认黑到白
                    if (pix > pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
            }
        }
    }
    else if (edgedirection == 2) // 2-左到右
    {
        if (bin.cols < interval)
        {
            cout << " 间隔选择错误 " << endl;
            return 1;
        }
        for (int y = 0; y < bin.rows; y = y + interval)
        {
            for (int x = 0; x < bin.cols - 1; x++)
            {
                int pix = bin.at<uchar>(y, x);
                int pix2 = bin.at<uchar>(y, x + 1);
                if (edgepolarity == 0)
                { // 0-黑到白
                    if (pix < pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else if (edgepolarity == 1) // 1-白到黑
                {
                    if (pix > pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else
                {
                    if (pix < pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
            }
        }
    }
    else if (edgedirection == 3) // 3-右到左
    {
        if (bin.cols < interval)
        {
            cout << " 间隔选择错误 " << endl;
            return 1;
        }
        for (int y = 0; y < bin.rows; y = y + interval)
        {
            for (int x = bin.cols - 2; x > 0; x--)
            {
                int pix = bin.at<uchar>(y, x);
                int pix2 = bin.at<uchar>(y, x + 1);
                if (edgepolarity == 0)
                { // 0-黑到白
                    if (pix > pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else if (edgepolarity == 1) // 1-白到黑
                {
                    if (pix < pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
                else
                {
                    if (pix > pix2)
                    {
                        points.push_back(cv::Point(x, y));
                        break;
                    }
                }
            }
        }
    }
    return 0;
}

int KEdgeDetectLineModule::fitlinefrompoints(vector<cv::Point> points, cv::Point &startp, cv::Point &endp)
{
    // 拟合直线
    cv::Vec4f line_params; // 点斜式
    // cv::fitLine(points, line_params, cv::DIST_L2, 0, 0.1, 0.1);

    // RANSAC 参数
    const int max_iterations = 100;
    const float distance_threshold = 2.0; // 内点距离阈值
    int best_inliers = 0;
    if (!points.empty())
    {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dist(0, points.size() - 1);

        for (int i = 0; i < max_iterations; i++)
        {
            // 随机选择两个点
            int idx1 = dist(gen);
            int idx2 = dist(gen);
            while (idx2 == idx1)
                idx2 = dist(gen);

            cv::Point p1 = points[idx1];
            cv::Point p2 = points[idx2];

            // 计算直线参数 (两点式)
            float vx = p2.x - p1.x;
            float vy = p2.y - p1.y;
            float norm = std::sqrt(vx * vx + vy * vy);
            if (norm < 1e-5)
                continue; // 避免除零

            vx /= norm;
            vy /= norm;

            // 计算点到直线距离并统计内点
            int inliers = 0;
            for (const auto &pt : points)
            {
                float distance = std::abs((pt.x - p1.x) * vy - (pt.y - p1.y) * vx);
                if (distance < distance_threshold)
                    inliers++;
            }

            // 更新最佳模型
            if (inliers > best_inliers)
            {
                best_inliers = inliers;
                line_params = cv::Vec4f(vx, vy, p1.x, p1.y);
            }
        }
    } // RANSAC end
    // 提取直线参数
    float vx = line_params[0];
    float vy = line_params[1];
    float x0 = line_params[2];
    float y0 = line_params[3];

    //(x-x0)/vx = (y-y0)/vy; k = vy/vx = (y-y0)/(x-x0)
    if (_edgeDirection.iValue() == 0 || _edgeDirection.iValue() == 1) // 从上到下,从下到上,不存在斜率无限大情况
    {
        double k = vy / vx;
        startp.x = points[0].x;
        startp.y = k * (startp.x - x0) + y0;
        endp.x = points[points.size() - 1].x;
        endp.y = k * (endp.x - x0) + y0;
    }
    else if (_edgeDirection.iValue() == 2 || _edgeDirection.iValue() == 3)
    { // 从左到右，从右到左，注意斜率无限大问题
        if (abs(vx) < 1e-5f)
        {
            return 1;
        }
        else
        {
            double k = vy / vx;
            startp.y = points[0].y;
            startp.x = (startp.y - y0) / k + x0;
            endp.y = points[points.size() - 1].y;
            endp.x = (endp.y - y0) / k + x0;
        }
    }

    return 0;
}
#pragma endregion

#pragma region 圆边缘测量检测模块
KEdgeDetectCircleModule::KEdgeDetectCircleModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("roiRect", &_roiRect);
    params().add("threshold", &_threshold);
    params().add("thresholdType", &_thresholdType);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KEdgeDetectCircleModule);

int KEdgeDetectCircleModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;
    cv::Mat gray = input(_roiRect.rect()).clone();

    if (gray.channels() == 3)
        cv::cvtColor(gray, gray, cv::COLOR_BGR2GRAY);
    int thresholdType = _thresholdType.iValue();
    if (thresholdType == 0)
        cv::threshold(gray, gray, _threshold.iValue(), 255, cv::THRESH_BINARY);
    else
        cv::threshold(gray, gray, _threshold.iValue(), 255, cv::THRESH_BINARY_INV);
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    int maxArea = -100, maxIdx = 0;

    cv::findContours(gray, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    if (contours.size() == 0)
    {
        std::cout << " 未找到圆 " << std::endl;
        return KCV_NG;
    }
    for (int i = 0; i < contours.size(); i++)
    {
        int area = cv::contourArea(contours[i]);
        if (area > maxArea)
        {
            maxArea = area;
            maxIdx = i;
        }
    }
    cv::Point2f center;
    float radius = 0.0;
    cv::minEnclosingCircle(contours[maxIdx], center, radius);

    int range = 15;                         // 搜索范围-由半径向内和向外
    float nangle = 5;                       // 等分角度
    float start_angle = 0;                  // 起始角度
    float end_angel = 360;                  // 结束角度
    std::vector<cv::Point2f> detect_points; // 实际边缘点
    std::vector<cv::Point2f> circle_points; // 圆环等分点

    // 计算圆环等分点
    //  将起始角度转换为弧度并偏移（数学上的0度是右侧，逆时针为正）
    const float offset_rad = start_angle * CV_PI / 180.0f;
    int n = (end_angel - start_angle) / nangle;
    for (int i = 0; i < n; ++i)
    {
        // 计算当前角度
        float angle = nangle * i * CV_PI / 180.0f + offset_rad;

        // 计算坐标
        float x = center.x + radius * std::cos(angle);
        float y = center.y + radius * std::sin(angle);
        circle_points.emplace_back(x, y);
        cv::circle(output, cv::Point(x, y), 1, cv::Scalar(255, 0, 0), 1);
    }
    detect_points.resize(circle_points.size());

    // 计算边缘点
    for (int i = 0; i < n; ++i)
    {
        // 计算当前角度
        float angle = nangle * i * CV_PI / 180.0f + offset_rad;
        bool findPoint = false;
        // 计算坐标
        for (int j = radius - range; j < radius + range - 1; j++)
        {
            float x1 = center.x + j * std::cos(angle);
            float y1 = center.y + j * std::sin(angle);
            float x2 = center.x + (j + 1) * std::cos(angle);
            float y2 = center.y + (j + 1) * std::sin(angle);

            // 转换为整数坐标
            int x1_int = cvRound(x1);
            int y1_int = cvRound(y1);
            int x2_int = cvRound(x2);
            int y2_int = cvRound(y2);

            // 边界检查
            if (x1_int >= 0 && x1_int < gray.cols && y1_int >= 0 && y1_int < gray.rows &&
                x2_int >= 0 && x2_int < gray.cols && y2_int >= 0 && y2_int < gray.rows)
            {
                int pix1 = gray.at<uchar>(y1_int, x1_int);
                int pix2 = gray.at<uchar>(y2_int, x2_int);

                if (pix1 > pix2)
                {
                    detect_points[i] = cv::Point(x1, y1);
                    cv::circle(output, detect_points[i], 1, cv::Scalar(0, 0, 255), 1);
                    findPoint = true;
                    break;
                }
            }
        }
        // 考虑无突变边缘情况
        if (!findPoint)
        {
            float x1 = center.x + (radius + range - 2) * std::cos(angle);
            float y1 = center.y + (radius + range - 2) * std::sin(angle);

            // 转换为整数坐标
            int x1_int = cvRound(x1);
            int y1_int = cvRound(y1);

            // 边界检查
            if (x1_int >= 0 && x1_int < gray.cols && y1_int >= 0 && y1_int < gray.rows)
            {
                int pix1 = gray.at<uchar>(y1_int, x1_int);
                if (pix1 == 255)
                {
                    detect_points[i] = cv::Point(x1, y1);
                }
                else
                {
                    // 计算备选点坐标
                    float inner_x = center.x + (radius - range) * std::cos(angle);
                    float inner_y = center.y + (radius - range) * std::sin(angle);
                    detect_points[i] = cv::Point(inner_x, inner_y);
                }
            }
            else
            {
                // 如果坐标超出范围，使用安全的备选点
                float inner_x = center.x + (radius - range) * std::cos(angle);
                float inner_y = center.y + (radius - range) * std::sin(angle);

                // 确保备选点也在图像范围内
                int inner_x_int = cvRound(inner_x);
                int inner_y_int = cvRound(inner_y);

                if (inner_x_int >= 0 && inner_x_int < gray.cols && inner_y_int >= 0 && inner_y_int < gray.rows)
                {
                    detect_points[i] = cv::Point(inner_x, inner_y);
                }
                else
                {
                    // 最后的保险：使用圆上的原始点
                    detect_points[i] = circle_points[i];
                }
            }
            cv::circle(output, detect_points[i], 1, cv::Scalar(0, 0, 255), 1);
        }
    }

    // 计算
    vector<cv::Point> bad;
    vector<vector<cv::Point>> allbad;
    bool start = false;
    float dist_threshold = 4.0;
    vector<float> dists_max;
    float dist_max = -1.0;
    for (int i = 0; i < n; ++i)
    {
        float dist1 = sqrt((circle_points[i].x - detect_points[i].x) * (circle_points[i].x - detect_points[i].x) + (circle_points[i].y - detect_points[i].y) * (circle_points[i].y - detect_points[i].y));
        float dist2 = sqrt((circle_points[i + 1].x - detect_points[i + 1].x) * (circle_points[i + 1].x - detect_points[i + 1].x) + (circle_points[i + 1].y - detect_points[i + 1].y) * (circle_points[i + 1].y - detect_points[i + 1].y));

        if (dist1 > 5)
        {
            cv::line(output, circle_points[i], detect_points[i], cv::Scalar(0, 255, 255), 1);
        }
        // 第一次进入突变区域
        if (dist1 < dist_threshold && dist2 > dist_threshold)
        {
            start = true;
            bad.clear();
            dist_max = -1.0;
            bad.push_back(detect_points[i]);
            bad.push_back(circle_points[i]);
        }
        if (start)
        {
            bad.push_back(detect_points[i + 1]);
            bad.push_back(circle_points[i + 1]);
            if (dist_max < dist2)
            {
                dist_max = dist2;
            }
        }
        if (dist1 > dist_threshold && dist2 < dist_threshold)
        {
            // 离开突变区域
            start = false;
            sortPointsClockwise(bad);
            allbad.push_back(bad);
            dists_max.push_back(dist_max);
        }
    }

    for (int i = 0; i < allbad.size(); i++)
    {
        cv::Scalar color = cv::Scalar(
            cv::theRNG().uniform(0, 256), // 蓝色分量
            cv::theRNG().uniform(0, 256), // 绿色分量
            cv::theRNG().uniform(0, 256)  // 红色分量
        );
        vector<cv::Point> points = allbad[i];
        std::cout << "points.size() = " << points.size() << std::endl;
        if (points.size() > 0)
        {
            cv::drawContours(output, allbad, i, color, -1);
            int area = cv::contourArea(points);
            cv::putText(output, "a:" + to_string(area) + " L:" + to_string(dists_max[i]).substr(0, 4),
                        cv::Point(5, i * 10 + 10), FONT_HERSHEY_PLAIN, 0.8, color);
        }
    }
    return 0;
}

// 计算点集的质心
cv::Point2f KEdgeDetectCircleModule::computeCentroid(const std::vector<cv::Point> &points)
{
    float cx = 0, cy = 0;
    for (const auto &pt : points)
    {
        cx += pt.x;
        cy += pt.y;
    }
    return cv::Point2f(cx / points.size(), cy / points.size());
}

// 顺时针排序点集
void KEdgeDetectCircleModule::sortPointsClockwise(std::vector<cv::Point> &points)
{
    // 1. 计算质心
    cv::Point2f center = computeCentroid(points);

    // 2. 使用Lambda表达式自定义排序
    std::sort(points.begin(), points.end(),
              [center](const cv::Point &a, const cv::Point &b)
              {
                  // 计算相对于质心的角度
                  double angleA = std::atan2(a.y - center.y, a.x - center.x);
                  double angleB = std::atan2(b.y - center.y, b.x - center.x);

                  // 处理角度跨越x轴的情况（负角度处理）
                  if (angleA < 0)
                      angleA += 2 * CV_PI;
                  if (angleB < 0)
                      angleB += 2 * CV_PI;

                  // 从大到小排序 → 顺时针
                  return angleA > angleB;
              });
}
#pragma endregion

KLineCircleDistModule::KLineCircleDistModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KLineCircleDistModule);

int KLineCircleDistModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;
    
    return 0;
}

void KLineCircleDistModule::setInputLine(cv::Point2d startPoint, cv::Point2d endPoint)
{
    _startPoint = startPoint;
    _endPoint = endPoint;
}

void KLineCircleDistModule::setInputCircle(cv::Point2d center, double radius)
{
    _center = center;
    _radius = radius;
}
