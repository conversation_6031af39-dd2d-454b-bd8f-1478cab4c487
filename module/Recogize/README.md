# KRecogizeModule 条码识别模块

该模块主要提供条码识别功能，包括二维码和一维条形码的检测与解码，以及字符识别功能。

## 模块概述

### 条码识别模块 (KCodeRecognizeModule)
- 支持二维码和一维条形码的识别
- 基于ZXing库实现高效的条码检测和解码
- 支持图像下采样以提高处理速度
- 自动在图像上标注识别结果

#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "downSampleNum": 下采样倍数，值大于1时启用下采样，一般不需要修改，zxing内部自动做了默认3倍的下采样
  - "Type": 条码类型，0: 二维码(矩阵码)，1: 一维条形码(线性码)
- 输出参数:
  - "image2Out": 输出图像，包含识别结果标注

### 字符识别模块 (KOCRModule)
- 支持字符识别
- 基于模板匹配实现字符识别
- 支持AI推理和后处理
- 输出识别结果

#### 参数配置
- 输入参数:
  - "image2In": 输入图像
  - "rects": 字符区域矩形框
  - "smartAi": 智能AI模型配置
- 输出参数:
  - "image2Out": 输出图像
  - "ocrResult": 识别结果

## 使用说明

模块继承自KModule基类，通过REGISTER_MODULE宏进行注册。遵循统一的接口规范：

1. 通过params()方法注册输入参数
2. 通过result()方法注册输出结果
3. 实现run()方法作为主要执行入口
4. 返回值0表示成功，其他值表示不同类型的错误

## 技术特性

- 基于ZXing库实现条码识别功能
- 支持多种条码格式，包括QR码、DataMatrix、Code128等
- 可配置的图像下采样，平衡速度和准确性
- 自动处理彩色和灰度图像
- 识别结果直接显示在输出图像上

## 应用场景

- 工业自动化中的产品标识识别
- 物流系统中的包裹跟踪
- 零售业的商品条码扫描
- 文档管理系统中的二维码识别
- 移动应用中的条码扫描功能
