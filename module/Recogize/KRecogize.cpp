#include "KRecogize.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/KImageMarker.h"
#include "KFactory.h"
#include <chrono> // 添加时间测量头文件
#include "ZXing/ReadBarcode.h"
#include "ZXing/BarcodeFormat.h"

#pragma region 二维码识别模块

KCodeRecognizeModule::KCodeRecognizeModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("downSampleNum", &_downSampleNum);
    params().add("Type", &_Type);
    _downSampleNum.setValue(1);
    _Type.setValue(0);
    /*输出参数*/
    result().add("image2Out", &_image2Out);
}
REGISTER_MODULE(KCodeRecognizeModule);

int KCodeRecognizeModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat input_downSample;
    if (_downSampleNum.iValue() > 1)
        cv::resize(input, input_downSample, cv::Size(input.cols / _downSampleNum.iValue(), input.rows / _downSampleNum.iValue()));
    else
        input_downSample = input;
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;

    cv::Mat grayImage;
    if (input_downSample.channels() == 3)
    {
        cv::cvtColor(input_downSample, grayImage, cv::COLOR_RGB2GRAY);
    }
    else
        grayImage = input_downSample;

    ZXing::ImageView imageView(
        grayImage.data,
        grayImage.cols,
        grayImage.rows,
        ZXing::ImageFormat::Lum);
    ZXing::ReaderOptions options;
    if (_Type.iValue() == 0) // 二维码
        options.setFormats(ZXing::BarcodeFormat::MatrixCodes);
    else // 条形码
        options.setFormats(ZXing::BarcodeFormat::LinearCodes);
    options.setTryDownscale(true);
    auto barcodes = ZXing::ReadBarcodes(imageView, options);

    for (const auto &b : barcodes)
    {
        std::cout << ZXing::ToString(b.format()) << ": " << b.text() << "\n";
        cv::putText(output, b.text(), cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 255, 0), 2);
        count++;
    }

    return 0;
}
#pragma endregion

#pragma region 字符识别模块
#include "KControllerAIEngineManager.h"
void getPathCharName(const std::string imagePath, std::string &charname)
{
    std::string pathname;
    for (int i = 0;; ++i)
    {
        if (imagePath[i] == '/' || imagePath[i] == '\\')
            pathname = imagePath.substr(0, i);
        if (imagePath[i] == 0)
            break;
    }

    for (int i = 0;; ++i)
    {
        if (pathname[i] == '/' || pathname[i] == '\\')
            charname = pathname.substr(i + 1);
        if (pathname[i] == 0)
            break;
    }

    return;
}

int keep_ratio_resizeW(cv::Mat &img, int dst_height, int dst_max_width, cv::Mat &imgdst)
{
    int ori_height = img.rows;
    int ori_width = img.cols;

    double new_width = std::ceil(static_cast<double>(dst_height) / ori_height * ori_width);
    int resize_width = std::min(dst_max_width, static_cast<int>(new_width));

    cv::Mat imgtemp;
    cv::resize(img, imgtemp, cv::Size(resize_width, dst_height));

    if (resize_width < dst_max_width)
    {
        imgtemp.copyTo(imgdst(cv::Rect(0, 0, resize_width, dst_height)));
    }
    else
    {
        cv::resize(imgtemp, imgdst, cv::Size(dst_max_width, dst_height));
    }

    return 0;
}

KOCRTemplateModule::KOCRTemplateModule()
{
    /*输入参数*/
    KControllerAIEngineManager *manager = KControllerAIEngineManager::singleInstance();

    _smartAi.smartAiData()->setInfo(KSmartParamInfo{20001, 20001, "id_recog", "id_recog", "id_recog"});

    KAIEngineInferEnity *e = manager->createEnity("AI/recog_slhz.kcloudnef");
    _smartAi.smartAiData()->setEnity(e);

    params().add("imageMarker", &_imageMarker);
}

REGISTER_MODULE(KOCRTemplateModule);

int KOCRTemplateModule::run()
{
    std::string imagePath = "template";
    std::vector<std::string> files;
    cv::glob(imagePath + "/*.png", files, true);

    ff512.resize(files.size(), std::vector<float>(512, 0));
    for (int i = 0; i < files.size(); ++i)
    {
        std::string charname;
        getPathCharName(files[i], charname);
        charnameLists.emplace_back(charname);

        cv::Mat img = cv::imread(files[i]);
        if (img.empty())
            continue;

        recogTemplate(img, ff512[i]);
    }
    return 0;
}

int KOCRTemplateModule::recogTemplate(cv::Mat &img, std::vector<float> ff)
{
    KAIEngineInferEnity *pRegEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *bRegEnity = dynamic_cast<KBitlandAIEngineInferEnity *>(pRegEnity);
    KImageMarker *marker = (KImageMarker *)_imageMarker.imageMarker();
    if (!bRegEnity)
    {
        KObject *obj = (KObject *)this;
        std::string s = "模块 " + obj->name() + " 没有模型 !";
        marker->addText(s.c_str(), KPoint(10, 50), KCV_RED);
        printf("recoglitao add text : %s \n", s.c_str());
        return 0; //
    }
    KBitlandAIEngineInferData *recog_rltdata = (KBitlandAIEngineInferData *)(bRegEnity->data());

    int ih_recog = recog_rltdata->InputDim[0].Height;
    int iw_recog = recog_rltdata->InputDim[0].Width;

    int oh_recog = recog_rltdata->OutputDim[0].Channel;
    int ow_recog = recog_rltdata->OutputDim[0].Height;

    // cv::Mat imgrecogdst =cv::Mat::zeros(ih_recog,iw_recog,CV_8UC3);
    //  std::cout<<"ih iw: "<< ih_recog<<","<<iw_recog<<"ms"<<std::endl;
    //  std::cout<<"oh ow: "<< oh_recog<<","<<ow_recog<<"ms"<<std::endl;

    cv::Mat imgrecogdst = cv::Mat::zeros(ih_recog, iw_recog, CV_8UC3);

    keep_ratio_resizeW(img, ih_recog, iw_recog, imgrecogdst);
    // imwrite("imgrecogdst.png", imgrecogdst);
    KImage2 inferImg(imgrecogdst);
    auto start = std::chrono::high_resolution_clock::now();
    bRegEnity->infer(&inferImg);

    auto end = std::chrono::high_resolution_clock::now();
    auto dur = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    // outfile<< dur.count()/1000000.0<<"ms"<<std::endl;
    // std::cout<<"infer time: "<< dur.count()/1000000.0<<"ms"<<std::endl;

    cv::Mat rec_mat(1, ow_recog, CV_32FC1, (float *)recog_rltdata->rltdata);

    for (int kk = 0; kk < ow_recog; ++kk)
    {
        ff[kk] = (rec_mat.at<float>(0, kk));
    }
    return 0;
}

std::vector<std::vector<float>> KOCRTemplateModule::getTemplateMes()
{
    return ff512;
}

std::vector<std::string> KOCRTemplateModule::getCharNameLists()
{
    return charnameLists;
}

bool comparex(const cv::Rect &a, const cv::Rect &b)
{
    return a.x < b.x;
}

int sortRects(cv::Mat &srcimg, std::vector<cv::Rect> &rects, std::vector<cv::Mat> &imgchars)
{
    sort(rects.begin(), rects.end(), comparex);
    if (rects.size() <= 0)
        return 1;
    for (auto rect : rects)
    {
        cv::Mat imgtemp = srcimg(rect).clone();
        imgchars.emplace_back(imgtemp);
    }
    return 0;
}

double euclidean_distance(std::vector<float> &base, std::vector<float> &target)
{
    double sumDescriptor = 0;
    for (int i = 0; i < base.size(); i++)
    {
        // std::cout<<"t:"<<target[i]<<std::endl;
        // std::cout<<"r:"<<base[i]<<std::endl;
        float fva = (base[i] - target[i]) * (base[i] - target[i]);
        sumDescriptor += fva;
        // std::cout<<"fva:"<<fva<<std::endl;
    }
    double simility = std::pow(sumDescriptor, 0.5);
    // std::cout<<"simility:" <<simility<<std::endl;
    return simility;
}

KOCRModule::KOCRModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("rects", &_rects);
    params().add("smartAi", &_smartAi);
    KSmartParamAI *ai = new KSmartParamAI;
    _smartAi.setAIParam(ai);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("ocrResult", &_ocrResult);
}

REGISTER_MODULE(KOCRModule);

int KOCRModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;

    std::vector<cv::Rect> rects = _rects.rect1D();
    std::vector<cv::Mat> imgchars;
    sortRects(input, rects, imgchars);
    std::string ss;
    recogOCR(imgchars, ss);
    _ocrResult.setValue(&ss);
    cv::putText(output, ss, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 255, 0), 2);
    return 0;
}

int KOCRModule::recogOCR(std::vector<cv::Mat> &imgchars, std::string &ss)
{
    KAIEngineInferEnity *pRegEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *bRegEnity = dynamic_cast<KBitlandAIEngineInferEnity *>(pRegEnity);

    if (!bRegEnity)
        return 0;
    KBitlandAIEngineInferData *recog_rltdata = (KBitlandAIEngineInferData *)(bRegEnity->data());

    int ih_recog = recog_rltdata->InputDim[0].Height;
    int iw_recog = recog_rltdata->InputDim[0].Width;

    int oh_recog = recog_rltdata->OutputDim[0].Channel;
    int ow_recog = recog_rltdata->OutputDim[0].Height;

    cv::Mat imgrecogdst = cv::Mat::zeros(ih_recog, iw_recog, CV_8UC3);
    for (int i = 0; i < imgchars.size(); ++i)
    {
        keep_ratio_resizeW(imgchars[i], ih_recog, iw_recog, imgrecogdst);
        KImage2 inferImg(imgrecogdst);
        bRegEnity->infer(&inferImg);

        int maxclass = -1;
        std::vector<float> ff;
        cv::Mat rec_mat(1, ow_recog, CV_32FC1, (float *)recog_rltdata->rltdata);
        for (int index = 0; index < ow_recog; ++index)
        {
            ff.emplace_back(rec_mat.at<float>(0, index));
        }
        std::vector<double> dd;
        dd.resize(ffTemplate.size(), 0.0);
        for (int index = 0; index < ffTemplate.size(); ++index)
        {
            std::vector<float> fftemp = ffTemplate[index];
            dd[index] = euclidean_distance(ff, fftemp);
        }
        auto minposition = std::min_element(dd.begin(), dd.end());
        int poschar = minposition - dd.begin();
        std::string sssingle = charnameListsTemplate[poschar];
        ss += sssingle;
    }

    return 0;
}

#pragma endregion