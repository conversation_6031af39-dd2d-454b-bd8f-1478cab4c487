#pragma once

#include "KModuleDefine.h"

#pragma region 二维码识别模块
class KCodeRecognizeModule : public KModule
{
public:
    KCodeRecognizeModule();
    int run() override;
    int count = 0;

private:
    KImage2Data _image2In;  // 输入图像
    KInt _downSampleNum;    // 下采样倍数
    KInt _Type;             // 0-二维码 1-条形码
    KImage2Data _image2Out; // 输出图像（可选，用于可视化结果）
};
#pragma endregion

#pragma region 字符识别模块

class KOCRTemplateModule : public KModule
{
public:
    KOCRTemplateModule();
    int run() override;

    int recogTemplate(cv::Mat &img, std::vector<float> ff);
    std::vector<std::vector<float>> getTemplateMes();
    std::vector<std::string> getCharNameLists();

private:
    KImageMarkerData _imageMarker;

    KSmartParamAIData _smartAi;
    std::vector<std::vector<float>> ff512;
    std::vector<std::string> charnameLists;
};

class KOCRModule : public KModule
{
public:
    KOCRModule();
    int run() override;
    int recogOCR(std::vector<cv::Mat> &imgchars, std::string &ss);

private:
    KImage2Data _image2In; // 输入图像
    KSmartParamAIData _smartAi;
    KRect1DData _rects;
    std::vector<std::vector<float>> ffTemplate;
    std::vector<std::string> charnameListsTemplate;

    KImage2Data _image2Out; // 输出图像（可选，用于可视化结果）
    KString _ocrResult;
};
#pragma endregion