#include "KDefect.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/KImageMarker.h"
#include "KFactory.h"

#pragma region 表面缺陷滤波模块
KSurfaceDefectFilterModule::KSurfaceDefectFilterModule() : KModule(), _ksize(15), _sigma(50), _numKernels(6), _defectThreshold(100)
{
    params().add("ksize", &_ksize);
    params().add("sigma", &_sigma);
    params().add("numKernels", &_numKernels);
    std::cout << "numKernels: " << _numKernels.iValue() << std::endl;
    params().add("defectThreshold", &_defectThreshold);

    params().add("image2In", &_image2In);
    // params().add("image2Out", &_image2Out);
    result().add("image2Out", &_image2Out);

    if (_ksize.iValue() % 2 == 0)
    {
        _ksize.setValue(_ksize.iValue() + 1); // Ensure kernel size is odd
    }
    // generateKernels();

    // 可视化生成的滤波核
    // visualizeKernels();
}

REGISTER_MODULE(KSurfaceDefectFilterModule);

int KSurfaceDefectFilterModule::run()
{
    cv::Mat grayImage;
    if (_image2In.Image2().channel() == 3)
    {
        cv::cvtColor(_image2In.Image2().src(), grayImage, cv::COLOR_BGR2GRAY);
    }
    else
    {
        grayImage = _image2In.Image2().src();
    }

    // Convert to float for filtering operations
    cv::Mat gray_float;
    grayImage.convertTo(gray_float, CV_32F);

    // Initialize a matrix to store the maximum response from all filters
    cv::Mat maxResponse = cv::Mat::zeros(gray_float.size(), CV_32F);

    // Apply each rotated kernel and find the maximum response
    for (const auto &kernel : kernels)
    {
        cv::Mat response;
        cv::filter2D(gray_float, response, -1, kernel, cv::Point(-1, -1), 0, cv::BORDER_REPLICATE);
        cv::max(maxResponse, response, maxResponse);
    }
    cv::Mat normalizedResponseMap;
    cv::normalize(maxResponse, normalizedResponseMap, 0, 255, cv::NORM_MINMAX, CV_8U);
    // cv::imwrite("normalizedResponseMap.png", normalizedResponseMap);

    cv::Mat binaryDefects;
    cv::threshold(normalizedResponseMap, binaryDefects, _defectThreshold.fValue(), 255, cv::THRESH_BINARY);
    // cv::imwrite("binaryDefects.png", binaryDefects);

    // 3. 使用形态学操作去除噪点并连接缺陷
    // 首先使用 "开" 操作去除小的噪点
    cv::Mat kernel_open = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(3, 3));
    cv::Mat openedDefects;
    cv::morphologyEx(binaryDefects, openedDefects, cv::MORPH_OPEN, kernel_open);

    // 然后使用 "闭" 操作连接断裂的缺陷
    cv::Mat kernel_close = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(7, 7)); // 增大闭操作核，以便连接较长的划痕
    cv::Mat closedDefects;
    cv::morphologyEx(openedDefects, closedDefects, cv::MORPH_CLOSE, kernel_close);

    _image2Out.Image2().src() = closedDefects;

    return KCV_OK;
}

void KSurfaceDefectFilterModule::generateKernels()
{
    if (_ksize.iValue() % 2 == 0)
    {
        _ksize.setValue(_ksize.iValue() + 1); // Ensure kernel size is odd
    }
    // 清空现有的kernel集合
    kernels.clear();

    int ksize = _ksize.iValue();
    double sigma = _sigma.fValue();
    int num_kernels = _numKernels.iValue();
    int center = ksize / 2;
    std::cout << "ksize: " << ksize << std::endl;
    std::cout << "sigma: " << sigma << std::endl;
    std::cout << "num_kernels: " << num_kernels << std::endl;
    // 1. 生成一维高斯核
    cv::Mat gauss1D = cv::Mat::zeros(1, ksize, CV_32F);
    for (int i = 0; i < ksize; ++i)
    {
        int x = i - center;
        gauss1D.at<float>(0, i) = (1.0f / (sqrt(2.0f * CV_PI) * sigma)) * exp(-(x * x) / (2 * sigma * sigma));
    }
    gauss1D /= cv::sum(gauss1D)[0]; // 归一化

    // 2. 生成二维带状核（只在中间一行有值）
    cv::Mat bandKernel = cv::Mat::zeros(ksize, ksize, CV_32F);
    for (int i = 0; i < ksize; ++i)
    {
        for (int j = 0; j < ksize / 2.0; ++j)
        {
            bandKernel.at<float>(center - ksize / 4.0 + j + 1, i) = gauss1D.at<float>(0, i);
        }
    }
    bandKernel /= cv::sum(bandKernel)[0];

    // 3. 旋转到不同角度，得到不同方向的滤波核
    for (int k = 0; k < num_kernels; ++k)
    {
        double angle = 180.0 * k / num_kernels;
        cv::Mat rotMat = cv::getRotationMatrix2D(cv::Point2f(center, center), angle, 1.0);
        cv::Mat rotatedKernel;
        cv::warpAffine(bandKernel, rotatedKernel, rotMat, bandKernel.size());
        kernels.push_back(rotatedKernel);
    }
}

// 添加函数以可视化滤波核
void KSurfaceDefectFilterModule::visualizeKernels()
{
    // 创建一个目录来保存核图像
    system("mkdir -p kernel_images");
    system("rm -rf kernel_images/*");

    // 单独保存每个滤波核
    for (size_t i = 0; i < kernels.size(); ++i)
    {
        // 将滤波核归一化到0-255用于显示
        cv::Mat kernelVis;
        cv::normalize(kernels[i], kernelVis, 0, 255, cv::NORM_MINMAX, CV_8U);

        // 可选：将核放大以便更好地查看
        cv::Mat kernelResized;
        // cv::resize(kernelVis, kernelResized, cv::Size(), 10, 10, cv::INTER_NEAREST);

        // 保存
        std::string filename = "kernel_images/kernel_" + std::to_string(i) + ".png";
        cv::imwrite(filename, kernelVis);
    }

    // 将所有核组合成一张大图，便于比较
    int num_kernels = kernels.size();
    int kernelSize = kernels[0].rows;

    // 计算网格布局：尽量接近方形
    int grid_size = ceil(sqrt(num_kernels));
    cv::Mat allKernels = cv::Mat::zeros(grid_size * kernelSize, grid_size * kernelSize, CV_8UC1);

    for (int i = 0; i < num_kernels; ++i)
    {
        int row = i / grid_size;
        int col = i % grid_size;

        cv::Mat kernelVis;
        cv::normalize(kernels[i], kernelVis, 0, 255, cv::NORM_MINMAX, CV_8U);

        // 复制到大图中
        kernelVis.copyTo(allKernels(cv::Rect(col * kernelSize, row * kernelSize, kernelSize, kernelSize)));
    }

    // 将组合图像放大
    cv::Mat allKernelsResized;
    cv::resize(allKernels, allKernelsResized, cv::Size(), 4, 4, cv::INTER_NEAREST);

    // 保存组合图像
    cv::imwrite("kernel_images/all_kernels.png", allKernelsResized);

    // 输出提示信息
    std::cout << "已生成 " << num_kernels << " 个滤波核的可视化图像，保存在 kernel_images 目录下" << std::endl;
    std::cout << "单个核图像: kernel_X.png, 组合图像: all_kernels.png" << std::endl;
}
#pragma endregion

#pragma region 字符缺陷检测模块
KCharacterDefectDetectModule::KCharacterDefectDetectModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KCharacterDefectDetectModule);

int KCharacterDefectDetectModule::run()
{
    cv::Mat grayImage;
    if (_image2In.Image2().channel() == 3)
    {
        cv::cvtColor(_image2In.Image2().src(), grayImage, cv::COLOR_BGR2GRAY);
    }
    else
    {
        grayImage = _image2In.Image2().src();
    }
    return KCV_OK;
}
#pragma endregion
