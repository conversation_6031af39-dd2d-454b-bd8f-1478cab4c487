# KDefectModule 传统缺陷检测模块

该模块主要用于工业视觉领域中的传统缺陷检测，包括表面缺陷检测等。

## 模块概述

### 表面缺陷检测模块 (KSurfaceDefectFilterModule)

- 使用**多行一维高斯条带核**，通过旋转生成多个方向的滤波器组，检测无纹理表面上的缺陷
- 适用于检测:
  - 划痕缺陷：表面上的线性刮痕
  - 斑点缺陷：局部亮度异常的小区域
  - 瑕疵缺陷：形状不规则的表面损伤
#### 参数配置
- 输入参数:
  - "ksize": 滤波器组大小，默认15
  - "sigma": 高斯核标准差，默认50
  - "numKernels": 滤波器组数量，默认6
  - "defectThreshold": 缺陷阈值，默认100
  - "image2In": 输入图像
- 输出参数:
  - "image2Out": 输出图像

## 使用说明

所有模块均继承自KModule基类，通过REGISTER_MODULE宏进行注册。各模块遵循统一的接口规范：

1. 通过参数系统定义输入输出
2. 实现run()方法作为主要执行入口
3. 通过enable()判断模块是否启用
4. 返回值0表示成功，其他值表示不同类型的错误

## 应用场景

- 工业产品表面质量检测：金属、玻璃、塑料等材料表面
- PCB板缺陷检测：划痕、异物、污点等
- 平板显示器缺陷检测：亮点、暗点、线条缺陷等
- 精密机械零件表面瑕疵检测


