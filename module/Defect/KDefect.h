#pragma once
#include "KModuleDefine.h"

#pragma region 表面缺陷滤波模块
class KSurfaceDefectFilterModule : public KModule
{
public:
    KSurfaceDefectFilterModule();
    int run() override;
    void generateKernels();
    void visualizeKernels();

private:
    // int ksize = 31;//核大小
    // double sigma = 4.0;//标准差
    // int numKernels = 8;//核数量（8个角度）
    // double defectThreshold = 100;//阈值
    KInt _ksize;
    KFloat _sigma;
    KInt _numKernels;
    KFloat _defectThreshold;

    std::vector<cv::Mat> kernels;
    KImage2Data _image2In;  // 输入图像                     //rgb图像
    KImage2Data _image2Out; // 结果二值图
};
#pragma endregion

#pragma region 字符缺陷检测模块
class KCharacterDefectDetectModule : public KModule
{
public:
    KCharacterDefectDetectModule();
    int run() override;

private:
    KImage2Data _image2In;  // 输入图像                     //rgb图像
    KImage2Data _image2Out; // 结果二值图
};
#pragma endregion