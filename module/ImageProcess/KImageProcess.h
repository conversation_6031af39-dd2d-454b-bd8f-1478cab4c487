
#pragma once
#include "KModuleDefine.h"
#include "aiEngine/KBitlandAIEnity.h"

#pragma region 图像固定比例缩放模块
class KKeepRatioResizeModule : public KModule
{
public:
    KKeepRatioResizeModule();
    int run() override;

private:
    KImage2Data _image2In;
    KInt _widthorheight;
    KInt _dstheight;
    KInt _dstwidth;
    KImage2Data _image2Out;
};

#pragma endregion

#pragma region 图像缩放模块
class KImageResizeModule : public KModule
{
public:
    KImageResizeModule();
    int run() override;

private:
    KImage2Data _image2In;
    KInt _interpolationType;
    KInt _dstheight;
    KInt _dstwidth;
    KImage2Data _image2Out;
};
#pragma endregion

#pragma region 图像滤波模块
class KImageFilterModule : public KModule
{
public:
    KImageFilterModule();
    int run() override;

private:
    KInt _filterType;
    KImage2Data _image2In;
    KImage2Data _image2Out;

    KInt _gaussianKernel;
    KInt _medianKernel;
    KInt _meanWidthKernel;
    KInt _meanHeightKernel;
};
#pragma endregion

#pragma region 图像增强模块
class KImageEnhancedModule : public KModule
{
public:
    KImageEnhancedModule();
    int run() override;

    int EnhancedInfer();
    cv::Mat EnhancedPostProcess(KBitlandAIEngineInferData *dataEnity);

    int adjustBrightnessGamma(const cv::Mat &image, double gamma);
    int enhanceContrast_CLAHE(const cv::Mat &input, double clipLimit = 2.0, cv::Size gridSize = cv::Size(8, 8));

private:
    KImageMarkerData _imageMarker; // 绘制工具
    KSmartParamAIData _smartAi;    // 输入模型

    KInt _filterType;
    KInt _enhanceNum; // 增强次数
    KImage2Data _image2In;
    KImage2Data _image2Out;

    KDouble _alpha;
    KInt _beta;
    KDouble _gamma;
};
#pragma endregion