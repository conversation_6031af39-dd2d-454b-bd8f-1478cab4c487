cmake_minimum_required(VERSION 3.10)

set(CMAKE_TOOLCHAIN_FILE "/usr/local/1684Env/cmake/toolchain_aarch64_down_1684.cmake")

project(KOperatorLibrary)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

set(CMAKE_BUILD_TYPE "Release")

file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

message(STATUS "OPENCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OPENCV_LIBRARY_DIRS: ${OpenCV_LIBRARY_DIRS}")
message(STATUS "OPENCV_LIBRARIES: ${OpenCV_LIBRARIES}")

message(STATUS "OpenSSL_INCLUDE_DIRS: ${OpenSSL_INCLUDE_DIRS}")
message(STATUS "OpenSSL_LIBRARY_DIRS: ${OpenSSL_LIBRARY_DIRS}")
message(STATUS "OpenSSL_LIBRARIES: ${OpenSSL_LIBRARIES}")

#模块
add_subdirectory(module/Standard)
add_subdirectory(module/ImageProcess)
add_subdirectory(module/ColorProcess)
add_subdirectory(module/Position)
add_subdirectory(module/Defect)
add_subdirectory(module/Measure)
add_subdirectory(module/Recogize)
#测试
add_subdirectory(test/Defect)
add_subdirectory(test/ImageProcess)
add_subdirectory(test/Position)
add_subdirectory(test/ColorProcess)
add_subdirectory(test/Measure)
add_subdirectory(test/Recogize)
#框架
add_subdirectory(deps/libSimd)
# add_subdirectory(deps/Simd)
add_subdirectory(deps/cvText)
add_subdirectory(deps/framework)
add_subdirectory(deps/aiEngine)
add_subdirectory(deps/algorithm)

#框架依赖
add_dependencies(KAlgorithm freetype libSimd freetype)
add_dependencies(KBitlandAIEngine KAlgorithm)
add_dependencies(KFramework KBitlandAIEngine KAlgorithm)
#模块依赖
add_dependencies(KStandardModule KFramework)
add_dependencies(KImageProcessModule KFramework)
add_dependencies(KColorProcessModule KFramework)
add_dependencies(KPositionModule KFramework)
add_dependencies(KDefectModule KFramework)
add_dependencies(KMeasureModule KFramework)
add_dependencies(KRecogizeModule KFramework)
#测试依赖
add_dependencies(KDefectModule-Test KFramework)
add_dependencies(KImageProcessModule-Test KFramework)
add_dependencies(KPositionModule-Test KFramework)
add_dependencies(KColorProcessModule-Test KFramework)
add_dependencies(KMeasureModule-Test KFramework)
add_dependencies(KRecogizeModule-Test KFramework)