#!/bin/bash

# 定义颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 定义清理函数
clean() {
    echo -e "${YELLOW}正在清理构建文件...${NC}"
    
    # 清理build目录
    if [ -d "build" ]; then
        rm -rf build
        echo -e "${GREEN}build目录已清理${NC}"
    else
        echo -e "${YELLOW}build目录不存在，无需清理${NC}"
    fi
    
    # 清理bin/Release目录
    if [ -d "bin/Release" ]; then
        rm -rf bin/Release/*
        echo -e "${GREEN}bin/Release目录已清理${NC}"
    else
        echo -e "${YELLOW}bin/Release目录不存在，无需清理${NC}"
    fi
    
    echo -e "${GREEN}清理完成！${NC}"
}

# 定义构建函数
build() {
    # 创建构建目录
    mkdir -p build
    cd build

    # 运行CMake
    echo -e "${YELLOW}正在配置项目...${NC}"
    cmake .. 
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误：CMake配置失败！${NC}"
        cd ..
        exit 1
    fi

    # 编译
    echo -e "${YELLOW}正在编译项目...${NC}"
    make -j$(nproc)
    if [ $? -ne 0 ]; then
        echo -e "${RED}错误：编译失败！${NC}"
        cd ..
        exit 2
    fi

    # 返回上层目录
    cd ..

    echo -e "${GREEN}编译成功完成！${NC}"
    echo -e "${GREEN}输出文件位于：$(pwd)/bin/Release/${NC}"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  无参数   - 执行构建"
    echo "  clean    - 清理构建文件和输出文件"
    echo "  rebuild  - 清理并重新构建"
    echo "  help     - 显示此帮助信息"
}

# 根据参数执行不同操作
case "$1" in
    clean)
        clean
        ;;
    rebuild)
        clean
        build
        ;;
    help)
        show_help
        ;;
    "")
        build
        ;;
    *)
        echo -e "${RED}未知选项: $1${NC}"
        show_help
        exit 1
        ;;
esac

exit 0 