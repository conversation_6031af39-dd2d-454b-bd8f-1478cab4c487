#include <opencv2/opencv.hpp>
#include <iostream>
#include <cmath>

// 复制calDistance函数用于测试
cv::Point2d calDistance(cv::Point2d input, cv::Point2d start, cv::Point2d end, double &dist)
{
    // 1. 计算方向向量
    cv::Point2d direction = end - start;
    // 2. 计算点到直线起点的向量
    cv::Point2d toPoint = input - start;
    // 3. 计算直线长度的平方
    double lengthSq = direction.x * direction.x + direction.y * direction.y;
    // 4. 处理直线退化的情况
    if (lengthSq < std::numeric_limits<double>::epsilon())
    {
        return cv::Point2d(0, 0);
    }
    // 5. 计算投影参数 t
    double t = (toPoint.x * direction.x + toPoint.y * direction.y) / lengthSq;
    // 6. 计算垂足坐标
    cv::Point2d footpoint = start + t * direction;
    // 7. 计算距离
    dist = cv::norm(input - footpoint);
    return footpoint;
}

// 正确的直线到圆距离计算函数
double calculateLineToCircleDistance(cv::Point2d lineStart, cv::Point2d lineEnd, 
                                   cv::Point2d circleCenter, double radius)
{
    // 使用calDistance函数计算圆心到直线的垂直距离
    double perpDistance = 0.0;
    cv::Point2d footPoint = calDistance(circleCenter, lineStart, lineEnd, perpDistance);
    
    // 计算直线到圆的实际距离
    // 垂直距离 - 半径 = 直线到圆的距离
    return perpDistance - radius;
}

int main()
{
    // 测试用例1: 直线在圆外
    std::cout << "=== 测试用例1: 直线在圆外 ===" << std::endl;
    cv::Point2d lineStart1(0, 0);
    cv::Point2d lineEnd1(100, 0);  // 水平线
    cv::Point2d circleCenter1(50, 20);  // 圆心在直线上方20像素
    double radius1 = 10;
    
    double distance1 = calculateLineToCircleDistance(lineStart1, lineEnd1, circleCenter1, radius1);
    std::cout << "直线: (" << lineStart1.x << "," << lineStart1.y << ") -> (" 
              << lineEnd1.x << "," << lineEnd1.y << ")" << std::endl;
    std::cout << "圆心: (" << circleCenter1.x << "," << circleCenter1.y << "), 半径: " << radius1 << std::endl;
    std::cout << "距离: " << distance1 << " (应该约为10)" << std::endl;
    std::cout << "状态: " << (distance1 > 0 ? "直线在圆外" : (distance1 == 0 ? "直线与圆相切" : "直线与圆相交")) << std::endl;
    std::cout << std::endl;
    
    // 测试用例2: 直线与圆相切
    std::cout << "=== 测试用例2: 直线与圆相切 ===" << std::endl;
    cv::Point2d lineStart2(0, 0);
    cv::Point2d lineEnd2(100, 0);  // 水平线
    cv::Point2d circleCenter2(50, 15);  // 圆心在直线上方15像素
    double radius2 = 15;
    
    double distance2 = calculateLineToCircleDistance(lineStart2, lineEnd2, circleCenter2, radius2);
    std::cout << "直线: (" << lineStart2.x << "," << lineStart2.y << ") -> (" 
              << lineEnd2.x << "," << lineEnd2.y << ")" << std::endl;
    std::cout << "圆心: (" << circleCenter2.x << "," << circleCenter2.y << "), 半径: " << radius2 << std::endl;
    std::cout << "距离: " << distance2 << " (应该约为0)" << std::endl;
    std::cout << "状态: " << (distance2 > 0 ? "直线在圆外" : (distance2 == 0 ? "直线与圆相切" : "直线与圆相交")) << std::endl;
    std::cout << std::endl;
    
    // 测试用例3: 直线与圆相交
    std::cout << "=== 测试用例3: 直线与圆相交 ===" << std::endl;
    cv::Point2d lineStart3(0, 0);
    cv::Point2d lineEnd3(100, 0);  // 水平线
    cv::Point2d circleCenter3(50, 5);  // 圆心在直线上方5像素
    double radius3 = 15;
    
    double distance3 = calculateLineToCircleDistance(lineStart3, lineEnd3, circleCenter3, radius3);
    std::cout << "直线: (" << lineStart3.x << "," << lineStart3.y << ") -> (" 
              << lineEnd3.x << "," << lineEnd3.y << ")" << std::endl;
    std::cout << "圆心: (" << circleCenter3.x << "," << circleCenter3.y << "), 半径: " << radius3 << std::endl;
    std::cout << "距离: " << distance3 << " (应该约为-10)" << std::endl;
    std::cout << "状态: " << (distance3 > 0 ? "直线在圆外" : (distance3 == 0 ? "直线与圆相切" : "直线与圆相交")) << std::endl;
    std::cout << std::endl;
    
    // 测试用例4: 斜线测试
    std::cout << "=== 测试用例4: 斜线测试 ===" << std::endl;
    cv::Point2d lineStart4(0, 0);
    cv::Point2d lineEnd4(100, 100);  // 45度斜线
    cv::Point2d circleCenter4(50, 0);  // 圆心在x轴上
    double radius4 = 10;
    
    double distance4 = calculateLineToCircleDistance(lineStart4, lineEnd4, circleCenter4, radius4);
    std::cout << "直线: (" << lineStart4.x << "," << lineStart4.y << ") -> (" 
              << lineEnd4.x << "," << lineEnd4.y << ")" << std::endl;
    std::cout << "圆心: (" << circleCenter4.x << "," << circleCenter4.y << "), 半径: " << radius4 << std::endl;
    std::cout << "距离: " << distance4 << std::endl;
    std::cout << "状态: " << (distance4 > 0 ? "直线在圆外" : (distance4 == 0 ? "直线与圆相切" : "直线与圆相交")) << std::endl;
    
    return 0;
}
