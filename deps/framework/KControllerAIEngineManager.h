﻿#ifndef KCONTROLLERAIENGINEMANAGER_H
#define KCONTROLLERAIENGINEMANAGER_H

#include <QObject>

class KAIEngineInferEnity;

class KControllerAIEngineManagerPrivate;
class KControllerAIEngineManager : public QObject
{
    Q_OBJECT
public:
    static KControllerAIEngineManager *singleInstance();

    KControllerAIEngineManager();
    ~KControllerAIEngineManager();

public slots:
    bool createAIEngine(const std::string &modelpath);

    KAIEngineInferEnity *createEnity(const std::string &modelpath);

private:
    KControllerAIEngineManagerPrivate *p;
};

#endif // KCONTROLLERAIENGINEMANAGER_H
