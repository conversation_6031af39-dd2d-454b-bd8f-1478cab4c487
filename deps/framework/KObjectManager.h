﻿#pragma once

#include <unordered_map>
#include "KDataDefine.h"

class KObject
{
public:
    KObject(EObjectType type = KModuleObj);
    int type() const;
    void setId(const std::string &id);
    const std::string &id() const;
    void setName(const std::string &name);
    const std::string &name() const;
    bool enable() const;
    void setEnable(bool enable);
    virtual int run() = 0;

protected:
    bool _enable = true;
    EObjectType _type;
    std::string _id;
    std::string _name;
};

//////////////////////////////

class KObjectManager
{
public:
    KObjectManager();

    bool addObject(const std::string &id, KObject *obj);
    KObject *obj(const std::string &id);
    const std::unordered_map<std::string, KObject *> &allObjs() const;

private:
    std::unordered_map<std::string, KObject *> mObjectMap;
};
