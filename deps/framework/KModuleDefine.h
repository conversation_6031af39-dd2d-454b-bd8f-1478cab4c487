﻿#pragma once

#include "KDataInclude.h"
#include "algorithm/KAlgorithmResultManager.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KObjectManager.h"

//模块，每一种操作代表一种模块，可以是具体的算法，io操作，分支流程等
// module 内部可以加或者设置chain，这样就可以在分支模块里根据执行结果来执行不同的chain
// 也就是一个算法里可以有多个chain，一个chain可以包含多个module，一个module也可以包含多个chain
// module里的public成员要隐藏起来，改为private，根据iD去获取成员

class KChain;
class KModulePrivate;
class KModule : public KObject
{
public:
    KModule();

    KDataCollection &params();
    const KDataCollection &params() const;
    KDataCollection &result();
    const KDataCollection &result() const;

    KDataBase *getParamData(const std::string &id);
    KDataBase *getResultData(const std::string &id);

    void setModuleType(int type);
    int moduleType() const;

protected:
    KModulePrivate *p = nullptr;
};
