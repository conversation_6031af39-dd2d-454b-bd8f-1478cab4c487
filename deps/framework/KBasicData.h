﻿#pragma once

#include "KDataDefine.h"

class KBool : public KDataBase
{
public:
    KBool(bool val = false);
    int type() const;
    bool bValue() const;
    void setValue(bool b);

    virtual bool setValue(void *value);

private:
    bool _b;
};

class KInt : public KDataBase
{
public:
    KInt(int val = 0);
    int type() const;
    int iValue() const;
    bool setValue(int i);
    void setRange(int min, int max);
    virtual bool setValue(void *value);

private:
    int _min = -2147483648;
    int _max = 2147483647;
    int _i;
};

class KFloat : public KDataBase
{
public:
    KFloat(float val = 0);
    int type() const;
    float fValue() const;
    bool setValue(float f);
    void setRange(float min, float max);
    virtual bool setValue(void *value);
private:
    float _min = -3.40282e+38;
    float _max =  3.40282e+38;
    float _f;
};

class KDouble : public KDataBase
{
public:
    KDouble(double val = 0);

    int type() const;
    double dValue() const;
    bool setValue(double d);
    void setRange(double min, double max);
    virtual bool setValue(void *value);

private:
    double _min = -3.40282e+38;
    double _max = 3.40282e+38;
    double _d;
};

class KString : public KDataBase
{
public:
    KString();
    KString(std::string &s);

    int type() const;
    const std::string &stringValue() const;
    bool setStr(std::string s);
    virtual bool setValue(void *value);

private:
    std::string _str;
};
