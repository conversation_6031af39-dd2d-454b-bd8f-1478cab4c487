﻿#pragma once

#include "KDataDefine.h"
#include "algorithm/KSmartParam.h"

class KSmartParamAIData : public KDataBase
{
public:
    KSmartParamAIData();
    KSmartParamAIData(KSmartParamAI *ai);
    void setAIParam(KSmartParamAI *ai);
    int type() const override;
    KSmartParamAI *smartAiData() const;

    const std::string &modelPath() const;
    bool setModel(const std::string &path);
    virtual bool setValue(void *value) override;

private:
    std::string _modelPath;
    KSmartParamAI *_ai = nullptr;
};
