﻿#include "KCondition.h"
#include <algorithm>

using std::string;

KCondition::KCondition()
{
}

void KCondition::setId(const string &id)
{
    _id = id;
}

const string &KCondition::id() const
{
    return _id;
}

void KCondition::setName(const string &name)
{
    _name = name;
}

const string &KCondition::name() const
{
    return _name;
}

KInt &KCondition::retData()
{
    return _ret;
}

EConditionType KCondition::condType() const
{
    return _condType;
}

KCompareCondition::KCompareCondition()
    : KCondition()
{
    _condType = CompareCond;
}

int KCompareCondition::result()
{
    if (!_srcData || (!_targetData && _targetIsBinding))
        return Cond_error;
    double src = 0;
    double dst = 0;
    switch (_srcData->type())
    {
    case KData_Int:
        src = ((KInt *)_srcData)->iValue();
        break;
    case KData_Float:
        src = ((KFloat *)_srcData)->fValue();
        break;
    case KData_Double:
        src = ((KDouble *)_srcData)->dValue();
        break;
    }
    if (!_targetIsBinding)
        dst = _targetValue;
    else
    {
        switch (_targetData->type())
        {
        case KData_Int:
            dst = ((KInt *)_targetData)->iValue();
            break;
        case KData_Float:
            dst = ((KFloat *)_targetData)->fValue();
            break;
        case KData_Double:
            dst = ((KDouble *)_targetData)->dValue();
            break;
        }
    }

    switch (_type)
    {
    case Compare_equal: //
        _ret.setValue(src == dst);
        break;
    case Compare_bigger:
        _ret.setValue(src > dst);
        break;
    case Compare_smaller:
        _ret.setValue(src < dst);
        break;
    case Compare_biggerEqual:
        _ret.setValue(src >= dst);
        break;
    case Compare_smallerEqual:
        _ret.setValue(src <= dst);
        break;
    default:
        break;
    }

    return _ret.iValue();

    //    if (!_srcData || !_targetData || _srcData->type() != _targetData->type())
    //        return Cond_error;
    //    switch (_type)
    //    {
    //    case Compare_equal:
    //    {
    //        switch (_srcData->type())
    //        {
    //        case KData_Int:
    //            _ret.setValue(((KInt *)_srcData)->iValue() == ((KInt *)_targetData)->iValue());
    //            break;
    //        case KData_Float:
    //            _ret.setValue(((KFloat *)_srcData)->fValue() == ((KFloat *)_targetData)->fValue());
    //            break;
    //        case KData_Double:
    //            _ret.setValue(((KDouble *)_srcData)->dValue() == ((KDouble *)_targetData)->dValue());
    //            break;
    //        }
    //    }
    //    break;
    //    case Compare_bigger:
    //    {
    //        switch (_srcData->type())
    //        {
    //        case KData_Int:
    //            _ret.setValue(((KInt *)_srcData)->iValue() > ((KInt *)_targetData)->iValue());
    //            break;
    //        case KData_Float:
    //            _ret.setValue(((KFloat *)_srcData)->fValue() > ((KFloat *)_targetData)->fValue());
    //            break;
    //        case KData_Double:
    //            _ret.setValue(((KDouble *)_srcData)->dValue() > ((KDouble *)_targetData)->dValue());
    //            break;
    //        }
    //    }
    //    break;
    //    case Compare_smaller:
    //    {
    //        switch (_srcData->type())
    //        {
    //        case KData_Int:
    //            _ret.setValue(((KInt *)_srcData)->iValue() < ((KInt *)_targetData)->iValue());
    //            break;
    //        case KData_Float:
    //            _ret.setValue(((KFloat *)_srcData)->fValue() < ((KFloat *)_targetData)->fValue());
    //            break;
    //        case KData_Double:
    //            _ret.setValue(((KDouble *)_srcData)->dValue() < ((KDouble *)_targetData)->dValue());
    //            break;
    //        }
    //    }
    //    break;
    //    default:
    //        break;
    //    }

    //    return _ret.iValue();
}

void KCompareCondition::setCompareType(ECompareType type)
{
    _type = type;
}

void KCompareCondition::setSrcData(KDataBase *data)
{
    _srcData = data;
}

void KCompareCondition::setTargetData(KDataBase *data)
{
    _targetData = data;
}

void KCompareCondition::setTargetValue(double value)
{
    _targetValue = value;
}

void KCompareCondition::setDstDataBinding(bool isBinding)
{
    _targetIsBinding = isBinding;
}

KLogicCondition::KLogicCondition()
    : KCondition()
{
    _condType = LogicCond;
}

int KLogicCondition::result()
{
    switch (_type)
    {
    case Logic_and:
    {
        for (const KBool *b : _datas)
        {
            if (!b->bValue())
            {
                _ret.setValue(Cond_false);
                break;
            }
        }
        for (KCondition *c : _conds)
        {
            if (!c->result())
            {
                _ret.setValue(Cond_false);
                break;
            }
        }
        _ret.setValue(Cond_true);
        break;
    }
    break;
    case Logic_or:
    {
        for (const KBool *b : _datas)
        {
            if (b->bValue())
            {
                _ret.setValue(Cond_true);
                break;
            }
        }
        for (KCondition *c : _conds)
        {
            if (c->result())
            {
                _ret.setValue(Cond_true);
                break;
            }
        }
        _ret.setValue(Cond_false);
        break;
    }
    break;
    case Logic_not:
        if (_datas.empty() && _conds.empty())
            _ret.setValue(Cond_false);
        else
        {
            if (!_datas.empty())
                _ret.setValue(!_datas[0]->bValue());
            else if (!_conds.empty())
                _ret.setValue(!_conds[0]->result());
        }
        break;
    default:
        break;
    }
    return _ret.iValue();
}

void KLogicCondition::setLogicType(ELogicType type)
{
    _type = type;
}

bool KLogicCondition::addData(KDataBase *data)
{
    if (!data || data->type() != KData_Bool)
        return false;
    _datas.push_back((KBool *)data);
    return true;
}

bool KLogicCondition::addCondition(KCondition *cond)
{
    if (!cond)
        return false;
    _conds.push_back((KCondition *)cond);
    return true;
}

KSwitchCondition::KSwitchCondition()
{
    _condType = SwitchCond;
}

bool KSwitchCondition::setData(KDataBase *data)
{
    if (!data || data->type() != KData_Int)
        return false;
    _data = (KInt *)data;
    return true;
}

int KSwitchCondition::result()
{
    if (!_data)
        return -1;
    return _data->iValue();
}

KConditionManager::KConditionManager()
{
}

bool KConditionManager::addCondition(const string &id, KCondition *cond)
{
    if (id.empty() || !cond || mCondMap.find(id) != mCondMap.end())
        return false;
    mCondMap[id] = cond;
    return true;
}

KCondition *KConditionManager::conidtion(const string &id)
{
    if (id.empty() || mCondMap.find(id) == mCondMap.end())
        return nullptr;
    return mCondMap[id];
}

KMultiLogicCondition::KMultiLogicCondition()
    : KCondition()
{
    _condType = MultiCond;
}

int KMultiLogicCondition::result()
{
    if (_conds.empty())
        return Cond_error;

    int ret = Cond_false;
    for (size_t i = 0; i < _conds.size(); ++i)
    {
        ret = _conds[i]->result();
        if (0 > ret)
            return ret;
    }
    return ret; // 前面的都只要计算出结果，给后面的条件用，返回的是最后一个条件的结果
}

bool KMultiLogicCondition::addCondition(KCondition *cond)
{
    if (!cond)
        return false;
    _conds.push_back((KCondition *)cond);
    return true;
}

bool KMultiLogicCondition::removeCondition(KCondition *cond)
{
    auto iter = std::find(_conds.begin(), _conds.end(), cond);
    if (iter == _conds.end())
        return false;
    _conds.erase(iter);
    return true;
}
