﻿#include "KModuleDefine.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/KImageMarker.h"
#include "KFactory.h"

class KModulePrivate
{
public:
    KModulePrivate()
    {
    }

public:
    int _moduleType = NormalModule;
    KDataCollection _in;
    KDataCollection _out;
};

KModule::KModule()
    : KObject()
{
    p = new KModulePrivate();
}

KDataCollection &KModule::params()
{
    return p->_in;
}
const KDataCollection &KModule::params() const
{
    return p->_in;
}

KDataCollection &KModule::result()
{
    return p->_out;
}
const KDataCollection &KModule::result() const
{
    return p->_out;
}

KDataBase *KModule::getParamData(const string &id)
{
    return p->_in.data(id);
}

KDataBase *KModule::getResultData(const string &id)
{
    return p->_out.data(id);
}

void KModule::setModuleType(int type)
{
    p->_moduleType = type;
}

int KModule::moduleType() const
{
    return p->_moduleType;
}
