﻿#include "KControllerAIEngineManager.h"
#include "algorithm/KAIEngineModel.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/tools/KCryptography.h"
#include <iostream>
#include <QDebug>
#include "aiEngine/KOnnxEncrypt.h"
#include <unistd.h>
#include <random>
#include <sstream>
#include <iomanip>

std::string generate_uuid()
{
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    std::stringstream ss;
    for (int i = 0; i < 16; ++i)
    {
        int byte = dis(gen);
        ss << std::hex << std::setw(2) << std::setfill('0') << byte;
        if (i == 3 || i == 5 || i == 7 || i == 9)
        {
            ss << "-";
        }
    }
    return ss.str();
}

class KControllerAIEngineManagerPrivate
{
public:
    KControllerAIEngineManagerPrivate()
        : _AIEngineManager(KAIEngineModelManager::singleInstance())
    {
    }

    KAIEngineModel *findAIEngine(const std::string &modelName)
    {
        for (KAIEngineModel *ai : _AIEngineManager->engines())
        {
            if (modelName == ai->engine()->path())
            {
                return ai;
            }
        }
        return nullptr;
    }

    KAIEngineModel *createAIEngine(const std::string &modelName)
    {
        std::cout << "KAIEngineModel *createAIEngine start" << std::endl;
        KAIEngineModel *aiModel = nullptr;
        if (0 == access(modelName.c_str(), F_OK))
        {
            std::cout << "KAIEngineModel *createAIEngine in if" << std::endl;
            //            KAIEngine *e = KSystemConfigManager::singleInstance()->currentConfig()->createEngine(
            //                StdStr2QStr(KCryptography::createGuid()));
            KAIEngine *e = new KBitlandAIEngine(modelName);

            e->setName(e->id());
            e->setInstruction(e->id());

            aiModel = _AIEngineManager->add(e);
            e->setPath(modelName);
        }
        std::cout << "KAIEngineModel *createAIEngine finish" << std::endl;
        return aiModel;
    }

public:
    KAIEngineModelManager *_AIEngineManager;
};

/////////////////////////////////////////////////////////////////////////////////
KControllerAIEngineManager *KControllerAIEngineManager::singleInstance()
{
    static std::shared_ptr<KControllerAIEngineManager> instance = std::make_shared<KControllerAIEngineManager>();
    return instance.get();
}

KControllerAIEngineManager::KControllerAIEngineManager() : p(0)
{
    CREATE_COMMON_PTR(p, KControllerAIEngineManagerPrivate);
}

KControllerAIEngineManager::~KControllerAIEngineManager()
{
    RELEASE_COMMON_PTR(p);
}

bool KControllerAIEngineManager::createAIEngine(const std::string &modelpath)
{
    if (p->findAIEngine(modelpath))
    {
        return true;
    }

    return (nullptr != p->createAIEngine(modelpath));
}

KAIEngineInferEnity *KControllerAIEngineManager::createEnity(const std::string &modelpath)
{
    KAIEngineModel *aimodel = p->findAIEngine(modelpath);
    if (!aimodel)
        aimodel = p->createAIEngine(modelpath);
    if (aimodel)
    {
        KAIEngineInferEnity *enity = aimodel->createEnity(QString::fromStdString(generate_uuid())); // engine用modelpath, enity要用不同的id
        //        KAIEngineInferEnity *enity = aimodel->createEnity(aimodel->id());
        if (enity)
            return enity;

        p->_AIEngineManager->remove(aimodel->id());
        return nullptr;
    }
    return nullptr;
}
