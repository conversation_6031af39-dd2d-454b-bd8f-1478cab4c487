﻿#pragma once

#include "KDataDefine.h"
#include "algorithm/KImageFrame.h"
#include "algorithm/KAlgorithm.h"
#include "algorithm/KImage2.h"
#include "algorithm/KAlgorithmShape.h"

class KImageData : public KDataBase
{
public:
    int type() const;
    unsigned char *data();
    int width() const;
    int height() const;
    void setWidth(int w);
    void setHeight(int h);
    void load(const std::string &filename);
    void save(const std::string &filename);
    KImageData *image() const;

private:
    int _t;
    int _w;
    int _h;
    std::vector<unsigned char> _buffer;
};

class KImage2Data : public KDataBase
{
public:
    KImage2Data();
    KImage2Data(int width, int height, int type, void *buffer);
    KImage2Data(cv::Mat &m);
    KImage2 &Image2();
    const KImage2 &Image2() const;
    KImage2 *KImage2Ptr();
    int type() const;

private:
    KImage2 _frame;
};

class KImage2DataVec : public KDataBase
{
public:
    KImage2DataVec();
    KImage2Data &Image2Data(int index);
    const KImage2Data &Image2Data(int index) const;
    KImage2Data *KImage2DataPtr(int index);

    int type() const;

    int size() const;
    void resize(int size);
    void addImage2Data(int index, cv::Mat &m);
    void addImage2Data(int index, KImage2 &m);
    void addImage2Data(int index, KImage2Data &m);

private:
    std::vector<KImage2Data> _image2Datas;
};

class KFrameData : public KDataBase
{
public:
    KFrameData(KImageFrame &frame);
    KFrameData(KImageFrame *frame);

    KImageFrame *framePtr() const;
    //int frameid() const;
    int type() const;

private:
    KImageFrame *_frame = nullptr;
};

class KFrameData1D : public KDataBase
{
public:
    KFrameData1D();
    KFrameData1D(KImageFrame1D &frame1D);
    KFrameData1D(KImageFrame1D *frame1D);

    KImageFrame1D *frame1DPtr() const;
    int type() const;

private:
    KImageFrame1D *_frame1D = nullptr;
};

class KFrameData2D : public KDataBase
{
public:
    KFrameData2D(KImageFrame2D &frame2D);
    KFrameData2D(KImageFrame2D *frame2D);

    KImageFrame2D *frame2DPtr() const;
    int type() const;

private:
    KImageFrame2D *_frame2D = nullptr;
};

class KRectData : public KDataBase
{
public:
    KRectData();
    KRectData(const KRect &rect);
    KRectData(const cv::Point &p1, const cv::Point &p2);
    int type() const;
    void setRect(const KRect &rect);

    int width() const;
    int height() const;

    KRect &rect();
    const KRect &rect() const;

private:
    KRect _rect;
};

class KRect1DData : public KDataBase
{
public:
    KRect1DData();
    KRect1DData(const KRect1D &rect);
    int type() const;
    void setRect(const KRect1D &rect);

    void push_back(const KRectData &rect);

    KRect1D &rect1D();
    const KRect1D &rect1D() const;

private:
    KRect1D _rect;
};
