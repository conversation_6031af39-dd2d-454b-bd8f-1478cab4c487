﻿#include "KBitlandData.h"

KBmImageData::KBmImageData()
{
}

KBmImageData::KBmImageData(void *image)
    : KDataBase()
{
    _image = image;
}

void KBmImageData::setBmImage(void *image)
{
    _image = image;
}

void *KBmImageData::bmImagePtr() const
{
    if (_o)
        return ((KBmImageData *)(_o))->bmImagePtr();
    return _image;
}

int KBmImageData::type() const
{
    return KData_BmImagePtr;
}

KAiInferData::KAiInferData()
{
}

KAiInferData::KAiInferData(void *data)
    : KDataBase()
{
    _data = data;
}

void KAiInferData::setInferData(void *data)
{
    _data = data;
}

void *KAiInferData::inferData() const
{
    if (_o)
        return ((KAiInferData *)(_o))->inferData();
    return _data;
}

int KAiInferData::type() const
{
    return KData_AiInferDataPtr;
}

KImageMarkerData::KImageMarkerData()
    : KDataBase()
{
}

int KImageMarkerData::type() const
{
    return KData_VoidPtr;
}

void KImageMarkerData::setImageMarker(void *maker)
{
    _imageMarker = maker;
}

void *KImageMarkerData::imageMarker()
{
    if (_o)
        return ((KImageMarkerData *)(_o))->imageMarker();
    return _imageMarker;
}

KAIEngineInferEnityData::KAIEngineInferEnityData()
    : KDataBase()
{
}

int KAIEngineInferEnityData::type() const
{
    return KData_VoidPtr;
}

void KAIEngineInferEnityData::setEnity(void *enity)
{
    _enity = enity;
}

void *KAIEngineInferEnityData::enity()
{
    if (_o)
        return ((KAIEngineInferEnityData *)(_o))->enity();
    return _enity;
}
