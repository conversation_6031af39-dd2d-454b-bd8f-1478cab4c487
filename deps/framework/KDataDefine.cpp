﻿#include "KDataDefine.h"

KDataBase::KDataBase()
    : _v(0), _o(0)
{
}

KDataBase::~KDataBase()
{
}

bool KDataBase::setValue(void *value)
{
    return false;
}

const KDataBase *KDataBase::value() const
{
    if (_o)
        return _o;
    return this;
}

bool KDataBase::bind(KDataBase *val)
{
    if (val->type() != type())
    {
        printf("KDataBase::bind failed, val type = %d, this type = %d\n", val->type(), type());
        return false;
    }

    _o = val;
    return true;
}
void KDataBase::unBind()
{
    _o = nullptr;
}
bool KDataBase::isBind()
{
    return _o != nullptr;
}

void KDataBase::setInfo(const KDataInfo &info)
{
    _info = info;
}

const KDataInfo &KDataBase::info() const
{
    if (_o)
        return _o->info();
    return _info;
}

void KDataBase::setId(const std::string &id)
{
    _info.Id = id;
}

const std::string &KDataBase::id() const
{
    if (_o)
        return _o->id();
    return _info.Id;
}

void KDataBase::setName(const std::string &name)
{
    _info.Name = name;
}

const std::string &KDataBase::name() const
{
    if (_o)
        return _o->name();
    return _info.Name;
}

void KDataBase::setComment(const std::string &comment)
{
    _info.Comment = comment;
}

const std::string &KDataBase::comment() const
{
    if (_o)
        return _o->comment();
    return _info.Comment;
}

/////////////////////////////////////////////
class KDataCollectionPrivate
{
public:
    std::map<std::string, KDataBase *> _container;
};

KDataCollection::KDataCollection()
{
    p = new KDataCollectionPrivate();
}

bool KDataCollection::add(const std::string &id, KDataBase *b)
{
    if (p->_container.find(id) != p->_container.end())
        return false;
    b->setId(id);
    b->setName(id);
    p->_container[id] = b;
    return true;
}
void KDataCollection::remove(const std::string &id)
{
    auto iter = p->_container.find(id);
    if (iter != p->_container.end())
        p->_container.erase(iter);
}

bool KDataCollection::isEmpty() const
{
    return p->_container.empty();
}
int KDataCollection::count() const
{
    return p->_container.size();
}

std::map<std::string, KDataBase *> KDataCollection::all() const
{
    return p->_container;
}

KDataBase *KDataCollection::data(const std::string &id) const
{
    if (p->_container.find(id) != p->_container.end())
        return p->_container[id];

    printf("KDataCollection::data return nullptr, id = %s \n all params:\n", id.c_str());
    for (auto iter = p->_container.begin(); iter != p->_container.end(); ++iter)
        printf("%s, ", iter->first.c_str());
    printf("\n");
    return nullptr;
}
