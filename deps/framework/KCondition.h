﻿#pragma once
#include "KBasicData.h"
#include <vector>
#include <any>
#include <unordered_map>

// 跳转条件，根据设置好的条件去执行不同的 chain
// 基本条件，类似于基本数据类型，比如 == 或者 != 或者 > 这种单个判断条件，就是一个condition
// (<10 || >20)这种，就是3个condition的组合,类似于一个chain
// > == < != >= <= && || !

class KCondition
{
public:
    KCondition();
    virtual int result() = 0;

    void setId(const std::string &id);
    const std::string &id() const;

    void setName(const std::string &name);
    const std::string &name() const;

    KInt &retData();
    EConditionType condType() const;

protected:
    std::string _id;
    std::string _name;
    KInt _ret = Cond_true;
    EConditionType _condType = CompareCond;
};

class KConditionManager
{
public:
    KConditionManager();

    bool addCondition(const std::string &id, KCondition *cond);
    KCondition *conidtion(const std::string &id);

private:
    std::unordered_map<std::string, KCondition *> mCondMap;
};

class KCompareCondition : public KCondition
{
public:
    KCompareCondition();
    virtual int result() override;
    void setCompareType(ECompareType type);
    void setSrcData(KDataBase *data);
    void setTargetData(KDataBase *data);
    void setTargetValue(double value);
    void setDstDataBinding(bool isBinding);

private:
    bool _targetIsBinding = false;
    ECompareType _type = Compare_equal;
    KDataBase *_srcData = nullptr;
    KDataBase *_targetData = nullptr;
    double _targetValue = 0; // 比较的对象可能是一个设置的值
};

class KLogicCondition : public KCondition
{
public:
    KLogicCondition();
    virtual int result() override;
    void setLogicType(ELogicType type);
    bool addData(KDataBase *data);
    bool addCondition(KCondition *cond); //可以根据多个condition的结果来逻辑组合

private:
    ELogicType _type = Logic_and;
    std::vector<KBool *> _datas;
    std::vector<KCondition *> _conds;
};

class KSwitchCondition : public KCondition // 先按照两个数据同类型来写
{
public:
    KSwitchCondition();
    bool setData(KDataBase *data);
    virtual int result() override;

private:
    KInt *_data = nullptr;
};

class KMultiLogicCondition : public KCondition
{
public:
    KMultiLogicCondition();        //根据多个condition的结果来逻辑组合，最后的一个条件的结果就是这个condition的结果
    virtual int result() override; // 从前往后计算。返回最后一个的结果
    bool addCondition(KCondition *cond);
    bool removeCondition(KCondition *cond);

private:
    std::vector<KCondition *> _conds;
};
