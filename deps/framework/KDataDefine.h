﻿#pragma once

#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <map>
#include <queue>
#include <thread>
#include "common/KAssembleAlgorithmDefine.h"

enum EObjectType
{
    KModuleObj,
    KChainObj,
};

#ifdef DOUBLE_REJECT_OUTPORT
enum Algo_Return_Code
{
    Algo_Return_Segment_A = 8,
    Algo_Return_Classify_A,
    Algo_Return_ClassifyType_A, // 类别分类错误
    Algo_Return_Abnormal_A,
    Algo_Return_Defect_A,
    Algo_Return_Dirty_A,
    Algo_Return_Recognize_ModelNum_A, // 严重不良品
    Algo_Return_Segment = 1000,
    Algo_Return_Classify,
    Algo_Return_ClassifyType,
    Algo_Return_Abnormal,
    Algo_Return_Defect,
    Algo_Return_Dirty,
    Algo_Return_Recognize_ModelNum,
};
#else
enum Algo_Return_Code
{
    Algo_Return_Segment = 1000,
    Algo_Return_Classify,     // 分类错误
    Algo_Return_ClassifyType, // 类别分类错误
    Algo_Return_Abnormal,
    Algo_Return_Defect,
    Algo_Return_Dirty,
    Algo_Return_Recognize_ModelNum, // 模号错误
};

#endif //

struct KDataInfo
{
    KDataInfo() {}
    KDataInfo(const std::string &id, const std::string &name)
        : Id(id), Name(name) {}

    std::string Id;
    std::string Name;
    std::string Comment; // 释义
};

//所有参数基类
class KDataBase
{
public:
    KDataBase();
    virtual ~KDataBase();

public:
    virtual int type() const = 0;
    virtual bool setValue(void *value);

    const KDataBase *value() const;
    bool bind(KDataBase *val);
    void unBind();
    bool isBind();

    void setInfo(const KDataInfo &info);
    const KDataInfo &info() const;

    void setId(const std::string &id);
    const std::string &id() const;

    void setName(const std::string &name);
    const std::string &name() const;

    void setComment(const std::string &comment);
    const std::string &comment() const;

protected:
    void *_v = nullptr;
    KDataInfo _info;
    KDataBase *_o = nullptr;
};

//参数集合
class KDataCollectionPrivate;
class KDataCollection
{
public:
    KDataCollection();

public:
    bool add(const std::string &id, KDataBase *b);
    void remove(const std::string &id);
    bool isEmpty() const;
    int count() const;
    std::map<std::string, KDataBase *> all() const;
    KDataBase *data(const std::string &id) const;

private:
    KDataCollectionPrivate *p;
};
