﻿#include "KBasicData.h"

KInt::KInt(int val)
    : _i(val)
{
    _v = &_i;
}

int KInt::type() const
{
    return KData_Int;
}

int KInt::iValue() const
{
    if (_o)
        return ((KInt *)(_o))->iValue();

    return _i;
}

bool KInt::setValue(int i)
{
    if (i < _min || i > _max)
        return false;
    _i = i;
    return true;
}

void KInt::setRange(int min, int max)
{
    _min = min;
    _max = max;
}

bool KInt::setValue(void *value)
{
    if (!value)
        return false;
    return setValue(*static_cast<int *>(value));
}

KDouble::KDouble(double d)
    : _d(d)
{
    _v = (&_d);
}

int KDouble::type() const
{
    return KData_Double;
}

double KDouble::dValue() const
{
    if (_o)
        return ((KDouble *)(_o))->dValue();

    return _d;
}

bool KDouble::setValue(double d)
{
    if (d < _min || d > _max)
        return false;
    _d = d;
    return true;
}

void KDouble::setRange(double min, double max)
{
    _min = min;
    _max = max;
}

bool KDouble::setValue(void *value)
{
    if (!value)
        return false;
    return setValue(*static_cast<double *>(value));
}

KBool::KBool(bool val)
    : _b(val)
{
    _v = (&_b);
}

int KBool::type() const
{
    return KData_Bool;
}

bool KBool::bValue() const
{
    if (_o)
        return ((KBool *)(_o))->bValue();

    return _b;
}

void KBool::setValue(bool b)
{
    _b = b;
}

bool KBool::setValue(void *value)
{
    if (!value)
        return false;
    setValue(*static_cast<bool *>(value));
    return true;
}

KFloat::KFloat(float val)
    : _f(val)
{
}

int KFloat::type() const
{
    return KData_Float;
}

float KFloat::fValue() const
{
    if (_o)
        return ((KFloat *)(_o))->fValue();
    return _f;
}

bool KFloat::setValue(float f)
{
    if (f < _min || f > _max)
        return false;
    _f = f;
    return true;
}

void KFloat::setRange(float min, float max)
{
    _min = min;
    _max = max;
}

bool KFloat::setValue(void *value)
{
    if (!value)
        return false;
    return setValue(*static_cast<float *>(value));
}

KString::KString()
{
}

KString::KString(std::string &s)
    : _str(s)
{
}

int KString::type() const
{
    return KData_String;
}

const std::string &KString::stringValue() const
{
    if (_o)
        return ((KString *)(_o))->stringValue();
    return _str;
}

bool KString::setStr(std::string s)
{
    _str = s;
    return true;
}

bool KString::setValue(void *value)
{
    if (!value)
        return false;
    return setStr(*static_cast<std::string *>(value));
}
