﻿#pragma once
#include "KDataDefine.h"
#include "algorithm/KImageMarker.h"

class KBmImageData : public KDataBase
{
public:
    KBmImageData();
    KBmImageData(void *image);

    void setBmImage(void *image);
    void *bmImagePtr() const;
    int type() const;

private:
    void *_image = nullptr;
};

class KAiInferData : public KDataBase
{
public:
    KAiInferData();
    KAiInferData(void *data);

    void setInferData(void *data);
    void *inferData() const;
    int type() const;

private:
    void *_data = nullptr;
};

class KImageMarkerData : public KDataBase
{
public:
    KImageMarkerData();
    int type() const;
    void setImageMarker(void *maker);
    void *imageMarker();

private:
    void *_imageMarker = nullptr;
};

class KAIEngineInferEnityData : public KDataBase
{
public:
    KAIEngineInferEnityData();
    int type() const;
    void setEnity(void *enity);
    void *enity();

private:
    void *_enity = nullptr;
};
