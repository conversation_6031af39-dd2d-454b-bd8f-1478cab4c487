﻿#include "KSmartData.h"
#include "KControllerAIEngineManager.h"

KSmartParamAIData::KSmartParamAIData()
    : KDataBase()
{
}

KSmartParamAIData::KSmartParamAIData(KSmartParamAI *ai)
    : KDataBase(), _ai(ai)
{
}

void KSmartParamAIData::setAIParam(KSmartParamAI *ai)
{
    _ai = ai;
}

int KSmartParamAIData::type() const
{
    return KData_SmartAi;
}

KSmartParamAI *KSmartParamAIData::smartAiData() const
{
    if (_o)
        return ((KSmartParamAIData *)(_o))->smartAiData();
    return _ai;
}

const std::string &KSmartParamAIData::modelPath() const
{
    return _modelPath;
}

bool KSmartParamAIData::setModel(const std::string &path)
{
    _modelPath = path;

    //    std::string aipath = "./aimodels/" + path;
    KAIEngineInferEnity *e = KControllerAIEngineManager::singleInstance()->createEnity(path);
    return smartAiData()->setEnity(e);
}

bool KSmartParamAIData::setValue(void *value)
{
    if (!value)
        return false;
    return setModel(static_cast<char *>(value));
}
