﻿#pragma once

#include <unordered_map>
#include <functional>

class KDataBase;
class KModule;
class KChain;
class KCondition;

using CreateKModule = std::function<KModule *()>;

#define KDataFac KDataFactory::singleInstance()
class KDataFactory
{
public:
    static KDataFactory *singleInstance();
    KDataBase *createData(int type);
    bool setDataValue(KDataBase *data, void *value);
};

#define KCondFac KConditionFactory::singleInstance()
class KConditionFactory
{
public:
    static KConditionFactory *singleInstance();
    KCondition *createCondition(int type);
};

#define KModFac KModuleFactory::singleInstance()
class KModuleFactory
{
public:
    static KModuleFactory *singleInstance();
    KModule *createModule(const std::string &className);
    bool registerModule(const std::string &className, CreateKModule createFunc);

private:
    std::unordered_map<std::string, CreateKModule> mCreateMap;
};

#define REGISTER_MODULE(className)                          \
    namespace                                               \
    { /* 匿名namespace避免跨文件冲突 */            \
    void __register_##className()                           \
    {                                                       \
        KModuleFactory::singleInstance()->registerModule(   \
            #className, []() { return new className; });    \
    }                                                       \
    struct __dummy_##className                              \
    {                                                       \
        __dummy_##className() { __register_##className(); } \
    } __dummy_instance_##className;                         \
    }

//// 辅助宏：拼接类名和行号
//#define CONCAT(a, b) CONCAT_IMPL(a, b)
//#define CONCAT_IMPL(a, b) a##b

//// 主注册宏
//#define REGISTER_MODULE(className)                                                                    \
//    static int CONCAT(__register_, CONCAT(className, __LINE__))(int)                                  \
//    {                                                                                                 \
//        KModuleFactory::singleInstance()->registerModule(#className, []() { return new className; }); \
//        return 0;                                                                                     \
//    }                                                                                                 \
//    static int CONCAT(__dummy_, CONCAT(className, __LINE__)) = CONCAT(__register_, CONCAT(className, __LINE__))(0);
