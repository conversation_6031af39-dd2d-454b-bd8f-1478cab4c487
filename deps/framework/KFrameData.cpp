﻿#include "KFrameData.h"

int KImageData::type() const
{
    return KData_Image;
}

unsigned char *KImageData::data()
{
    return _buffer.data();
}

int KImageData::width() const
{
    return _w;
}

int KImageData::height() const
{
    return _h;
}

void KImageData::setWidth(int w)
{
    _w = w;
}

void KImageData::setHeight(int h)
{
    _h = h;
}

void KImageData::load(const std::string &filename)
{
}

void KImageData::save(const std::string &filename)
{
}

KImageData *KImageData::image() const
{
    return const_cast<KImageData *>((KImageData *)value());
}

KFrameData::KFrameData(KImageFrame &frame)
    : KDataBase()
{
    _frame = &frame;
}

KFrameData::KFrameData(KImageFrame *frame)
    : KDataBase()
{
    _frame = frame;
}

KImageFrame *KFrameData::framePtr() const
{
    if (_o)
        return ((KFrameData *)(_o))->framePtr();
    return _frame;
}

int KFrameData::type() const
{
    return KData_FramePtr;
}

KFrameData1D::KFrameData1D()
    : KDataBase()
{
}

KFrameData1D::KFrameData1D(KImageFrame1D &frame1D)
    : KDataBase()
{
    _frame1D = &frame1D;
}

KFrameData1D::KFrameData1D(KImageFrame1D *frame1D)
    : KDataBase()
{
    _frame1D = frame1D;
}

KImageFrame1D *KFrameData1D::frame1DPtr() const
{
    if (_o)
        return ((KFrameData1D *)(_o))->frame1DPtr();
    return _frame1D;
}

int KFrameData1D::type() const
{
    return KData_Frame1DPtr;
}

KFrameData2D::KFrameData2D(KImageFrame2D &frame2D)
    : KDataBase()
{
    _frame2D = &frame2D;
}

KFrameData2D::KFrameData2D(KImageFrame2D *frame2D)
    : KDataBase()
{
    _frame2D = frame2D;
}

KImageFrame2D *KFrameData2D::frame2DPtr() const
{
    if (_o)
        return ((KFrameData2D *)(_o))->frame2DPtr();
    return _frame2D;
}

int KFrameData2D::type() const
{
    return KData_Frame2DPtr;
}

KImage2Data::KImage2Data()
    : KDataBase()
{
}

KImage2Data::KImage2Data(int width, int height, int type, void *buffer)
    : KDataBase()
{
    _frame = KImage2(width, height, type, buffer);
}

KImage2Data::KImage2Data(cv::Mat &m)
{
    _frame = KImage2(m);
}

KImage2 &KImage2Data::Image2()
{
    if (_o)
        return ((KImage2Data *)(_o))->Image2();
    return _frame;
}

const KImage2 &KImage2Data::Image2() const
{
    if (_o)
        return ((KImage2Data *)(_o))->Image2();
    return _frame;
}

KImage2 *KImage2Data::KImage2Ptr()
{
    if (_o)
        return ((KImage2Data *)(_o))->KImage2Ptr();
    return &_frame;
}

int KImage2Data::type() const
{
    return KData_Image2;
}

KRectData::KRectData()
    : KDataBase()
{
}

KRectData::KRectData(const KRect &rect)
    : KDataBase()
{
    _rect = rect;
}

KRectData::KRectData(const cv::Point &p1, const cv::Point &p2)
    : KDataBase()
{
    _rect = KRect(p1, p2);
}

int KRectData::type() const
{
    return KData_Rect;
}

void KRectData::setRect(const KRect &rect)
{
    _rect = rect;
}

int KRectData::width() const
{
    if (_o)
        return ((KRectData *)(_o))->width();
    return _rect.width;
}

int KRectData::height() const
{
    if (_o)
        return ((KRectData *)(_o))->height();
    return _rect.height;
}

KRect &KRectData::rect()
{
    if (_o)
        return ((KRectData *)(_o))->rect();
    return _rect;
}

const KRect &KRectData::rect() const
{
    if (_o)
        return ((KRectData *)(_o))->rect();
    return _rect;
}

KRect1DData::KRect1DData()
    : KDataBase()
{
}

KRect1DData::KRect1DData(const KRect1D &rect)
    : KDataBase()
{
    _rect = rect;
}

int KRect1DData::type() const
{
    return KData_Rect1D;
}

void KRect1DData::setRect(const KRect1D &rect)
{
    _rect = rect;
}

void KRect1DData::push_back(const KRectData &rect)
{
    if (_o)
        ((KRect1DData *)(_o))->push_back(rect);
    _rect.push_back(rect.rect());
}

KRect1D &KRect1DData::rect1D()
{
    if (_o)
        return ((KRect1DData *)(_o))->rect1D();
    return _rect;
}

const KRect1D &KRect1DData::rect1D() const
{
    if (_o)
        return ((KRect1DData *)(_o))->rect1D();
    return _rect;
}

KImage2DataVec::KImage2DataVec()
    : KDataBase()
{
}

KImage2Data &KImage2DataVec::Image2Data(int index)
{
    if (_o)
        return ((KImage2DataVec *)(_o))->Image2Data(index);
    if (index < _image2Datas.size())
        return _image2Datas[index];
    KImage2Data temp;
    return temp;
}

const KImage2Data &KImage2DataVec::Image2Data(int index) const
{
    if (_o)
        return ((KImage2DataVec *)(_o))->Image2Data(index);
    if (index < _image2Datas.size())
        return _image2Datas[index];
    KImage2Data temp;
    return temp;
}

KImage2Data *KImage2DataVec::KImage2DataPtr(int index)
{
    if (_o)
        return ((KImage2DataVec *)(_o))->KImage2DataPtr(index);
    if (index < _image2Datas.size())
        return &_image2Datas[index];
    return nullptr;
}

int KImage2DataVec::type() const
{
    return KData_Image2Vec;
}

int KImage2DataVec::size() const
{
    return _image2Datas.size();
}

void KImage2DataVec::resize(int size)
{
    if (size != _image2Datas.size())
        _image2Datas.resize(size);
}

void KImage2DataVec::addImage2Data(int index, Mat &m)
{
    if (index >= _image2Datas.size())
        return;
    _image2Datas[index] = KImage2Data(m);
}

void KImage2DataVec::addImage2Data(int index, KImage2 &m)
{
    if (index >= _image2Datas.size())
        return;
    _image2Datas[index] = KImage2Data(m.src());
}

void KImage2DataVec::addImage2Data(int index, KImage2Data &m)
{
    if (index >= _image2Datas.size())
        return;
    _image2Datas[index] = m;
}
