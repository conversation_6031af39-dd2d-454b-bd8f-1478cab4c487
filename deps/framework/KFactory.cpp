﻿#include "KFactory.h"
#include "KDataInclude.h"
#include "KCondition.h"

KDataFactory *KDataFactory::singleInstance()
{
    static KDataFactory *ptr = new KDataFactory;
    return ptr;
}

KDataBase *KDataFactory::createData(int type)
{
    switch (type)
    {
    case KData_Int:
        return new KInt;
    case KData_Bool:
        return new KBool;
    case KData_Float:
        return new KFloat;
    case KData_Double:
        return new KDouble;
        //    case KData_Char:
        //        return new KChar;
        //    case KData_String:
        //        return new KString;
    case KData_SmartAi:
        return new KSmartParamAIData;
        //    case KData_CharPtr:
        //        return new KCharPtr;
        //    case KData_VoidPtr:
        //        return new KVoidPtr;
    case KData_Image:
        return new KImageData;
    case KData_Image2: // KImage2
        return new KImage2Data;
    case KData_Image2Vec: // KImage2 vector
        return new KImage2DataVec;
        //    case KData_Frame: // KFrame
        //        return new KFrameData;
        //    case KData_FramePtr:
        //        return new KFrameDataPtr;
    case KData_Frame1D:
        return new KFrameData1D;
        //    case KData_Frame1DPtr:
        //        return new KFrameData1DPtr;
        //    case KData_Frame2D:
        //        return new KFrameData2D;
        //    case KData_Frame2DPtr:
        //        return new KFrameData2DPtr;
    case KData_BmImagePtr:
        return new KBmImageData;
    case KData_AiInferDataPtr:
        return new KAiInferData;
    case KData_Rect:
        return new KRectData;
    case KData_Rect1D:
        return new KRect1DData;
    default:
        return nullptr;
    }
}

bool KDataFactory::setDataValue(KDataBase *data, void *value)
{
    if (!data || !value)
        return false;
    switch (data->type())
    {
    case KData_Int:
        ((KInt *)data)->setValue(*(int *)value);
        return true;
    case KData_Bool:
        ((KBool *)data)->setValue(*(bool *)value);
        return true;
    case KData_Float:
        ((KFloat *)data)->setValue(*(float *)value);
        return true;
    case KData_Double:
        ((KDouble *)data)->setValue(*(double *)value);
        return true;
        //    case KData_Char:
        //        return new KChar;
        //    case KData_String:
        //        return new KString;
    case KData_SmartAi:
        ((KSmartParamAIData *)data)->setAIParam((KSmartParamAI *)value);
        return true;
        //    case KData_CharPtr:
        //        return new KCharPtr;
        //    case KData_VoidPtr:
        //        return new KVoidPtr;
        //    case KData_Image:
        //        ((KImageData *)data)->setValue(*(int *)value);
        //        return true;
        //    case KData_Image2: // KImage2
        //        ((KImage2Data *)data)->setValue(*(int *)value);
        //        return true;
        //    case KData_Image2Vec: // KImage2 vector
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
        //    case KData_Frame: // KFrame
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
        //        //    case KData_FramePtr:
        //        //        return new KFrameDataPtr;
        //    case KData_Frame1D:
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
        //        //    case KData_Frame1DPtr:
        //        //        return new KFrameData1DPtr;
        //    case KData_Frame2D:
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
        //        //    case KData_Frame2DPtr:
        //        //        return new KFrameData2DPtr;
        //    case KData_BmImagePtr:
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
        //    case KData_AiInferDataPtr:
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
        //    case KData_Rect:
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
        //    case KData_Rect1D:
        //        ((KInt *)data)->setValue(*(int *)value);
        //        return true;
    default:
        return false;
    }
}

KModuleFactory *KModuleFactory::singleInstance()
{
    static KModuleFactory *ptr = new KModuleFactory;
    return ptr;
}

KModule *KModuleFactory::createModule(const string &className)
{
    if (className.empty() || mCreateMap.find(className) == mCreateMap.end())
        return nullptr;
    return mCreateMap[className]();
}

bool KModuleFactory::registerModule(const string &className, CreateKModule createFunc)
{
    if (className.empty() || mCreateMap.find(className) != mCreateMap.end())
    {
        printf(" ****** registe class %s failed. \n", className.c_str());
        return false;
    }
    mCreateMap[className] = createFunc;
    printf(" ====== registe class %s successful. \n", className.c_str());
}

KConditionFactory *KConditionFactory::singleInstance()
{
    static KConditionFactory *ptr = new KConditionFactory;
    return ptr;
}

KCondition *KConditionFactory::createCondition(int type)
{
    switch (type)
    {
    case CompareCond:
        return new KCompareCondition;
    case LogicCond:
        return new KLogicCondition;
    case SwitchCond:
        return new KSwitchCondition;
    case MultiCond:
        return new KMultiLogicCondition;
    }
    return nullptr;
}
