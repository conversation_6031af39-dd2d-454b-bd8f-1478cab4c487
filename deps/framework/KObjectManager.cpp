﻿#include "KObjectManager.h"

KObject::KObject(EObjectType type)
    : _type(type)
{
}

int KObject::type() const
{
    return _type;
}

void KObject::setId(const std::string &id)
{
    _id = id;
}

const std::string &KObject::id() const
{
    return _id;
}

void KObject::setName(const std::string &name)
{
    _name = name;
}

const std::string &KObject::name() const
{
    return _name;
}

bool KObject::enable() const
{
    return _enable;
}

void KObject::setEnable(bool enable)
{
    _enable = enable;
}

//////////////////////////////

KObjectManager::KObjectManager()
{
}

bool KObjectManager::addObject(const std::string &id, KObject *obj)
{
    if (id.empty() || !obj || mObjectMap.find(id) != mObjectMap.end())
        return false;
    mObjectMap[id] = obj;
    return true;
}

KObject *KObjectManager::obj(const std::string &id)
{
    if (id.empty() || mObjectMap.find(id) == mObjectMap.end())
        return nullptr;
    return mObjectMap[id];
}

const std::unordered_map<std::string, KObject *> &KObjectManager::allObjs() const
{
    return mObjectMap;
}
