﻿#pragma execution_character_set("utf-8")
#include <wchar.h>
#include <assert.h>
#include <locale.h>
#include <ctype.h>
#include <cmath>

#include "CvxText.h"

#define KDFLT_WCHAR_SIZE 512

class CvxTextPrivate
{
public:
    CvxTextPrivate(const char *freeType)
    {
        assert(freeType != NULL);

        // 打开字库文件, 创建一个字体
        if (FT_Init_FreeType(&_library))
            throw;
        if (FT_New_Face(_library, freeType, 0, &_face))
            throw;

        // 设置字体输出参数
        //restoreFont();

        // 设置C语言的字符集环境
        setlocale(LC_ALL, "");

        _dst.resize(KDFLT_WCHAR_SIZE);

        _fontSize.val[0] = 20;  // 字体大小
        _fontSize.val[1] = 0.5; // 空白字符大小比例
        _fontSize.val[2] = 0.1; // 间隔大小比例
        _fontSize.val[3] = 0;   // 旋转角度(不支持)

        _fontUnderline = false; // 下画线(不支持)

        _fontDiaphaneity = 1.0; // 色彩比例(可产生透明效果)

        // 设置字符大小
        FT_Set_Pixel_Sizes(_face, (int)_fontSize.val[0], 0);
    }

    ~CvxTextPrivate()
    {
        FT_Done_Face(_face);
        FT_Done_FreeType(_library);
    }

    int toWChar(const char *src, wchar_t *dst, const char *local)
    {
        if (src == NULL)
        {
            //dst = NULL;
            return 0;
        }

        // 根据环境变量设置locale
        setlocale(LC_CTYPE, local);

        // 得到转化为需要的宽字符大小
        int w_size = mbstowcs(NULL, src, 0) + 1;

        // w_size = 0 说明mbstowcs返回值为-1。即在运行过程中遇到了非法字符(很有可能使locale
        // 没有设置正确)
        if (w_size == 0)
        {
            //dst = NULL;
            return -1;
        }

        //wcout << "w_size" << w_size << endl;
        //dst = new wchar_t[w_size];
        /*if (w_size < _dst.size())
		{
			_dst.resize(w_size);
		}*/
        //if (!dst) {
        //    return -1;
        //}

        int ret = mbstowcs(dst, src, w_size);
        if (ret <= 0)
        {
            return -1;
        }
        return 0;
    }

    //void putWChar(cv::Mat& img, wchar_t wc, cv::Point& pos, cv::Scalar color)
    void putWChar(cv::Mat &img, const wchar_t *text, cv::Point &pos, cv::Scalar color)
    {
        if (!text || !img.data)
            return;
        double space = _fontSize.val[0] * _fontSize.val[1];
        double sep = _fontSize.val[0] * _fontSize.val[2];

        for (int i = 0; text[i] != '\0'; ++i)
        {
            FT_UInt glyph_index = FT_Get_Char_Index(_face, text[i]);

            FT_Load_Glyph(_face, glyph_index, FT_LOAD_DEFAULT);
            FT_Render_Glyph(_face->glyph, FT_RENDER_MODE_MONO);
            FT_GlyphSlot slot = _face->glyph;

            // 行列数
            int rows = slot->bitmap.rows;
            int cols = slot->bitmap.width;

            // 输出当前的字符
            for (int i = 0; i < rows; ++i)
            {
                for (int j = 0; j < cols; ++j)
                {
                    int off = i * slot->bitmap.pitch + j / 8;

                    if (slot->bitmap.buffer[off] & (0xC0 >> (j % 8)))
                    {
                        int r = pos.y - (rows - 1 - i);
                        int c = pos.x + j;

                        if (r >= 0 && r < img.rows && c >= 0 && c < img.cols)
                        {
                            cv::Vec3b pixel = img.at<cv::Vec3b>(cv::Point(c, r));
                            cv::Scalar scalar = cv::Scalar(pixel.val[0], pixel.val[1], pixel.val[2]);

                            // 进行色彩融合
                            float p = _fontDiaphaneity;
                            for (int k = 0; k < 4; ++k)
                            {
                                scalar.val[k] = scalar.val[k] * (1 - p) + color.val[k] * p;
                            }

                            img.at<cv::Vec3b>(cv::Point(c, r))[0] = (unsigned char)(scalar.val[0]);
                            img.at<cv::Vec3b>(cv::Point(c, r))[1] = (unsigned char)(scalar.val[1]);
                            img.at<cv::Vec3b>(cv::Point(c, r))[2] = (unsigned char)(scalar.val[2]);
                        }
                    }
                }
            }

            // 修改下一个字的输出位置
            pos.x += (int)((cols ? cols : space) + sep);
        }
    }

public:
    FT_Library _library; // 字库
    FT_Face _face;       // 字体

    // 默认的字体输出参数
    cv::Scalar _fontSize;
    bool _fontUnderline;
    float _fontDiaphaneity;

    std::vector<wchar_t> _dst;
};

// 打开字库
CvxText::CvxText(const char *freeType)
{
    p = new CvxTextPrivate(freeType);
}

// 释放FreeType资源
CvxText::~CvxText()
{
}

void CvxText::toWChar(const char *src, wchar_t *dst)
{
    p->toWChar(src, dst, "zh_CN.utf8");
}

// 设置字体参数:
//
// font         - 字体类型, 目前不支持
// size         - 字体大小/空白比例/间隔比例/旋转角度
// underline   - 下画线
// diaphaneity   - 透明度
void CvxText::getFont(cv::Scalar *size, bool *underline, float *diaphaneity)
{
    if (size)
        *size = p->_fontSize;
    if (underline)
        *underline = p->_fontUnderline;
    if (diaphaneity)
        *diaphaneity = p->_fontDiaphaneity;
}

void CvxText::setFont(cv::Scalar *size, bool *underline, float *diaphaneity)
{
    if (size)
    {
        p->_fontSize.val[0] = std::fabs(size->val[0]);
        p->_fontSize.val[1] = std::fabs(size->val[1]);
        p->_fontSize.val[2] = std::fabs(size->val[2]);
        p->_fontSize.val[3] = std::fabs(size->val[3]);
    }
    if (underline)
    {
        p->_fontUnderline = *underline;
    }
    if (diaphaneity)
    {
        p->_fontDiaphaneity = *diaphaneity;
    }

    FT_Set_Pixel_Sizes(p->_face, (int)p->_fontSize.val[0], 0);
}

// 输出函数(颜色默认为白色)
int CvxText::putText(cv::Mat &img, char *text, cv::Point pos)
{
    return putText(img, text, pos, CV_RGB(255, 255, 255));
}

int CvxText::putText(cv::Mat &img, const wchar_t *text, cv::Point pos)
{
    return putText(img, text, pos, CV_RGB(255, 255, 255));
}

int CvxText::putText(cv::Mat &img, const char *text, cv::Point pos, cv::Scalar color)
{
    p->toWChar(text, p->_dst.data(), "zh_CN.utf8");
    return putText(img, p->_dst.data(), pos, color);
}

int CvxText::putText(cv::Mat &img, const wchar_t *text, cv::Point pos, cv::Scalar color)
{
    p->putWChar(img, text, pos, color);
    return 0;
}
