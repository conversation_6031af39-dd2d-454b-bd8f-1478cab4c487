﻿#pragma execution_character_set("utf-8")
#ifndef OPENCV_CVX_TEXT_HPP_
#define OPENCV_CVX_TEXT_HPP_

#include <ft2build.h>
#include FT_FREETYPE_H

#include <opencv2/opencv.hpp>
#include <vector>

#ifdef _WIN32
#ifdef CVXTEXT_LIB
#define CVXTEXT_EXPORT __declspec(dllexport)
#else
#define CVXTEXT_EXPORT __declspec(dllimport)
#endif
#else
#define CVXTEXT_EXPORT
#endif

class CvxTextPrivate;
class /*CVXTEXT_EXPORT*/ CvxText
{
public:
    /**
     * 装载字库文件
     */
    CvxText(const char *freeType);
    virtual ~CvxText();

    void toWChar(const char *src, wchar_t *dst);
    /**
     * 获取字体配置.
     *
     * \param size        字体大小/空白比例/间隔比例/旋转角度
     * \param underline   下画线
     * \param diaphaneity 透明度
     *
     * \sa setFont, restoreFont
     */
    void getFont(cv::Scalar *size = nullptr, bool *underline = nullptr, float *diaphaneity = nullptr);

    /**
     * 设置字体参数.
     *
     * \param size        字体大小/空白比例/间隔比例/旋转角度
     * \param underline   下画线
     * \param diaphaneity 透明度
     *
     * \sa getFont, restoreFont
     */
    void setFont(cv::Scalar *size = nullptr, bool *underline = nullptr, float *diaphaneity = nullptr);

    /**
     * 恢复原始的字体设置.
     *
     * \sa getFont, setFont
     */
    void restoreFont();

    /**
     * 输出汉字(颜色默认为黑色).遇到不能输出的字符将停止.
     *
     * \param img  输出的影象
     * \param text 文本内容
     * \param pos  文本位置
     *
     * \return 返回成功输出的字符长度，失败返回-1.
     */
    int putText(cv::Mat &img, char *text, cv::Point pos);

    /**
     * 输出汉字(颜色默认为黑色).遇到不能输出的字符将停止.
     *
     * \param img  输出的影象
     * \param text 文本内容
     * \param pos  文本位置
     *
     * \return 返回成功输出的字符长度，失败返回-1.
     */
    int putText(cv::Mat &img, const wchar_t *text, cv::Point pos);

    /**
     * 输出汉字.遇到不能输出的字符将停止.
     *
     * \param img   输出的影象
     * \param text  文本内容
     * \param pos   文本位置
     * \param color 文本颜色
     *
     * \return 返回成功输出的字符长度，失败返回-1.
     */
    int putText(cv::Mat &img, const char *text, cv::Point pos, cv::Scalar color);

    /**
     * 输出汉字.遇到不能输出的字符将停止.
     *
     * \param img   输出的影象
     * \param text  文本内容
     * \param pos   文本位置
     * \param color 文本颜色
     *
     * \return 返回成功输出的字符长度，失败返回-1.
     */
    int putText(cv::Mat &img, const wchar_t *text, cv::Point pos, cv::Scalar color);

private:
    // 禁止copy
    CvxText &operator=(const CvxText &);

private:
    CvxTextPrivate *p;
};

#endif // OPENCV_CVX_TEXT_HPP_