cmake_minimum_required(VERSION 3.10)

project(Simd)

file(MAKE_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin/Release)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(CMAKE_CXX_FLAGS_RELEASE "-O2 -Wall")
# 启用详细编译信息输出
set(CMAKE_VERBOSE_MAKEFILE ON)

# 包含目录
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/deps
)

include_directories(${OpenCV_INCLUDE_DIRS})
link_directories(${OpenCV_LIBRARY_DIRS})
# message(STATUS "OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
# message(STATUS "OpenCV_LIBRARY_DIRS: ${OpenCV_LIBRARY_DIRS}")
# message(STATUS "OpenCV_LIBRARIES: ${OpenCV_LIBRARIES}")


# 设置源文件
set(SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Fill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Float32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Gemm32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1HogLite.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Neural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Resizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Svm.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Synet.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetActivation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetConvolution32fNhwcDirect.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetDeconvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetFused.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetMergedConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1SynetPooling.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1Winograd.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2AbsDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2AbsDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2AbsGradientSaturatedSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2AddFeatureDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2AlphaBlending.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Background.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BayerToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BayerToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BgraToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BgraToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BgrToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BgrToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BgrToRgb.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2BgrToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Binarization.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Conditional.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Deinterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Detection.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2EdgeBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Fill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Float16.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Float32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2GaussianBlur3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Gemm32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2GrayToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2GrayToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Histogram.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Hog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2HogLite.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Int16ToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Integral.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Interference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Interleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Laplace.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Lbp.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2MeanFilter3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2MedianFilter.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Neural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Operation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Reduce.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2ReduceGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2ReduceGray3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2ReduceGray4x4.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2ReduceGray5x5.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Reorder.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2ResizeBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Resizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Segmentation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2ShiftBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Sobel.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Statistic.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2StatisticMoments.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2StretchGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Synet.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SynetActivation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SynetConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SynetConvolution32fNhwcDirect.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SynetDeconvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SynetMergedConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2SynetPooling.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2Texture.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2YuvToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2YuvToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2YuvToHue.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwAbsDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwAbsGradientSaturatedSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwAddFeatureDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwAlphaBlending.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBayerToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBayerToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgraToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgraToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgraToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgraToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgrToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgrToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgrToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgrToRgb.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBgrToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwBinarization.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwConditional.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwDeinterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwDetection.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwEdgeBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwFill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwFloat16.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwFloat32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwGaussianBlur3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwGrayToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwGrayToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwHistogram.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwHog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwHogLite.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwInt16ToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwIntegral.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwInterference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwInterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwLaplace.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwLbp.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwMeanFilter3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwMedianFilter.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwNeural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwOperation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwReduce.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwReduceGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwReduceGray3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwReduceGray4x4.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwReduceGray5x5.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwReorder.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwResizeBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwResizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwSegmentation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwShiftBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwSobel.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwSquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwStatistic.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwStatisticMoments.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwStretchGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwSynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwTexture.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwYuvToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwYuvToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bwYuvToHue.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fFill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fGemm32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fNeural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fResizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSvm.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynet.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetActivation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetConvolution32fNhwcDirect.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetDeconvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetFused.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetMergedConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fSynetPooling.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512fWinograd.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseAbsDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseAbsDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseAbsGradientSaturatedSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseAddFeatureDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseAlphaBlending.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBayerToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBayerToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgraToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgraToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgraToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgraToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgrToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgrToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgrToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgrToHsl.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgrToHsv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgrToRgb.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBgrToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseBinarization.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseConditional.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseCopy.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseCpu.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseCrc32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseDeinterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseDetection.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseEdgeBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseFill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseFloat16.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseFloat32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseGaussianBlur3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseGemm32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseGrayToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseGrayToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseHistogram.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseHog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseHogLite.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseInt16ToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseIntegral.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseInterference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseInterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseLaplace.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseLbp.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseMeanFilter3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseMedianFilter.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseNeural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseOperation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBasePerformance.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseReduce.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseReduceGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseReduceGray3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseReduceGray4x4.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseReduceGray5x5.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseReorder.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseResizeBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseResizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSegmentation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseShiftBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSobel.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseStatistic.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseStatisticMoments.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseStretchGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSvm.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynet.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynetActivation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynetConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynetDeconvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynetFused.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynetMergedConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseSynetPooling.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseTexture.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseThread.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseTransform.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseWinograd.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseYuvToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseYuvToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseYuvToHsl.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseYuvToHsv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBaseYuvToHue.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdLib.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdMsaOperation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonAbsDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonAbsGradientSaturatedSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonAddFeatureDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonAlphaBlending.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBayerToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBayerToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgraToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgraToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgraToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgraToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgrToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgrToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgrToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgrToRgb.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBgrToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonBinarization.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonConditional.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonDeinterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonDetection.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonEdgeBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonFill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonFloat16.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonFloat32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonGaussianBlur3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonGemm32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonGrayToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonGrayToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonHistogram.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonHog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonHogLite.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonInt16ToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonInterference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonInterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonLaplace.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonLbp.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonMeanFilter3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonMedianFilter.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonNeural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonOperation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonReduce.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonReduceGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonReduceGray3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonReduceGray4x4.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonReduceGray5x5.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonReorder.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonResizeBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonResizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSegmentation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonShiftBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSobel.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonStatistic.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonStatisticMoments.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonStretchGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSvm.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynet.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetActivation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetConvolution32fNhwcDirect.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetDeconvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetFused.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetMergedConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonSynetPooling.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonTexture.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonTransform.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonWinograd.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonYuvToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonYuvToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeonYuvToHue.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Fill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Float32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Gemm32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Hog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Neural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Resizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1SquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Svm.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Synet.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1SynetActivation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1SynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1SynetFused.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1SynetPooling.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1Winograd.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2AbsDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2AbsGradientSaturatedSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2AddFeatureDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2AlphaBlending.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Background.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2BayerToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2BgraToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2BgraToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2BgrToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2BgrToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Binarization.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Conditional.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Deinterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2EdgeBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Fill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Float32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2GaussianBlur3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2GrayToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Histogram.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Hog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Int16ToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Interference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Interleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Laplace.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Lbp.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2MeanFilter3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2MedianFilter.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Neural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Operation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Reduce.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2ReduceGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2ReduceGray3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2ReduceGray4x4.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2ReduceGray5x5.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Reorder.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2ResizeBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Resizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Segmentation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2ShiftBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Sobel.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2SquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Statistic.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2StatisticMoments.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2StretchGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Synet.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2SynetActivation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2SynetConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2SynetConvolution32fNhwcDirect.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2SynetDeconvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2SynetMergedConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2Texture.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2YuvToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2YuvToHue.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse3Gemm32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse3Neural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse3SynetConvolution32f.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse41Detection.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse41Hog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse41HogLite.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse41Resizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse41Segmentation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse41SynetConversion.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse42Crc32.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3AlphaBlending.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BayerToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgraToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgraToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgraToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgrToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgrToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgrToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgrToRgb.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3BgrToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Deinterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3GaussianBlur3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3GrayToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Interleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Laplace.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3MeanFilter3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Reduce.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3ReduceGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3ReduceGray4x4.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Reorder.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3ResizeBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Resizer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Sobel.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3SquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Texture.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3Transform.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3YuvToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxAbsDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxAbsGradientSaturatedSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxAddFeatureDifference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxAlphaBlending.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgraToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgraToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgraToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgraToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgrToBayer.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgrToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgrToGray.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBgrToYuv.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxBinarization.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxConditional.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxDeinterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxEdgeBackground.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxFill.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxGaussianBlur3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxGrayToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxGrayToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxHistogram.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxInterference.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxInterleave.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxLaplace.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxLbp.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxMeanFilter3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxMedianFilter.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxOperation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxReduceGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxReduceGray3x3.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxReduceGray4x4.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxReduceGray5x5.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxReorder.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxResizeBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxSegmentation.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxShiftBilinear.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxSobel.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxSquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxStatistic.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxStretchGray2x2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxTexture.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxYuvToBgr.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmxYuvToBgra.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVsxHog.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVsxNeural.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVsxSquaredDifferenceSum.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVsxSvm.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVsxYuvToHue.cpp
)

# 设置头文件
set(HEADERS
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAllocator.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdArray.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx1.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx2.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512bw.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdAvx512f.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBase.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdBayer.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdCompare.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdConfig.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdConst.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdContour.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdConversion.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdCpu.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdDefs.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdDetection.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdDetection.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdDrawing.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdEnable.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdExp.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdExtract.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdFont.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdFrame.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdGemm.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdImageMatcher.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdInit.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdIntegral.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdLib.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdLib.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdLoad.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdLog.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdMath.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdMemory.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdMotion.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdMsa.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeon.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeural.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdNeural.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdParallel.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdPerformance.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdPixel.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdPoint.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdPow.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdPyramid.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdRectangle.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdResizer.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdRuntime.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSet.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdShift.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse1.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse2.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse3.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse41.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSse42.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSsse3.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdStore.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdStream.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSynet.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSynetConvolution32f.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSynetConvolution32fCommon.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSynetDeconvolution32f.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdSynetMergedConvolution32f.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdTime.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdTranspose.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdUpdate.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVersion.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdView.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVmx.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdVsx.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdWinograd.h
    ${CMAKE_CURRENT_SOURCE_DIR}/SimdXml.hpp
)

# 创建库
add_library(${PROJECT_NAME} SHARED ${SOURCES} ${HEADERS})
# add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})
# 链接依赖库
target_link_libraries(${PROJECT_NAME}
    ${OpenCV_LIBRARIES}
)

