/*
* Simd Library (http://ermig1979.github.io/Simd).
*
* Copyright (c) 2011-2099 Yermalayeu <PERSON>har.
*
* Permission is hereby granted, free of charge, to any person obtaining a copy
* of this software and associated documentation files (the "Software"), to deal
* in the Software without restriction, including without limitation the rights
* to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
* copies of the Software, and to permit persons to whom the Software is
* furnished to do so, subject to the following conditions:
*
* The above copyright notice and this permission notice shall be included in
* all copies or substantial portions of the Software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
* IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
* FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
* AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
* LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
* OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
* SOFTWARE.
*/
#ifndef __SimdConfig_h__
#define __SimdConfig_h__

//#define SIMD_SSE_DISABLE

//#define SIMD_SSE2_DISABLE

//#define SIMD_SSE3_DISABLE

//#define SIMD_SSSE3_DISABLE

//#define SIMD_SSE41_DISABLE

//#define SIMD_SSE42_DISABLE

//#define SIMD_AVX_DISABLE

//#define SIMD_AVX2_DISABLE

//#define SIMD_AVX512F_DISABLE

//#define SIMD_AVX512BW_DISABLE

//#define SIMD_VMX_DISABLE

//#define SIMD_VSX_DISABLE

//#define SIMD_NEON_DISABLE

//#define SIMD_NEON_FP16_DISABLE

//#define SIMD_MSA_DISABLE

//#define SIMD_STATIC

#define SIMD_LOG_ENABLE

#define SIMD_ALLOCATE_ERROR_MESSAGE

#define SIMD_ALLOCATE_ASSERT

#define SIMD_NO_MANS_LAND 64

#define SIMD_NEON_RCP_ITER -1

#define SIMD_NEON_ASM_DISABLE

#define SIMD_NEON_PREFECH_SIZE 384

#define SIMD_OPENCV_ENABLE

//#define SIMD_PERFORMANCE_STATISTIC

//#define SIMD_RUNTIME_STATISTIC

//#define SIMD_FUTURE_DISABLE

#endif//__SimdConfig_h__
