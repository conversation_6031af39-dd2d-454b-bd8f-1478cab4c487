/*
* Simd Library (http://ermig1979.github.io/Simd).
*
* Copyright (c) 2011-2017 <PERSON><PERSON><PERSON><PERSON>.
*
* Permission is hereby granted, free of charge, to any person obtaining a copy
* of this software and associated documentation files (the "Software"), to deal
* in the Software without restriction, including without limitation the rights
* to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
* copies of the Software, and to permit persons to whom the Software is
* furnished to do so, subject to the following conditions:
*
* The above copyright notice and this permission notice shall be included in
* all copies or substantial portions of the Software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
* IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
* FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
* AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
* LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
* OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
* SOFTWARE.
*/
#include "Simd/SimdDefs.h"

namespace Simd
{
    namespace Base
    {
        // Precalculated CRC32c lookup table for polynomial 0x1EDC6F41 (castagnoli-crc).
        static const uint32_t Crc32cTable[8][256] =
        {
#ifdef SIMD_BIG_ENDIAN
            {
                0x00000000, 0x03836bf2, 0xf7703be1, 0xf4f35013, 0x1f979ac7, 0x1c14f135, 0xe8e7a126, 0xeb64cad4,
                0xcf58d98a, 0xccdbb278, 0x3828e26b, 0x3bab8999, 0xd0cf434d, 0xd34c28bf, 0x27bf78ac, 0x243c135e,
                0x6fc75e10, 0x6c4435e2, 0x98b765f1, 0x9b340e03, 0x7050c4d7, 0x73d3af25, 0x8720ff36, 0x84a394c4,
                0xa09f879a, 0xa31cec68, 0x57efbc7b, 0x546cd789, 0xbf081d5d, 0xbc8b76af, 0x487826bc, 0x4bfb4d4e,
                0xde8ebd20, 0xdd0dd6d2, 0x29fe86c1, 0x2a7ded33, 0xc11927e7, 0xc29a4c15, 0x36691c06, 0x35ea77f4,
                0x11d664aa, 0x12550f58, 0xe6a65f4b, 0xe52534b9, 0x0e41fe6d, 0x0dc2959f, 0xf931c58c, 0xfab2ae7e,
                0xb149e330, 0xb2ca88c2, 0x4639d8d1, 0x45bab323, 0xaede79f7, 0xad5d1205, 0x59ae4216, 0x5a2d29e4,
                0x7e113aba, 0x7d925148, 0x8961015b, 0x8ae26aa9, 0x6186a07d, 0x6205cb8f, 0x96f69b9c, 0x9575f06e,
                0xbc1d7b41, 0xbf9e10b3, 0x4b6d40a0, 0x48ee2b52, 0xa38ae186, 0xa0098a74, 0x54fada67, 0x5779b195,
                0x7345a2cb, 0x70c6c939, 0x8435992a, 0x87b6f2d8, 0x6cd2380c, 0x6f5153fe, 0x9ba203ed, 0x9821681f,
                0xd3da2551, 0xd0594ea3, 0x24aa1eb0, 0x27297542, 0xcc4dbf96, 0xcfced464, 0x3b3d8477, 0x38beef85,
                0x1c82fcdb, 0x1f019729, 0xebf2c73a, 0xe871acc8, 0x0315661c, 0x00960dee, 0xf4655dfd, 0xf7e6360f,
                0x6293c661, 0x6110ad93, 0x95e3fd80, 0x96609672, 0x7d045ca6, 0x7e873754, 0x8a746747, 0x89f70cb5,
                0xadcb1feb, 0xae487419, 0x5abb240a, 0x59384ff8, 0xb25c852c, 0xb1dfeede, 0x452cbecd, 0x46afd53f,
                0x0d549871, 0x0ed7f383, 0xfa24a390, 0xf9a7c862, 0x12c302b6, 0x11406944, 0xe5b33957, 0xe63052a5,
                0xc20c41fb, 0xc18f2a09, 0x357c7a1a, 0x36ff11e8, 0xdd9bdb3c, 0xde18b0ce, 0x2aebe0dd, 0x29688b2f,
                0x783bf682, 0x7bb89d70, 0x8f4bcd63, 0x8cc8a691, 0x67ac6c45, 0x642f07b7, 0x90dc57a4, 0x935f3c56,
                0xb7632f08, 0xb4e044fa, 0x401314e9, 0x43907f1b, 0xa8f4b5cf, 0xab77de3d, 0x5f848e2e, 0x5c07e5dc,
                0x17fca892, 0x147fc360, 0xe08c9373, 0xe30ff881, 0x086b3255, 0x0be859a7, 0xff1b09b4, 0xfc986246,
                0xd8a47118, 0xdb271aea, 0x2fd44af9, 0x2c57210b, 0xc733ebdf, 0xc4b0802d, 0x3043d03e, 0x33c0bbcc,
                0xa6b54ba2, 0xa5362050, 0x51c57043, 0x52461bb1, 0xb922d165, 0xbaa1ba97, 0x4e52ea84, 0x4dd18176,
                0x69ed9228, 0x6a6ef9da, 0x9e9da9c9, 0x9d1ec23b, 0x767a08ef, 0x75f9631d, 0x810a330e, 0x828958fc,
                0xc97215b2, 0xcaf17e40, 0x3e022e53, 0x3d8145a1, 0xd6e58f75, 0xd566e487, 0x2195b494, 0x2216df66,
                0x062acc38, 0x05a9a7ca, 0xf15af7d9, 0xf2d99c2b, 0x19bd56ff, 0x1a3e3d0d, 0xeecd6d1e, 0xed4e06ec,
                0xc4268dc3, 0xc7a5e631, 0x3356b622, 0x30d5ddd0, 0xdbb11704, 0xd8327cf6, 0x2cc12ce5, 0x2f424717,
                0x0b7e5449, 0x08fd3fbb, 0xfc0e6fa8, 0xff8d045a, 0x14e9ce8e, 0x176aa57c, 0xe399f56f, 0xe01a9e9d,
                0xabe1d3d3, 0xa862b821, 0x5c91e832, 0x5f1283c0, 0xb4764914, 0xb7f522e6, 0x430672f5, 0x40851907,
                0x64b90a59, 0x673a61ab, 0x93c931b8, 0x904a5a4a, 0x7b2e909e, 0x78adfb6c, 0x8c5eab7f, 0x8fddc08d,
                0x1aa830e3, 0x192b5b11, 0xedd80b02, 0xee5b60f0, 0x053faa24, 0x06bcc1d6, 0xf24f91c5, 0xf1ccfa37,
                0xd5f0e969, 0xd673829b, 0x2280d288, 0x2103b97a, 0xca6773ae, 0xc9e4185c, 0x3d17484f, 0x3e9423bd,
                0x756f6ef3, 0x76ec0501, 0x821f5512, 0x819c3ee0, 0x6af8f434, 0x697b9fc6, 0x9d88cfd5, 0x9e0ba427,
                0xba37b779, 0xb9b4dc8b, 0x4d478c98, 0x4ec4e76a, 0xa5a02dbe, 0xa623464c, 0x52d0165f, 0x51537dad
            },
            {
                0x00000000, 0x7798a213, 0xee304527, 0x99a8e734, 0xdc618a4e, 0xabf9285d, 0x3251cf69, 0x45c96d7a,
                0xb8c3149d, 0xcf5bb68e, 0x56f351ba, 0x216bf3a9, 0x64a29ed3, 0x133a3cc0, 0x8a92dbf4, 0xfd0a79e7,
                0x81f1c53f, 0xf669672c, 0x6fc18018, 0x1859220b, 0x5d904f71, 0x2a08ed62, 0xb3a00a56, 0xc438a845,
                0x3932d1a2, 0x4eaa73b1, 0xd7029485, 0xa09a3696, 0xe5535bec, 0x92cbf9ff, 0x0b631ecb, 0x7cfbbcd8,
                0x02e38b7f, 0x757b296c, 0xecd3ce58, 0x9b4b6c4b, 0xde820131, 0xa91aa322, 0x30b24416, 0x472ae605,
                0xba209fe2, 0xcdb83df1, 0x5410dac5, 0x238878d6, 0x664115ac, 0x11d9b7bf, 0x8871508b, 0xffe9f298,
                0x83124e40, 0xf48aec53, 0x6d220b67, 0x1abaa974, 0x5f73c40e, 0x28eb661d, 0xb1438129, 0xc6db233a,
                0x3bd15add, 0x4c49f8ce, 0xd5e11ffa, 0xa279bde9, 0xe7b0d093, 0x90287280, 0x098095b4, 0x7e1837a7,
                0x04c617ff, 0x735eb5ec, 0xeaf652d8, 0x9d6ef0cb, 0xd8a79db1, 0xaf3f3fa2, 0x3697d896, 0x410f7a85,
                0xbc050362, 0xcb9da171, 0x52354645, 0x25ade456, 0x6064892c, 0x17fc2b3f, 0x8e54cc0b, 0xf9cc6e18,
                0x8537d2c0, 0xf2af70d3, 0x6b0797e7, 0x1c9f35f4, 0x5956588e, 0x2ecefa9d, 0xb7661da9, 0xc0febfba,
                0x3df4c65d, 0x4a6c644e, 0xd3c4837a, 0xa45c2169, 0xe1954c13, 0x960dee00, 0x0fa50934, 0x783dab27,
                0x06259c80, 0x71bd3e93, 0xe815d9a7, 0x9f8d7bb4, 0xda4416ce, 0xaddcb4dd, 0x347453e9, 0x43ecf1fa,
                0xbee6881d, 0xc97e2a0e, 0x50d6cd3a, 0x274e6f29, 0x62870253, 0x151fa040, 0x8cb74774, 0xfb2fe567,
                0x87d459bf, 0xf04cfbac, 0x69e41c98, 0x1e7cbe8b, 0x5bb5d3f1, 0x2c2d71e2, 0xb58596d6, 0xc21d34c5,
                0x3f174d22, 0x488fef31, 0xd1270805, 0xa6bfaa16, 0xe376c76c, 0x94ee657f, 0x0d46824b, 0x7ade2058,
                0xf9fac3fb, 0x8e6261e8, 0x17ca86dc, 0x605224cf, 0x259b49b5, 0x5203eba6, 0xcbab0c92, 0xbc33ae81,
                0x4139d766, 0x36a17575, 0xaf099241, 0xd8913052, 0x9d585d28, 0xeac0ff3b, 0x7368180f, 0x04f0ba1c,
                0x780b06c4, 0x0f93a4d7, 0x963b43e3, 0xe1a3e1f0, 0xa46a8c8a, 0xd3f22e99, 0x4a5ac9ad, 0x3dc26bbe,
                0xc0c81259, 0xb750b04a, 0x2ef8577e, 0x5960f56d, 0x1ca99817, 0x6b313a04, 0xf299dd30, 0x85017f23,
                0xfb194884, 0x8c81ea97, 0x15290da3, 0x62b1afb0, 0x2778c2ca, 0x50e060d9, 0xc94887ed, 0xbed025fe,
                0x43da5c19, 0x3442fe0a, 0xadea193e, 0xda72bb2d, 0x9fbbd657, 0xe8237444, 0x718b9370, 0x06133163,
                0x7ae88dbb, 0x0d702fa8, 0x94d8c89c, 0xe3406a8f, 0xa68907f5, 0xd111a5e6, 0x48b942d2, 0x3f21e0c1,
                0xc22b9926, 0xb5b33b35, 0x2c1bdc01, 0x5b837e12, 0x1e4a1368, 0x69d2b17b, 0xf07a564f, 0x87e2f45c,
                0xfd3cd404, 0x8aa47617, 0x130c9123, 0x64943330, 0x215d5e4a, 0x56c5fc59, 0xcf6d1b6d, 0xb8f5b97e,
                0x45ffc099, 0x3267628a, 0xabcf85be, 0xdc5727ad, 0x999e4ad7, 0xee06e8c4, 0x77ae0ff0, 0x0036ade3,
                0x7ccd113b, 0x0b55b328, 0x92fd541c, 0xe565f60f, 0xa0ac9b75, 0xd7343966, 0x4e9cde52, 0x39047c41,
                0xc40e05a6, 0xb396a7b5, 0x2a3e4081, 0x5da6e292, 0x186f8fe8, 0x6ff72dfb, 0xf65fcacf, 0x81c768dc,
                0xffdf5f7b, 0x8847fd68, 0x11ef1a5c, 0x6677b84f, 0x23bed535, 0x54267726, 0xcd8e9012, 0xba163201,
                0x471c4be6, 0x3084e9f5, 0xa92c0ec1, 0xdeb4acd2, 0x9b7dc1a8, 0xece563bb, 0x754d848f, 0x02d5269c,
                0x7e2e9a44, 0x09b63857, 0x901edf63, 0xe7867d70, 0xa24f100a, 0xd5d7b219, 0x4c7f552d, 0x3be7f73e,
                0xc6ed8ed9, 0xb1752cca, 0x28ddcbfe, 0x5f4569ed, 0x1a8c0497, 0x6d14a684, 0xf4bc41b0, 0x8324e3a3
            },
            {
                0x00000000, 0x7e9241a5, 0x0d526f4f, 0x73c02eea, 0x1aa4de9e, 0x64369f3b, 0x17f6b1d1, 0x6964f074,
                0xc53e5138, 0xbbac109d, 0xc86c3e77, 0xb6fe7fd2, 0xdf9a8fa6, 0xa108ce03, 0xd2c8e0e9, 0xac5aa14c,
                0x8a7da270, 0xf4efe3d5, 0x872fcd3f, 0xf9bd8c9a, 0x90d97cee, 0xee4b3d4b, 0x9d8b13a1, 0xe3195204,
                0x4f43f348, 0x31d1b2ed, 0x42119c07, 0x3c83dda2, 0x55e72dd6, 0x2b756c73, 0x58b54299, 0x2627033c,
                0x14fb44e1, 0x6a690544, 0x19a92bae, 0x673b6a0b, 0x0e5f9a7f, 0x70cddbda, 0x030df530, 0x7d9fb495,
                0xd1c515d9, 0xaf57547c, 0xdc977a96, 0xa2053b33, 0xcb61cb47, 0xb5f38ae2, 0xc633a408, 0xb8a1e5ad,
                0x9e86e691, 0xe014a734, 0x93d489de, 0xed46c87b, 0x8422380f, 0xfab079aa, 0x89705740, 0xf7e216e5,
                0x5bb8b7a9, 0x252af60c, 0x56ead8e6, 0x28789943, 0x411c6937, 0x3f8e2892, 0x4c4e0678, 0x32dc47dd,
                0xd98065c7, 0xa7122462, 0xd4d20a88, 0xaa404b2d, 0xc324bb59, 0xbdb6fafc, 0xce76d416, 0xb0e495b3,
                0x1cbe34ff, 0x622c755a, 0x11ec5bb0, 0x6f7e1a15, 0x061aea61, 0x7888abc4, 0x0b48852e, 0x75dac48b,
                0x53fdc7b7, 0x2d6f8612, 0x5eafa8f8, 0x203de95d, 0x49591929, 0x37cb588c, 0x440b7666, 0x3a9937c3,
                0x96c3968f, 0xe851d72a, 0x9b91f9c0, 0xe503b865, 0x8c674811, 0xf2f509b4, 0x8135275e, 0xffa766fb,
                0xcd7b2126, 0xb3e96083, 0xc0294e69, 0xbebb0fcc, 0xd7dfffb8, 0xa94dbe1d, 0xda8d90f7, 0xa41fd152,
                0x0845701e, 0x76d731bb, 0x05171f51, 0x7b855ef4, 0x12e1ae80, 0x6c73ef25, 0x1fb3c1cf, 0x6121806a,
                0x47068356, 0x3994c2f3, 0x4a54ec19, 0x34c6adbc, 0x5da25dc8, 0x23301c6d, 0x50f03287, 0x2e627322,
                0x8238d26e, 0xfcaa93cb, 0x8f6abd21, 0xf1f8fc84, 0x989c0cf0, 0xe60e4d55, 0x95ce63bf, 0xeb5c221a,
                0x4377278b, 0x3de5662e, 0x4e2548c4, 0x30b70961, 0x59d3f915, 0x2741b8b0, 0x5481965a, 0x2a13d7ff,
                0x864976b3, 0xf8db3716, 0x8b1b19fc, 0xf5895859, 0x9ceda82d, 0xe27fe988, 0x91bfc762, 0xef2d86c7,
                0xc90a85fb, 0xb798c45e, 0xc458eab4, 0xbacaab11, 0xd3ae5b65, 0xad3c1ac0, 0xdefc342a, 0xa06e758f,
                0x0c34d4c3, 0x72a69566, 0x0166bb8c, 0x7ff4fa29, 0x16900a5d, 0x68024bf8, 0x1bc26512, 0x655024b7,
                0x578c636a, 0x291e22cf, 0x5ade0c25, 0x244c4d80, 0x4d28bdf4, 0x33bafc51, 0x407ad2bb, 0x3ee8931e,
                0x92b23252, 0xec2073f7, 0x9fe05d1d, 0xe1721cb8, 0x8816eccc, 0xf684ad69, 0x85448383, 0xfbd6c226,
                0xddf1c11a, 0xa36380bf, 0xd0a3ae55, 0xae31eff0, 0xc7551f84, 0xb9c75e21, 0xca0770cb, 0xb495316e,
                0x18cf9022, 0x665dd187, 0x159dff6d, 0x6b0fbec8, 0x026b4ebc, 0x7cf90f19, 0x0f3921f3, 0x71ab6056,
                0x9af7424c, 0xe46503e9, 0x97a52d03, 0xe9376ca6, 0x80539cd2, 0xfec1dd77, 0x8d01f39d, 0xf393b238,
                0x5fc91374, 0x215b52d1, 0x529b7c3b, 0x2c093d9e, 0x456dcdea, 0x3bff8c4f, 0x483fa2a5, 0x36ade300,
                0x108ae03c, 0x6e18a199, 0x1dd88f73, 0x634aced6, 0x0a2e3ea2, 0x74bc7f07, 0x077c51ed, 0x79ee1048,
                0xd5b4b104, 0xab26f0a1, 0xd8e6de4b, 0xa6749fee, 0xcf106f9a, 0xb1822e3f, 0xc24200d5, 0xbcd04170,
                0x8e0c06ad, 0xf09e4708, 0x835e69e2, 0xfdcc2847, 0x94a8d833, 0xea3a9996, 0x99fab77c, 0xe768f6d9,
                0x4b325795, 0x35a01630, 0x466038da, 0x38f2797f, 0x5196890b, 0x2f04c8ae, 0x5cc4e644, 0x2256a7e1,
                0x0471a4dd, 0x7ae3e578, 0x0923cb92, 0x77b18a37, 0x1ed57a43, 0x60473be6, 0x1387150c, 0x6d1554a9,
                0xc14ff5e5, 0xbfddb440, 0xcc1d9aaa, 0xb28fdb0f, 0xdbeb2b7b, 0xa5796ade, 0xd6b94434, 0xa82b0591
            },
            {
                0x00000000, 0xb8aa45dd, 0x812367bf, 0x39892262, 0xf331227b, 0x4b9b67a6, 0x721245c4, 0xcab80019,
                0xe66344f6, 0x5ec9012b, 0x67402349, 0xdfea6694, 0x1552668d, 0xadf82350, 0x94710132, 0x2cdb44ef,
                0x3db164e9, 0x851b2134, 0xbc920356, 0x0438468b, 0xce804692, 0x762a034f, 0x4fa3212d, 0xf70964f0,
                0xdbd2201f, 0x637865c2, 0x5af147a0, 0xe25b027d, 0x28e30264, 0x904947b9, 0xa9c065db, 0x116a2006,
                0x8b1425d7, 0x33be600a, 0x0a374268, 0xb29d07b5, 0x782507ac, 0xc08f4271, 0xf9066013, 0x41ac25ce,
                0x6d776121, 0xd5dd24fc, 0xec54069e, 0x54fe4343, 0x9e46435a, 0x26ec0687, 0x1f6524e5, 0xa7cf6138,
                0xb6a5413e, 0x0e0f04e3, 0x37862681, 0x8f2c635c, 0x45946345, 0xfd3e2698, 0xc4b704fa, 0x7c1d4127,
                0x50c605c8, 0xe86c4015, 0xd1e56277, 0x694f27aa, 0xa3f727b3, 0x1b5d626e, 0x22d4400c, 0x9a7e05d1,
                0xe75fa6ab, 0x5ff5e376, 0x667cc114, 0xded684c9, 0x146e84d0, 0xacc4c10d, 0x954de36f, 0x2de7a6b2,
                0x013ce25d, 0xb996a780, 0x801f85e2, 0x38b5c03f, 0xf20dc026, 0x4aa785fb, 0x732ea799, 0xcb84e244,
                0xdaeec242, 0x6244879f, 0x5bcda5fd, 0xe367e020, 0x29dfe039, 0x9175a5e4, 0xa8fc8786, 0x1056c25b,
                0x3c8d86b4, 0x8427c369, 0xbdaee10b, 0x0504a4d6, 0xcfbca4cf, 0x7716e112, 0x4e9fc370, 0xf63586ad,
                0x6c4b837c, 0xd4e1c6a1, 0xed68e4c3, 0x55c2a11e, 0x9f7aa107, 0x27d0e4da, 0x1e59c6b8, 0xa6f38365,
                0x8a28c78a, 0x32828257, 0x0b0ba035, 0xb3a1e5e8, 0x7919e5f1, 0xc1b3a02c, 0xf83a824e, 0x4090c793,
                0x51fae795, 0xe950a248, 0xd0d9802a, 0x6873c5f7, 0xa2cbc5ee, 0x1a618033, 0x23e8a251, 0x9b42e78c,
                0xb799a363, 0x0f33e6be, 0x36bac4dc, 0x8e108101, 0x44a88118, 0xfc02c4c5, 0xc58be6a7, 0x7d21a37a,
                0x3fc9a052, 0x8763e58f, 0xbeeac7ed, 0x06408230, 0xccf88229, 0x7452c7f4, 0x4ddbe596, 0xf571a04b,
                0xd9aae4a4, 0x6100a179, 0x5889831b, 0xe023c6c6, 0x2a9bc6df, 0x92318302, 0xabb8a160, 0x1312e4bd,
                0x0278c4bb, 0xbad28166, 0x835ba304, 0x3bf1e6d9, 0xf149e6c0, 0x49e3a31d, 0x706a817f, 0xc8c0c4a2,
                0xe41b804d, 0x5cb1c590, 0x6538e7f2, 0xdd92a22f, 0x172aa236, 0xaf80e7eb, 0x9609c589, 0x2ea38054,
                0xb4dd8585, 0x0c77c058, 0x35fee23a, 0x8d54a7e7, 0x47eca7fe, 0xff46e223, 0xc6cfc041, 0x7e65859c,
                0x52bec173, 0xea1484ae, 0xd39da6cc, 0x6b37e311, 0xa18fe308, 0x1925a6d5, 0x20ac84b7, 0x9806c16a,
                0x896ce16c, 0x31c6a4b1, 0x084f86d3, 0xb0e5c30e, 0x7a5dc317, 0xc2f786ca, 0xfb7ea4a8, 0x43d4e175,
                0x6f0fa59a, 0xd7a5e047, 0xee2cc225, 0x568687f8, 0x9c3e87e1, 0x2494c23c, 0x1d1de05e, 0xa5b7a583,
                0xd89606f9, 0x603c4324, 0x59b56146, 0xe11f249b, 0x2ba72482, 0x930d615f, 0xaa84433d, 0x122e06e0,
                0x3ef5420f, 0x865f07d2, 0xbfd625b0, 0x077c606d, 0xcdc46074, 0x756e25a9, 0x4ce707cb, 0xf44d4216,
                0xe5276210, 0x5d8d27cd, 0x640405af, 0xdcae4072, 0x1616406b, 0xaebc05b6, 0x973527d4, 0x2f9f6209,
                0x034426e6, 0xbbee633b, 0x82674159, 0x3acd0484, 0xf075049d, 0x48df4140, 0x71566322, 0xc9fc26ff,
                0x5382232e, 0xeb2866f3, 0xd2a14491, 0x6a0b014c, 0xa0b30155, 0x18194488, 0x219066ea, 0x993a2337,
                0xb5e167d8, 0x0d4b2205, 0x34c20067, 0x8c6845ba, 0x46d045a3, 0xfe7a007e, 0xc7f3221c, 0x7f5967c1,
                0x6e3347c7, 0xd699021a, 0xef102078, 0x57ba65a5, 0x9d0265bc, 0x25a82061, 0x1c210203, 0xa48b47de,
                0x88500331, 0x30fa46ec, 0x0973648e, 0xb1d92153, 0x7b61214a, 0xc3cb6497, 0xfa4246f5, 0x42e80328
            },
            {
                0x00000000, 0xac6f1138, 0x58df2270, 0xf4b03348, 0xb0be45e0, 0x1cd154d8, 0xe8616790, 0x440e76a8,
                0x910b67c5, 0x3d6476fd, 0xc9d445b5, 0x65bb548d, 0x21b52225, 0x8dda331d, 0x796a0055, 0xd505116d,
                0xd361228f, 0x7f0e33b7, 0x8bbe00ff, 0x27d111c7, 0x63df676f, 0xcfb07657, 0x3b00451f, 0x976f5427,
                0x426a454a, 0xee055472, 0x1ab5673a, 0xb6da7602, 0xf2d400aa, 0x5ebb1192, 0xaa0b22da, 0x066433e2,
                0x57b5a81b, 0xfbdab923, 0x0f6a8a6b, 0xa3059b53, 0xe70bedfb, 0x4b64fcc3, 0xbfd4cf8b, 0x13bbdeb3,
                0xc6becfde, 0x6ad1dee6, 0x9e61edae, 0x320efc96, 0x76008a3e, 0xda6f9b06, 0x2edfa84e, 0x82b0b976,
                0x84d48a94, 0x28bb9bac, 0xdc0ba8e4, 0x7064b9dc, 0x346acf74, 0x9805de4c, 0x6cb5ed04, 0xc0dafc3c,
                0x15dfed51, 0xb9b0fc69, 0x4d00cf21, 0xe16fde19, 0xa561a8b1, 0x090eb989, 0xfdbe8ac1, 0x51d19bf9,
                0xae6a5137, 0x0205400f, 0xf6b57347, 0x5ada627f, 0x1ed414d7, 0xb2bb05ef, 0x460b36a7, 0xea64279f,
                0x3f6136f2, 0x930e27ca, 0x67be1482, 0xcbd105ba, 0x8fdf7312, 0x23b0622a, 0xd7005162, 0x7b6f405a,
                0x7d0b73b8, 0xd1646280, 0x25d451c8, 0x89bb40f0, 0xcdb53658, 0x61da2760, 0x956a1428, 0x39050510,
                0xec00147d, 0x406f0545, 0xb4df360d, 0x18b02735, 0x5cbe519d, 0xf0d140a5, 0x046173ed, 0xa80e62d5,
                0xf9dff92c, 0x55b0e814, 0xa100db5c, 0x0d6fca64, 0x4961bccc, 0xe50eadf4, 0x11be9ebc, 0xbdd18f84,
                0x68d49ee9, 0xc4bb8fd1, 0x300bbc99, 0x9c64ada1, 0xd86adb09, 0x7405ca31, 0x80b5f979, 0x2cdae841,
                0x2abedba3, 0x86d1ca9b, 0x7261f9d3, 0xde0ee8eb, 0x9a009e43, 0x366f8f7b, 0xc2dfbc33, 0x6eb0ad0b,
                0xbbb5bc66, 0x17daad5e, 0xe36a9e16, 0x4f058f2e, 0x0b0bf986, 0xa764e8be, 0x53d4dbf6, 0xffbbcace,
                0x5cd5a26e, 0xf0bab356, 0x040a801e, 0xa8659126, 0xec6be78e, 0x4004f6b6, 0xb4b4c5fe, 0x18dbd4c6,
                0xcddec5ab, 0x61b1d493, 0x9501e7db, 0x396ef6e3, 0x7d60804b, 0xd10f9173, 0x25bfa23b, 0x89d0b303,
                0x8fb480e1, 0x23db91d9, 0xd76ba291, 0x7b04b3a9, 0x3f0ac501, 0x9365d439, 0x67d5e771, 0xcbbaf649,
                0x1ebfe724, 0xb2d0f61c, 0x4660c554, 0xea0fd46c, 0xae01a2c4, 0x026eb3fc, 0xf6de80b4, 0x5ab1918c,
                0x0b600a75, 0xa70f1b4d, 0x53bf2805, 0xffd0393d, 0xbbde4f95, 0x17b15ead, 0xe3016de5, 0x4f6e7cdd,
                0x9a6b6db0, 0x36047c88, 0xc2b44fc0, 0x6edb5ef8, 0x2ad52850, 0x86ba3968, 0x720a0a20, 0xde651b18,
                0xd80128fa, 0x746e39c2, 0x80de0a8a, 0x2cb11bb2, 0x68bf6d1a, 0xc4d07c22, 0x30604f6a, 0x9c0f5e52,
                0x490a4f3f, 0xe5655e07, 0x11d56d4f, 0xbdba7c77, 0xf9b40adf, 0x55db1be7, 0xa16b28af, 0x0d043997,
                0xf2bff359, 0x5ed0e261, 0xaa60d129, 0x060fc011, 0x4201b6b9, 0xee6ea781, 0x1ade94c9, 0xb6b185f1,
                0x63b4949c, 0xcfdb85a4, 0x3b6bb6ec, 0x9704a7d4, 0xd30ad17c, 0x7f65c044, 0x8bd5f30c, 0x27bae234,
                0x21ded1d6, 0x8db1c0ee, 0x7901f3a6, 0xd56ee29e, 0x91609436, 0x3d0f850e, 0xc9bfb646, 0x65d0a77e,
                0xb0d5b613, 0x1cbaa72b, 0xe80a9463, 0x4465855b, 0x006bf3f3, 0xac04e2cb, 0x58b4d183, 0xf4dbc0bb,
                0xa50a5b42, 0x09654a7a, 0xfdd57932, 0x51ba680a, 0x15b41ea2, 0xb9db0f9a, 0x4d6b3cd2, 0xe1042dea,
                0x34013c87, 0x986e2dbf, 0x6cde1ef7, 0xc0b10fcf, 0x84bf7967, 0x28d0685f, 0xdc605b17, 0x700f4a2f,
                0x766b79cd, 0xda0468f5, 0x2eb45bbd, 0x82db4a85, 0xc6d53c2d, 0x6aba2d15, 0x9e0a1e5d, 0x32650f65,
                0xe7601e08, 0x4b0f0f30, 0xbfbf3c78, 0x13d02d40, 0x57de5be8, 0xfbb14ad0, 0x0f017998, 0xa36e68a0
            },
            {
                0x00000000, 0x196b30ef, 0xc3a08cdb, 0xdacbbc34, 0x7737f5b2, 0x6e5cc55d, 0xb4977969, 0xadfc4986,
                0x1f180660, 0x0673368f, 0xdcb88abb, 0xc5d3ba54, 0x682ff3d2, 0x7144c33d, 0xab8f7f09, 0xb2e44fe6,
                0x3e300cc0, 0x275b3c2f, 0xfd90801b, 0xe4fbb0f4, 0x4907f972, 0x506cc99d, 0x8aa775a9, 0x93cc4546,
                0x21280aa0, 0x38433a4f, 0xe288867b, 0xfbe3b694, 0x561fff12, 0x4f74cffd, 0x95bf73c9, 0x8cd44326,
                0x8d16f485, 0x947dc46a, 0x4eb6785e, 0x57dd48b1, 0xfa210137, 0xe34a31d8, 0x39818dec, 0x20eabd03,
                0x920ef2e5, 0x8b65c20a, 0x51ae7e3e, 0x48c54ed1, 0xe5390757, 0xfc5237b8, 0x26998b8c, 0x3ff2bb63,
                0xb326f845, 0xaa4dc8aa, 0x7086749e, 0x69ed4471, 0xc4110df7, 0xdd7a3d18, 0x07b1812c, 0x1edab1c3,
                0xac3efe25, 0xb555ceca, 0x6f9e72fe, 0x76f54211, 0xdb090b97, 0xc2623b78, 0x18a9874c, 0x01c2b7a3,
                0xeb5b040e, 0xf23034e1, 0x28fb88d5, 0x3190b83a, 0x9c6cf1bc, 0x8507c153, 0x5fcc7d67, 0x46a74d88,
                0xf443026e, 0xed283281, 0x37e38eb5, 0x2e88be5a, 0x8374f7dc, 0x9a1fc733, 0x40d47b07, 0x59bf4be8,
                0xd56b08ce, 0xcc003821, 0x16cb8415, 0x0fa0b4fa, 0xa25cfd7c, 0xbb37cd93, 0x61fc71a7, 0x78974148,
                0xca730eae, 0xd3183e41, 0x09d38275, 0x10b8b29a, 0xbd44fb1c, 0xa42fcbf3, 0x7ee477c7, 0x678f4728,
                0x664df08b, 0x7f26c064, 0xa5ed7c50, 0xbc864cbf, 0x117a0539, 0x081135d6, 0xd2da89e2, 0xcbb1b90d,
                0x7955f6eb, 0x603ec604, 0xbaf57a30, 0xa39e4adf, 0x0e620359, 0x170933b6, 0xcdc28f82, 0xd4a9bf6d,
                0x587dfc4b, 0x4116cca4, 0x9bdd7090, 0x82b6407f, 0x2f4a09f9, 0x36213916, 0xecea8522, 0xf581b5cd,
                0x4765fa2b, 0x5e0ecac4, 0x84c576f0, 0x9dae461f, 0x30520f99, 0x29393f76, 0xf3f28342, 0xea99b3ad,
                0xd6b7081c, 0xcfdc38f3, 0x151784c7, 0x0c7cb428, 0xa180fdae, 0xb8ebcd41, 0x62207175, 0x7b4b419a,
                0xc9af0e7c, 0xd0c43e93, 0x0a0f82a7, 0x1364b248, 0xbe98fbce, 0xa7f3cb21, 0x7d387715, 0x645347fa,
                0xe88704dc, 0xf1ec3433, 0x2b278807, 0x324cb8e8, 0x9fb0f16e, 0x86dbc181, 0x5c107db5, 0x457b4d5a,
                0xf79f02bc, 0xeef43253, 0x343f8e67, 0x2d54be88, 0x80a8f70e, 0x99c3c7e1, 0x43087bd5, 0x5a634b3a,
                0x5ba1fc99, 0x42cacc76, 0x98017042, 0x816a40ad, 0x2c96092b, 0x35fd39c4, 0xef3685f0, 0xf65db51f,
                0x44b9faf9, 0x5dd2ca16, 0x87197622, 0x9e7246cd, 0x338e0f4b, 0x2ae53fa4, 0xf02e8390, 0xe945b37f,
                0x6591f059, 0x7cfac0b6, 0xa6317c82, 0xbf5a4c6d, 0x12a605eb, 0x0bcd3504, 0xd1068930, 0xc86db9df,
                0x7a89f639, 0x63e2c6d6, 0xb9297ae2, 0xa0424a0d, 0x0dbe038b, 0x14d53364, 0xce1e8f50, 0xd775bfbf,
                0x3dec0c12, 0x24873cfd, 0xfe4c80c9, 0xe727b026, 0x4adbf9a0, 0x53b0c94f, 0x897b757b, 0x90104594,
                0x22f40a72, 0x3b9f3a9d, 0xe15486a9, 0xf83fb646, 0x55c3ffc0, 0x4ca8cf2f, 0x9663731b, 0x8f0843f4,
                0x03dc00d2, 0x1ab7303d, 0xc07c8c09, 0xd917bce6, 0x74ebf560, 0x6d80c58f, 0xb74b79bb, 0xae204954,
                0x1cc406b2, 0x05af365d, 0xdf648a69, 0xc60fba86, 0x6bf3f300, 0x7298c3ef, 0xa8537fdb, 0xb1384f34,
                0xb0faf897, 0xa991c878, 0x735a744c, 0x6a3144a3, 0xc7cd0d25, 0xdea63dca, 0x046d81fe, 0x1d06b111,
                0xafe2fef7, 0xb689ce18, 0x6c42722c, 0x752942c3, 0xd8d50b45, 0xc1be3baa, 0x1b75879e, 0x021eb771,
                0x8ecaf457, 0x97a1c4b8, 0x4d6a788c, 0x54014863, 0xf9fd01e5, 0xe096310a, 0x3a5d8d3e, 0x2336bdd1,
                0x91d2f237, 0x88b9c2d8, 0x52727eec, 0x4b194e03, 0xe6e50785, 0xff8e376a, 0x25458b5e, 0x3c2ebbb1
            },
            {
                0x00000000, 0xc82c0368, 0x905906d0, 0x587505b8, 0xd1c5e0a5, 0x19e9e3cd, 0x419ce675, 0x89b0e51d,
                0x53fd2d4e, 0x9bd12e26, 0xc3a42b9e, 0x0b8828f6, 0x8238cdeb, 0x4a14ce83, 0x1261cb3b, 0xda4dc853,
                0xa6fa5b9c, 0x6ed658f4, 0x36a35d4c, 0xfe8f5e24, 0x773fbb39, 0xbf13b851, 0xe766bde9, 0x2f4abe81,
                0xf50776d2, 0x3d2b75ba, 0x655e7002, 0xad72736a, 0x24c29677, 0xecee951f, 0xb49b90a7, 0x7cb793cf,
                0xbd835b3d, 0x75af5855, 0x2dda5ded, 0xe5f65e85, 0x6c46bb98, 0xa46ab8f0, 0xfc1fbd48, 0x3433be20,
                0xee7e7673, 0x2652751b, 0x7e2770a3, 0xb60b73cb, 0x3fbb96d6, 0xf79795be, 0xafe29006, 0x67ce936e,
                0x1b7900a1, 0xd35503c9, 0x8b200671, 0x430c0519, 0xcabce004, 0x0290e36c, 0x5ae5e6d4, 0x92c9e5bc,
                0x48842def, 0x80a82e87, 0xd8dd2b3f, 0x10f12857, 0x9941cd4a, 0x516dce22, 0x0918cb9a, 0xc134c8f2,
                0x7a07b77a, 0xb22bb412, 0xea5eb1aa, 0x2272b2c2, 0xabc257df, 0x63ee54b7, 0x3b9b510f, 0xf3b75267,
                0x29fa9a34, 0xe1d6995c, 0xb9a39ce4, 0x718f9f8c, 0xf83f7a91, 0x301379f9, 0x68667c41, 0xa04a7f29,
                0xdcfdece6, 0x14d1ef8e, 0x4ca4ea36, 0x8488e95e, 0x0d380c43, 0xc5140f2b, 0x9d610a93, 0x554d09fb,
                0x8f00c1a8, 0x472cc2c0, 0x1f59c778, 0xd775c410, 0x5ec5210d, 0x96e92265, 0xce9c27dd, 0x06b024b5,
                0xc784ec47, 0x0fa8ef2f, 0x57ddea97, 0x9ff1e9ff, 0x16410ce2, 0xde6d0f8a, 0x86180a32, 0x4e34095a,
                0x9479c109, 0x5c55c261, 0x0420c7d9, 0xcc0cc4b1, 0x45bc21ac, 0x8d9022c4, 0xd5e5277c, 0x1dc92414,
                0x617eb7db, 0xa952b4b3, 0xf127b10b, 0x390bb263, 0xb0bb577e, 0x78975416, 0x20e251ae, 0xe8ce52c6,
                0x32839a95, 0xfaaf99fd, 0xa2da9c45, 0x6af69f2d, 0xe3467a30, 0x2b6a7958, 0x731f7ce0, 0xbb337f88,
                0xf40e6ef5, 0x3c226d9d, 0x64576825, 0xac7b6b4d, 0x25cb8e50, 0xede78d38, 0xb5928880, 0x7dbe8be8,
                0xa7f343bb, 0x6fdf40d3, 0x37aa456b, 0xff864603, 0x7636a31e, 0xbe1aa076, 0xe66fa5ce, 0x2e43a6a6,
                0x52f43569, 0x9ad83601, 0xc2ad33b9, 0x0a8130d1, 0x8331d5cc, 0x4b1dd6a4, 0x1368d31c, 0xdb44d074,
                0x01091827, 0xc9251b4f, 0x91501ef7, 0x597c1d9f, 0xd0ccf882, 0x18e0fbea, 0x4095fe52, 0x88b9fd3a,
                0x498d35c8, 0x81a136a0, 0xd9d43318, 0x11f83070, 0x9848d56d, 0x5064d605, 0x0811d3bd, 0xc03dd0d5,
                0x1a701886, 0xd25c1bee, 0x8a291e56, 0x42051d3e, 0xcbb5f823, 0x0399fb4b, 0x5becfef3, 0x93c0fd9b,
                0xef776e54, 0x275b6d3c, 0x7f2e6884, 0xb7026bec, 0x3eb28ef1, 0xf69e8d99, 0xaeeb8821, 0x66c78b49,
                0xbc8a431a, 0x74a64072, 0x2cd345ca, 0xe4ff46a2, 0x6d4fa3bf, 0xa563a0d7, 0xfd16a56f, 0x353aa607,
                0x8e09d98f, 0x4625dae7, 0x1e50df5f, 0xd67cdc37, 0x5fcc392a, 0x97e03a42, 0xcf953ffa, 0x07b93c92,
                0xddf4f4c1, 0x15d8f7a9, 0x4dadf211, 0x8581f179, 0x0c311464, 0xc41d170c, 0x9c6812b4, 0x544411dc,
                0x28f38213, 0xe0df817b, 0xb8aa84c3, 0x708687ab, 0xf93662b6, 0x311a61de, 0x696f6466, 0xa143670e,
                0x7b0eaf5d, 0xb322ac35, 0xeb57a98d, 0x237baae5, 0xaacb4ff8, 0x62e74c90, 0x3a924928, 0xf2be4a40,
                0x338a82b2, 0xfba681da, 0xa3d38462, 0x6bff870a, 0xe24f6217, 0x2a63617f, 0x721664c7, 0xba3a67af,
                0x6077affc, 0xa85bac94, 0xf02ea92c, 0x3802aa44, 0xb1b24f59, 0x799e4c31, 0x21eb4989, 0xe9c74ae1,
                0x9570d92e, 0x5d5cda46, 0x0529dffe, 0xcd05dc96, 0x44b5398b, 0x8c993ae3, 0xd4ec3f5b, 0x1cc03c33,
                0xc68df460, 0x0ea1f708, 0x56d4f2b0, 0x9ef8f1d8, 0x174814c5, 0xdf6417ad, 0x87111215, 0x4f3d117d
            },
            {
                0x00000000, 0x277d3c49, 0x4efa7892, 0x698744db, 0x6d821d21, 0x4aff2168, 0x237865b3, 0x040559fa,
                0xda043b42, 0xfd79070b, 0x94fe43d0, 0xb3837f99, 0xb7862663, 0x90fb1a2a, 0xf97c5ef1, 0xde0162b8,
                0xb4097684, 0x93744acd, 0xfaf30e16, 0xdd8e325f, 0xd98b6ba5, 0xfef657ec, 0x97711337, 0xb00c2f7e,
                0x6e0d4dc6, 0x4970718f, 0x20f73554, 0x078a091d, 0x038f50e7, 0x24f26cae, 0x4d752875, 0x6a08143c,
                0x9965000d, 0xbe183c44, 0xd79f789f, 0xf0e244d6, 0xf4e71d2c, 0xd39a2165, 0xba1d65be, 0x9d6059f7,
                0x43613b4f, 0x641c0706, 0x0d9b43dd, 0x2ae67f94, 0x2ee3266e, 0x099e1a27, 0x60195efc, 0x476462b5,
                0x2d6c7689, 0x0a114ac0, 0x63960e1b, 0x44eb3252, 0x40ee6ba8, 0x679357e1, 0x0e14133a, 0x29692f73,
                0xf7684dcb, 0xd0157182, 0xb9923559, 0x9eef0910, 0x9aea50ea, 0xbd976ca3, 0xd4102878, 0xf36d1431,
                0x32cb001a, 0x15b63c53, 0x7c317888, 0x5b4c44c1, 0x5f491d3b, 0x78342172, 0x11b365a9, 0x36ce59e0,
                0xe8cf3b58, 0xcfb20711, 0xa63543ca, 0x81487f83, 0x854d2679, 0xa2301a30, 0xcbb75eeb, 0xecca62a2,
                0x86c2769e, 0xa1bf4ad7, 0xc8380e0c, 0xef453245, 0xeb406bbf, 0xcc3d57f6, 0xa5ba132d, 0x82c72f64,
                0x5cc64ddc, 0x7bbb7195, 0x123c354e, 0x35410907, 0x314450fd, 0x16396cb4, 0x7fbe286f, 0x58c31426,
                0xabae0017, 0x8cd33c5e, 0xe5547885, 0xc22944cc, 0xc62c1d36, 0xe151217f, 0x88d665a4, 0xafab59ed,
                0x71aa3b55, 0x56d7071c, 0x3f5043c7, 0x182d7f8e, 0x1c282674, 0x3b551a3d, 0x52d25ee6, 0x75af62af,
                0x1fa77693, 0x38da4ada, 0x515d0e01, 0x76203248, 0x72256bb2, 0x555857fb, 0x3cdf1320, 0x1ba22f69,
                0xc5a34dd1, 0xe2de7198, 0x8b593543, 0xac24090a, 0xa82150f0, 0x8f5c6cb9, 0xe6db2862, 0xc1a6142b,
                0x64960134, 0x43eb3d7d, 0x2a6c79a6, 0x0d1145ef, 0x09141c15, 0x2e69205c, 0x47ee6487, 0x609358ce,
                0xbe923a76, 0x99ef063f, 0xf06842e4, 0xd7157ead, 0xd3102757, 0xf46d1b1e, 0x9dea5fc5, 0xba97638c,
                0xd09f77b0, 0xf7e24bf9, 0x9e650f22, 0xb918336b, 0xbd1d6a91, 0x9a6056d8, 0xf3e71203, 0xd49a2e4a,
                0x0a9b4cf2, 0x2de670bb, 0x44613460, 0x631c0829, 0x671951d3, 0x40646d9a, 0x29e32941, 0x0e9e1508,
                0xfdf30139, 0xda8e3d70, 0xb30979ab, 0x947445e2, 0x90711c18, 0xb70c2051, 0xde8b648a, 0xf9f658c3,
                0x27f73a7b, 0x008a0632, 0x690d42e9, 0x4e707ea0, 0x4a75275a, 0x6d081b13, 0x048f5fc8, 0x23f26381,
                0x49fa77bd, 0x6e874bf4, 0x07000f2f, 0x207d3366, 0x24786a9c, 0x030556d5, 0x6a82120e, 0x4dff2e47,
                0x93fe4cff, 0xb48370b6, 0xdd04346d, 0xfa790824, 0xfe7c51de, 0xd9016d97, 0xb086294c, 0x97fb1505,
                0x565d012e, 0x71203d67, 0x18a779bc, 0x3fda45f5, 0x3bdf1c0f, 0x1ca22046, 0x7525649d, 0x525858d4,
                0x8c593a6c, 0xab240625, 0xc2a342fe, 0xe5de7eb7, 0xe1db274d, 0xc6a61b04, 0xaf215fdf, 0x885c6396,
                0xe25477aa, 0xc5294be3, 0xacae0f38, 0x8bd33371, 0x8fd66a8b, 0xa8ab56c2, 0xc12c1219, 0xe6512e50,
                0x38504ce8, 0x1f2d70a1, 0x76aa347a, 0x51d70833, 0x55d251c9, 0x72af6d80, 0x1b28295b, 0x3c551512,
                0xcf380123, 0xe8453d6a, 0x81c279b1, 0xa6bf45f8, 0xa2ba1c02, 0x85c7204b, 0xec406490, 0xcb3d58d9,
                0x153c3a61, 0x32410628, 0x5bc642f3, 0x7cbb7eba, 0x78be2740, 0x5fc31b09, 0x36445fd2, 0x1139639b,
                0x7b3177a7, 0x5c4c4bee, 0x35cb0f35, 0x12b6337c, 0x16b36a86, 0x31ce56cf, 0x58491214, 0x7f342e5d,
                0xa1354ce5, 0x864870ac, 0xefcf3477, 0xc8b2083e, 0xccb751c4, 0xebca6d8d, 0x824d2956, 0xa530151f
            }
#else//SIMD_BIG_ENDIAN
            {
                0x00000000, 0xf26b8303, 0xe13b70f7, 0x1350f3f4, 0xc79a971f, 0x35f1141c, 0x26a1e7e8, 0xd4ca64eb,
                0x8ad958cf, 0x78b2dbcc, 0x6be22838, 0x9989ab3b, 0x4d43cfd0, 0xbf284cd3, 0xac78bf27, 0x5e133c24,
                0x105ec76f, 0xe235446c, 0xf165b798, 0x030e349b, 0xd7c45070, 0x25afd373, 0x36ff2087, 0xc494a384,
                0x9a879fa0, 0x68ec1ca3, 0x7bbcef57, 0x89d76c54, 0x5d1d08bf, 0xaf768bbc, 0xbc267848, 0x4e4dfb4b,
                0x20bd8ede, 0xd2d60ddd, 0xc186fe29, 0x33ed7d2a, 0xe72719c1, 0x154c9ac2, 0x061c6936, 0xf477ea35,
                0xaa64d611, 0x580f5512, 0x4b5fa6e6, 0xb93425e5, 0x6dfe410e, 0x9f95c20d, 0x8cc531f9, 0x7eaeb2fa,
                0x30e349b1, 0xc288cab2, 0xd1d83946, 0x23b3ba45, 0xf779deae, 0x05125dad, 0x1642ae59, 0xe4292d5a,
                0xba3a117e, 0x4851927d, 0x5b016189, 0xa96ae28a, 0x7da08661, 0x8fcb0562, 0x9c9bf696, 0x6ef07595,
                0x417b1dbc, 0xb3109ebf, 0xa0406d4b, 0x522bee48, 0x86e18aa3, 0x748a09a0, 0x67dafa54, 0x95b17957,
                0xcba24573, 0x39c9c670, 0x2a993584, 0xd8f2b687, 0x0c38d26c, 0xfe53516f, 0xed03a29b, 0x1f682198,
                0x5125dad3, 0xa34e59d0, 0xb01eaa24, 0x42752927, 0x96bf4dcc, 0x64d4cecf, 0x77843d3b, 0x85efbe38,
                0xdbfc821c, 0x2997011f, 0x3ac7f2eb, 0xc8ac71e8, 0x1c661503, 0xee0d9600, 0xfd5d65f4, 0x0f36e6f7,
                0x61c69362, 0x93ad1061, 0x80fde395, 0x72966096, 0xa65c047d, 0x5437877e, 0x4767748a, 0xb50cf789,
                0xeb1fcbad, 0x197448ae, 0x0a24bb5a, 0xf84f3859, 0x2c855cb2, 0xdeeedfb1, 0xcdbe2c45, 0x3fd5af46,
                0x7198540d, 0x83f3d70e, 0x90a324fa, 0x62c8a7f9, 0xb602c312, 0x44694011, 0x5739b3e5, 0xa55230e6,
                0xfb410cc2, 0x092a8fc1, 0x1a7a7c35, 0xe811ff36, 0x3cdb9bdd, 0xceb018de, 0xdde0eb2a, 0x2f8b6829,
                0x82f63b78, 0x709db87b, 0x63cd4b8f, 0x91a6c88c, 0x456cac67, 0xb7072f64, 0xa457dc90, 0x563c5f93,
                0x082f63b7, 0xfa44e0b4, 0xe9141340, 0x1b7f9043, 0xcfb5f4a8, 0x3dde77ab, 0x2e8e845f, 0xdce5075c,
                0x92a8fc17, 0x60c37f14, 0x73938ce0, 0x81f80fe3, 0x55326b08, 0xa759e80b, 0xb4091bff, 0x466298fc,
                0x1871a4d8, 0xea1a27db, 0xf94ad42f, 0x0b21572c, 0xdfeb33c7, 0x2d80b0c4, 0x3ed04330, 0xccbbc033,
                0xa24bb5a6, 0x502036a5, 0x4370c551, 0xb11b4652, 0x65d122b9, 0x97baa1ba, 0x84ea524e, 0x7681d14d,
                0x2892ed69, 0xdaf96e6a, 0xc9a99d9e, 0x3bc21e9d, 0xef087a76, 0x1d63f975, 0x0e330a81, 0xfc588982,
                0xb21572c9, 0x407ef1ca, 0x532e023e, 0xa145813d, 0x758fe5d6, 0x87e466d5, 0x94b49521, 0x66df1622,
                0x38cc2a06, 0xcaa7a905, 0xd9f75af1, 0x2b9cd9f2, 0xff56bd19, 0x0d3d3e1a, 0x1e6dcdee, 0xec064eed,
                0xc38d26c4, 0x31e6a5c7, 0x22b65633, 0xd0ddd530, 0x0417b1db, 0xf67c32d8, 0xe52cc12c, 0x1747422f,
                0x49547e0b, 0xbb3ffd08, 0xa86f0efc, 0x5a048dff, 0x8ecee914, 0x7ca56a17, 0x6ff599e3, 0x9d9e1ae0,
                0xd3d3e1ab, 0x21b862a8, 0x32e8915c, 0xc083125f, 0x144976b4, 0xe622f5b7, 0xf5720643, 0x07198540,
                0x590ab964, 0xab613a67, 0xb831c993, 0x4a5a4a90, 0x9e902e7b, 0x6cfbad78, 0x7fab5e8c, 0x8dc0dd8f,
                0xe330a81a, 0x115b2b19, 0x020bd8ed, 0xf0605bee, 0x24aa3f05, 0xd6c1bc06, 0xc5914ff2, 0x37faccf1,
                0x69e9f0d5, 0x9b8273d6, 0x88d28022, 0x7ab90321, 0xae7367ca, 0x5c18e4c9, 0x4f48173d, 0xbd23943e,
                0xf36e6f75, 0x0105ec76, 0x12551f82, 0xe03e9c81, 0x34f4f86a, 0xc69f7b69, 0xd5cf889d, 0x27a40b9e,
                0x79b737ba, 0x8bdcb4b9, 0x988c474d, 0x6ae7c44e, 0xbe2da0a5, 0x4c4623a6, 0x5f16d052, 0xad7d5351
            },
            {
                0x00000000, 0x13a29877, 0x274530ee, 0x34e7a899, 0x4e8a61dc, 0x5d28f9ab, 0x69cf5132, 0x7a6dc945,
                0x9d14c3b8, 0x8eb65bcf, 0xba51f356, 0xa9f36b21, 0xd39ea264, 0xc03c3a13, 0xf4db928a, 0xe7790afd,
                0x3fc5f181, 0x2c6769f6, 0x1880c16f, 0x0b225918, 0x714f905d, 0x62ed082a, 0x560aa0b3, 0x45a838c4,
                0xa2d13239, 0xb173aa4e, 0x859402d7, 0x96369aa0, 0xec5b53e5, 0xfff9cb92, 0xcb1e630b, 0xd8bcfb7c,
                0x7f8be302, 0x6c297b75, 0x58ced3ec, 0x4b6c4b9b, 0x310182de, 0x22a31aa9, 0x1644b230, 0x05e62a47,
                0xe29f20ba, 0xf13db8cd, 0xc5da1054, 0xd6788823, 0xac154166, 0xbfb7d911, 0x8b507188, 0x98f2e9ff,
                0x404e1283, 0x53ec8af4, 0x670b226d, 0x74a9ba1a, 0x0ec4735f, 0x1d66eb28, 0x298143b1, 0x3a23dbc6,
                0xdd5ad13b, 0xcef8494c, 0xfa1fe1d5, 0xe9bd79a2, 0x93d0b0e7, 0x80722890, 0xb4958009, 0xa737187e,
                0xff17c604, 0xecb55e73, 0xd852f6ea, 0xcbf06e9d, 0xb19da7d8, 0xa23f3faf, 0x96d89736, 0x857a0f41,
                0x620305bc, 0x71a19dcb, 0x45463552, 0x56e4ad25, 0x2c896460, 0x3f2bfc17, 0x0bcc548e, 0x186eccf9,
                0xc0d23785, 0xd370aff2, 0xe797076b, 0xf4359f1c, 0x8e585659, 0x9dface2e, 0xa91d66b7, 0xbabffec0,
                0x5dc6f43d, 0x4e646c4a, 0x7a83c4d3, 0x69215ca4, 0x134c95e1, 0x00ee0d96, 0x3409a50f, 0x27ab3d78,
                0x809c2506, 0x933ebd71, 0xa7d915e8, 0xb47b8d9f, 0xce1644da, 0xddb4dcad, 0xe9537434, 0xfaf1ec43,
                0x1d88e6be, 0x0e2a7ec9, 0x3acdd650, 0x296f4e27, 0x53028762, 0x40a01f15, 0x7447b78c, 0x67e52ffb,
                0xbf59d487, 0xacfb4cf0, 0x981ce469, 0x8bbe7c1e, 0xf1d3b55b, 0xe2712d2c, 0xd69685b5, 0xc5341dc2,
                0x224d173f, 0x31ef8f48, 0x050827d1, 0x16aabfa6, 0x6cc776e3, 0x7f65ee94, 0x4b82460d, 0x5820de7a,
                0xfbc3faf9, 0xe861628e, 0xdc86ca17, 0xcf245260, 0xb5499b25, 0xa6eb0352, 0x920cabcb, 0x81ae33bc,
                0x66d73941, 0x7575a136, 0x419209af, 0x523091d8, 0x285d589d, 0x3bffc0ea, 0x0f186873, 0x1cbaf004,
                0xc4060b78, 0xd7a4930f, 0xe3433b96, 0xf0e1a3e1, 0x8a8c6aa4, 0x992ef2d3, 0xadc95a4a, 0xbe6bc23d,
                0x5912c8c0, 0x4ab050b7, 0x7e57f82e, 0x6df56059, 0x1798a91c, 0x043a316b, 0x30dd99f2, 0x237f0185,
                0x844819fb, 0x97ea818c, 0xa30d2915, 0xb0afb162, 0xcac27827, 0xd960e050, 0xed8748c9, 0xfe25d0be,
                0x195cda43, 0x0afe4234, 0x3e19eaad, 0x2dbb72da, 0x57d6bb9f, 0x447423e8, 0x70938b71, 0x63311306,
                0xbb8de87a, 0xa82f700d, 0x9cc8d894, 0x8f6a40e3, 0xf50789a6, 0xe6a511d1, 0xd242b948, 0xc1e0213f,
                0x26992bc2, 0x353bb3b5, 0x01dc1b2c, 0x127e835b, 0x68134a1e, 0x7bb1d269, 0x4f567af0, 0x5cf4e287,
                0x04d43cfd, 0x1776a48a, 0x23910c13, 0x30339464, 0x4a5e5d21, 0x59fcc556, 0x6d1b6dcf, 0x7eb9f5b8,
                0x99c0ff45, 0x8a626732, 0xbe85cfab, 0xad2757dc, 0xd74a9e99, 0xc4e806ee, 0xf00fae77, 0xe3ad3600,
                0x3b11cd7c, 0x28b3550b, 0x1c54fd92, 0x0ff665e5, 0x759baca0, 0x663934d7, 0x52de9c4e, 0x417c0439,
                0xa6050ec4, 0xb5a796b3, 0x81403e2a, 0x92e2a65d, 0xe88f6f18, 0xfb2df76f, 0xcfca5ff6, 0xdc68c781,
                0x7b5fdfff, 0x68fd4788, 0x5c1aef11, 0x4fb87766, 0x35d5be23, 0x26772654, 0x12908ecd, 0x013216ba,
                0xe64b1c47, 0xf5e98430, 0xc10e2ca9, 0xd2acb4de, 0xa8c17d9b, 0xbb63e5ec, 0x8f844d75, 0x9c26d502,
                0x449a2e7e, 0x5738b609, 0x63df1e90, 0x707d86e7, 0x0a104fa2, 0x19b2d7d5, 0x2d557f4c, 0x3ef7e73b,
                0xd98eedc6, 0xca2c75b1, 0xfecbdd28, 0xed69455f, 0x97048c1a, 0x84a6146d, 0xb041bcf4, 0xa3e32483
            },
            {
                0x00000000, 0xa541927e, 0x4f6f520d, 0xea2ec073, 0x9edea41a, 0x3b9f3664, 0xd1b1f617, 0x74f06469,
                0x38513ec5, 0x9d10acbb, 0x773e6cc8, 0xd27ffeb6, 0xa68f9adf, 0x03ce08a1, 0xe9e0c8d2, 0x4ca15aac,
                0x70a27d8a, 0xd5e3eff4, 0x3fcd2f87, 0x9a8cbdf9, 0xee7cd990, 0x4b3d4bee, 0xa1138b9d, 0x045219e3,
                0x48f3434f, 0xedb2d131, 0x079c1142, 0xa2dd833c, 0xd62de755, 0x736c752b, 0x9942b558, 0x3c032726,
                0xe144fb14, 0x4405696a, 0xae2ba919, 0x0b6a3b67, 0x7f9a5f0e, 0xdadbcd70, 0x30f50d03, 0x95b49f7d,
                0xd915c5d1, 0x7c5457af, 0x967a97dc, 0x333b05a2, 0x47cb61cb, 0xe28af3b5, 0x08a433c6, 0xade5a1b8,
                0x91e6869e, 0x34a714e0, 0xde89d493, 0x7bc846ed, 0x0f382284, 0xaa79b0fa, 0x40577089, 0xe516e2f7,
                0xa9b7b85b, 0x0cf62a25, 0xe6d8ea56, 0x43997828, 0x37691c41, 0x92288e3f, 0x78064e4c, 0xdd47dc32,
                0xc76580d9, 0x622412a7, 0x880ad2d4, 0x2d4b40aa, 0x59bb24c3, 0xfcfab6bd, 0x16d476ce, 0xb395e4b0,
                0xff34be1c, 0x5a752c62, 0xb05bec11, 0x151a7e6f, 0x61ea1a06, 0xc4ab8878, 0x2e85480b, 0x8bc4da75,
                0xb7c7fd53, 0x12866f2d, 0xf8a8af5e, 0x5de93d20, 0x29195949, 0x8c58cb37, 0x66760b44, 0xc337993a,
                0x8f96c396, 0x2ad751e8, 0xc0f9919b, 0x65b803e5, 0x1148678c, 0xb409f5f2, 0x5e273581, 0xfb66a7ff,
                0x26217bcd, 0x8360e9b3, 0x694e29c0, 0xcc0fbbbe, 0xb8ffdfd7, 0x1dbe4da9, 0xf7908dda, 0x52d11fa4,
                0x1e704508, 0xbb31d776, 0x511f1705, 0xf45e857b, 0x80aee112, 0x25ef736c, 0xcfc1b31f, 0x6a802161,
                0x56830647, 0xf3c29439, 0x19ec544a, 0xbcadc634, 0xc85da25d, 0x6d1c3023, 0x8732f050, 0x2273622e,
                0x6ed23882, 0xcb93aafc, 0x21bd6a8f, 0x84fcf8f1, 0xf00c9c98, 0x554d0ee6, 0xbf63ce95, 0x1a225ceb,
                0x8b277743, 0x2e66e53d, 0xc448254e, 0x6109b730, 0x15f9d359, 0xb0b84127, 0x5a968154, 0xffd7132a,
                0xb3764986, 0x1637dbf8, 0xfc191b8b, 0x595889f5, 0x2da8ed9c, 0x88e97fe2, 0x62c7bf91, 0xc7862def,
                0xfb850ac9, 0x5ec498b7, 0xb4ea58c4, 0x11abcaba, 0x655baed3, 0xc01a3cad, 0x2a34fcde, 0x8f756ea0,
                0xc3d4340c, 0x6695a672, 0x8cbb6601, 0x29faf47f, 0x5d0a9016, 0xf84b0268, 0x1265c21b, 0xb7245065,
                0x6a638c57, 0xcf221e29, 0x250cde5a, 0x804d4c24, 0xf4bd284d, 0x51fcba33, 0xbbd27a40, 0x1e93e83e,
                0x5232b292, 0xf77320ec, 0x1d5de09f, 0xb81c72e1, 0xccec1688, 0x69ad84f6, 0x83834485, 0x26c2d6fb,
                0x1ac1f1dd, 0xbf8063a3, 0x55aea3d0, 0xf0ef31ae, 0x841f55c7, 0x215ec7b9, 0xcb7007ca, 0x6e3195b4,
                0x2290cf18, 0x87d15d66, 0x6dff9d15, 0xc8be0f6b, 0xbc4e6b02, 0x190ff97c, 0xf321390f, 0x5660ab71,
                0x4c42f79a, 0xe90365e4, 0x032da597, 0xa66c37e9, 0xd29c5380, 0x77ddc1fe, 0x9df3018d, 0x38b293f3,
                0x7413c95f, 0xd1525b21, 0x3b7c9b52, 0x9e3d092c, 0xeacd6d45, 0x4f8cff3b, 0xa5a23f48, 0x00e3ad36,
                0x3ce08a10, 0x99a1186e, 0x738fd81d, 0xd6ce4a63, 0xa23e2e0a, 0x077fbc74, 0xed517c07, 0x4810ee79,
                0x04b1b4d5, 0xa1f026ab, 0x4bdee6d8, 0xee9f74a6, 0x9a6f10cf, 0x3f2e82b1, 0xd50042c2, 0x7041d0bc,
                0xad060c8e, 0x08479ef0, 0xe2695e83, 0x4728ccfd, 0x33d8a894, 0x96993aea, 0x7cb7fa99, 0xd9f668e7,
                0x9557324b, 0x3016a035, 0xda386046, 0x7f79f238, 0x0b899651, 0xaec8042f, 0x44e6c45c, 0xe1a75622,
                0xdda47104, 0x78e5e37a, 0x92cb2309, 0x378ab177, 0x437ad51e, 0xe63b4760, 0x0c158713, 0xa954156d,
                0xe5f54fc1, 0x40b4ddbf, 0xaa9a1dcc, 0x0fdb8fb2, 0x7b2bebdb, 0xde6a79a5, 0x3444b9d6, 0x91052ba8
            },
            {
                0x00000000, 0xdd45aab8, 0xbf672381, 0x62228939, 0x7b2231f3, 0xa6679b4b, 0xc4451272, 0x1900b8ca,
                0xf64463e6, 0x2b01c95e, 0x49234067, 0x9466eadf, 0x8d665215, 0x5023f8ad, 0x32017194, 0xef44db2c,
                0xe964b13d, 0x34211b85, 0x560392bc, 0x8b463804, 0x924680ce, 0x4f032a76, 0x2d21a34f, 0xf06409f7,
                0x1f20d2db, 0xc2657863, 0xa047f15a, 0x7d025be2, 0x6402e328, 0xb9474990, 0xdb65c0a9, 0x06206a11,
                0xd725148b, 0x0a60be33, 0x6842370a, 0xb5079db2, 0xac072578, 0x71428fc0, 0x136006f9, 0xce25ac41,
                0x2161776d, 0xfc24ddd5, 0x9e0654ec, 0x4343fe54, 0x5a43469e, 0x8706ec26, 0xe524651f, 0x3861cfa7,
                0x3e41a5b6, 0xe3040f0e, 0x81268637, 0x5c632c8f, 0x45639445, 0x98263efd, 0xfa04b7c4, 0x27411d7c,
                0xc805c650, 0x15406ce8, 0x7762e5d1, 0xaa274f69, 0xb327f7a3, 0x6e625d1b, 0x0c40d422, 0xd1057e9a,
                0xaba65fe7, 0x76e3f55f, 0x14c17c66, 0xc984d6de, 0xd0846e14, 0x0dc1c4ac, 0x6fe34d95, 0xb2a6e72d,
                0x5de23c01, 0x80a796b9, 0xe2851f80, 0x3fc0b538, 0x26c00df2, 0xfb85a74a, 0x99a72e73, 0x44e284cb,
                0x42c2eeda, 0x9f874462, 0xfda5cd5b, 0x20e067e3, 0x39e0df29, 0xe4a57591, 0x8687fca8, 0x5bc25610,
                0xb4868d3c, 0x69c32784, 0x0be1aebd, 0xd6a40405, 0xcfa4bccf, 0x12e11677, 0x70c39f4e, 0xad8635f6,
                0x7c834b6c, 0xa1c6e1d4, 0xc3e468ed, 0x1ea1c255, 0x07a17a9f, 0xdae4d027, 0xb8c6591e, 0x6583f3a6,
                0x8ac7288a, 0x57828232, 0x35a00b0b, 0xe8e5a1b3, 0xf1e51979, 0x2ca0b3c1, 0x4e823af8, 0x93c79040,
                0x95e7fa51, 0x48a250e9, 0x2a80d9d0, 0xf7c57368, 0xeec5cba2, 0x3380611a, 0x51a2e823, 0x8ce7429b,
                0x63a399b7, 0xbee6330f, 0xdcc4ba36, 0x0181108e, 0x1881a844, 0xc5c402fc, 0xa7e68bc5, 0x7aa3217d,
                0x52a0c93f, 0x8fe56387, 0xedc7eabe, 0x30824006, 0x2982f8cc, 0xf4c75274, 0x96e5db4d, 0x4ba071f5,
                0xa4e4aad9, 0x79a10061, 0x1b838958, 0xc6c623e0, 0xdfc69b2a, 0x02833192, 0x60a1b8ab, 0xbde41213,
                0xbbc47802, 0x6681d2ba, 0x04a35b83, 0xd9e6f13b, 0xc0e649f1, 0x1da3e349, 0x7f816a70, 0xa2c4c0c8,
                0x4d801be4, 0x90c5b15c, 0xf2e73865, 0x2fa292dd, 0x36a22a17, 0xebe780af, 0x89c50996, 0x5480a32e,
                0x8585ddb4, 0x58c0770c, 0x3ae2fe35, 0xe7a7548d, 0xfea7ec47, 0x23e246ff, 0x41c0cfc6, 0x9c85657e,
                0x73c1be52, 0xae8414ea, 0xcca69dd3, 0x11e3376b, 0x08e38fa1, 0xd5a62519, 0xb784ac20, 0x6ac10698,
                0x6ce16c89, 0xb1a4c631, 0xd3864f08, 0x0ec3e5b0, 0x17c35d7a, 0xca86f7c2, 0xa8a47efb, 0x75e1d443,
                0x9aa50f6f, 0x47e0a5d7, 0x25c22cee, 0xf8878656, 0xe1873e9c, 0x3cc29424, 0x5ee01d1d, 0x83a5b7a5,
                0xf90696d8, 0x24433c60, 0x4661b559, 0x9b241fe1, 0x8224a72b, 0x5f610d93, 0x3d4384aa, 0xe0062e12,
                0x0f42f53e, 0xd2075f86, 0xb025d6bf, 0x6d607c07, 0x7460c4cd, 0xa9256e75, 0xcb07e74c, 0x16424df4,
                0x106227e5, 0xcd278d5d, 0xaf050464, 0x7240aedc, 0x6b401616, 0xb605bcae, 0xd4273597, 0x09629f2f,
                0xe6264403, 0x3b63eebb, 0x59416782, 0x8404cd3a, 0x9d0475f0, 0x4041df48, 0x22635671, 0xff26fcc9,
                0x2e238253, 0xf36628eb, 0x9144a1d2, 0x4c010b6a, 0x5501b3a0, 0x88441918, 0xea669021, 0x37233a99,
                0xd867e1b5, 0x05224b0d, 0x6700c234, 0xba45688c, 0xa345d046, 0x7e007afe, 0x1c22f3c7, 0xc167597f,
                0xc747336e, 0x1a0299d6, 0x782010ef, 0xa565ba57, 0xbc65029d, 0x6120a825, 0x0302211c, 0xde478ba4,
                0x31035088, 0xec46fa30, 0x8e647309, 0x5321d9b1, 0x4a21617b, 0x9764cbc3, 0xf54642fa, 0x2803e842
            },
            {
                0x00000000, 0x38116fac, 0x7022df58, 0x4833b0f4, 0xe045beb0, 0xd854d11c, 0x906761e8, 0xa8760e44,
                0xc5670b91, 0xfd76643d, 0xb545d4c9, 0x8d54bb65, 0x2522b521, 0x1d33da8d, 0x55006a79, 0x6d1105d5,
                0x8f2261d3, 0xb7330e7f, 0xff00be8b, 0xc711d127, 0x6f67df63, 0x5776b0cf, 0x1f45003b, 0x27546f97,
                0x4a456a42, 0x725405ee, 0x3a67b51a, 0x0276dab6, 0xaa00d4f2, 0x9211bb5e, 0xda220baa, 0xe2336406,
                0x1ba8b557, 0x23b9dafb, 0x6b8a6a0f, 0x539b05a3, 0xfbed0be7, 0xc3fc644b, 0x8bcfd4bf, 0xb3debb13,
                0xdecfbec6, 0xe6ded16a, 0xaeed619e, 0x96fc0e32, 0x3e8a0076, 0x069b6fda, 0x4ea8df2e, 0x76b9b082,
                0x948ad484, 0xac9bbb28, 0xe4a80bdc, 0xdcb96470, 0x74cf6a34, 0x4cde0598, 0x04edb56c, 0x3cfcdac0,
                0x51eddf15, 0x69fcb0b9, 0x21cf004d, 0x19de6fe1, 0xb1a861a5, 0x89b90e09, 0xc18abefd, 0xf99bd151,
                0x37516aae, 0x0f400502, 0x4773b5f6, 0x7f62da5a, 0xd714d41e, 0xef05bbb2, 0xa7360b46, 0x9f2764ea,
                0xf236613f, 0xca270e93, 0x8214be67, 0xba05d1cb, 0x1273df8f, 0x2a62b023, 0x625100d7, 0x5a406f7b,
                0xb8730b7d, 0x806264d1, 0xc851d425, 0xf040bb89, 0x5836b5cd, 0x6027da61, 0x28146a95, 0x10050539,
                0x7d1400ec, 0x45056f40, 0x0d36dfb4, 0x3527b018, 0x9d51be5c, 0xa540d1f0, 0xed736104, 0xd5620ea8,
                0x2cf9dff9, 0x14e8b055, 0x5cdb00a1, 0x64ca6f0d, 0xccbc6149, 0xf4ad0ee5, 0xbc9ebe11, 0x848fd1bd,
                0xe99ed468, 0xd18fbbc4, 0x99bc0b30, 0xa1ad649c, 0x09db6ad8, 0x31ca0574, 0x79f9b580, 0x41e8da2c,
                0xa3dbbe2a, 0x9bcad186, 0xd3f96172, 0xebe80ede, 0x439e009a, 0x7b8f6f36, 0x33bcdfc2, 0x0badb06e,
                0x66bcb5bb, 0x5eadda17, 0x169e6ae3, 0x2e8f054f, 0x86f90b0b, 0xbee864a7, 0xf6dbd453, 0xcecabbff,
                0x6ea2d55c, 0x56b3baf0, 0x1e800a04, 0x269165a8, 0x8ee76bec, 0xb6f60440, 0xfec5b4b4, 0xc6d4db18,
                0xabc5decd, 0x93d4b161, 0xdbe70195, 0xe3f66e39, 0x4b80607d, 0x73910fd1, 0x3ba2bf25, 0x03b3d089,
                0xe180b48f, 0xd991db23, 0x91a26bd7, 0xa9b3047b, 0x01c50a3f, 0x39d46593, 0x71e7d567, 0x49f6bacb,
                0x24e7bf1e, 0x1cf6d0b2, 0x54c56046, 0x6cd40fea, 0xc4a201ae, 0xfcb36e02, 0xb480def6, 0x8c91b15a,
                0x750a600b, 0x4d1b0fa7, 0x0528bf53, 0x3d39d0ff, 0x954fdebb, 0xad5eb117, 0xe56d01e3, 0xdd7c6e4f,
                0xb06d6b9a, 0x887c0436, 0xc04fb4c2, 0xf85edb6e, 0x5028d52a, 0x6839ba86, 0x200a0a72, 0x181b65de,
                0xfa2801d8, 0xc2396e74, 0x8a0ade80, 0xb21bb12c, 0x1a6dbf68, 0x227cd0c4, 0x6a4f6030, 0x525e0f9c,
                0x3f4f0a49, 0x075e65e5, 0x4f6dd511, 0x777cbabd, 0xdf0ab4f9, 0xe71bdb55, 0xaf286ba1, 0x9739040d,
                0x59f3bff2, 0x61e2d05e, 0x29d160aa, 0x11c00f06, 0xb9b60142, 0x81a76eee, 0xc994de1a, 0xf185b1b6,
                0x9c94b463, 0xa485dbcf, 0xecb66b3b, 0xd4a70497, 0x7cd10ad3, 0x44c0657f, 0x0cf3d58b, 0x34e2ba27,
                0xd6d1de21, 0xeec0b18d, 0xa6f30179, 0x9ee26ed5, 0x36946091, 0x0e850f3d, 0x46b6bfc9, 0x7ea7d065,
                0x13b6d5b0, 0x2ba7ba1c, 0x63940ae8, 0x5b856544, 0xf3f36b00, 0xcbe204ac, 0x83d1b458, 0xbbc0dbf4,
                0x425b0aa5, 0x7a4a6509, 0x3279d5fd, 0x0a68ba51, 0xa21eb415, 0x9a0fdbb9, 0xd23c6b4d, 0xea2d04e1,
                0x873c0134, 0xbf2d6e98, 0xf71ede6c, 0xcf0fb1c0, 0x6779bf84, 0x5f68d028, 0x175b60dc, 0x2f4a0f70,
                0xcd796b76, 0xf56804da, 0xbd5bb42e, 0x854adb82, 0x2d3cd5c6, 0x152dba6a, 0x5d1e0a9e, 0x650f6532,
                0x081e60e7, 0x300f0f4b, 0x783cbfbf, 0x402dd013, 0xe85bde57, 0xd04ab1fb, 0x9879010f, 0xa0686ea3
            },
            {
                0x00000000, 0xef306b19, 0xdb8ca0c3, 0x34bccbda, 0xb2f53777, 0x5dc55c6e, 0x697997b4, 0x8649fcad,
                0x6006181f, 0x8f367306, 0xbb8ab8dc, 0x54bad3c5, 0xd2f32f68, 0x3dc34471, 0x097f8fab, 0xe64fe4b2,
                0xc00c303e, 0x2f3c5b27, 0x1b8090fd, 0xf4b0fbe4, 0x72f90749, 0x9dc96c50, 0xa975a78a, 0x4645cc93,
                0xa00a2821, 0x4f3a4338, 0x7b8688e2, 0x94b6e3fb, 0x12ff1f56, 0xfdcf744f, 0xc973bf95, 0x2643d48c,
                0x85f4168d, 0x6ac47d94, 0x5e78b64e, 0xb148dd57, 0x370121fa, 0xd8314ae3, 0xec8d8139, 0x03bdea20,
                0xe5f20e92, 0x0ac2658b, 0x3e7eae51, 0xd14ec548, 0x570739e5, 0xb83752fc, 0x8c8b9926, 0x63bbf23f,
                0x45f826b3, 0xaac84daa, 0x9e748670, 0x7144ed69, 0xf70d11c4, 0x183d7add, 0x2c81b107, 0xc3b1da1e,
                0x25fe3eac, 0xcace55b5, 0xfe729e6f, 0x1142f576, 0x970b09db, 0x783b62c2, 0x4c87a918, 0xa3b7c201,
                0x0e045beb, 0xe13430f2, 0xd588fb28, 0x3ab89031, 0xbcf16c9c, 0x53c10785, 0x677dcc5f, 0x884da746,
                0x6e0243f4, 0x813228ed, 0xb58ee337, 0x5abe882e, 0xdcf77483, 0x33c71f9a, 0x077bd440, 0xe84bbf59,
                0xce086bd5, 0x213800cc, 0x1584cb16, 0xfab4a00f, 0x7cfd5ca2, 0x93cd37bb, 0xa771fc61, 0x48419778,
                0xae0e73ca, 0x413e18d3, 0x7582d309, 0x9ab2b810, 0x1cfb44bd, 0xf3cb2fa4, 0xc777e47e, 0x28478f67,
                0x8bf04d66, 0x64c0267f, 0x507ceda5, 0xbf4c86bc, 0x39057a11, 0xd6351108, 0xe289dad2, 0x0db9b1cb,
                0xebf65579, 0x04c63e60, 0x307af5ba, 0xdf4a9ea3, 0x5903620e, 0xb6330917, 0x828fc2cd, 0x6dbfa9d4,
                0x4bfc7d58, 0xa4cc1641, 0x9070dd9b, 0x7f40b682, 0xf9094a2f, 0x16392136, 0x2285eaec, 0xcdb581f5,
                0x2bfa6547, 0xc4ca0e5e, 0xf076c584, 0x1f46ae9d, 0x990f5230, 0x763f3929, 0x4283f2f3, 0xadb399ea,
                0x1c08b7d6, 0xf338dccf, 0xc7841715, 0x28b47c0c, 0xaefd80a1, 0x41cdebb8, 0x75712062, 0x9a414b7b,
                0x7c0eafc9, 0x933ec4d0, 0xa7820f0a, 0x48b26413, 0xcefb98be, 0x21cbf3a7, 0x1577387d, 0xfa475364,
                0xdc0487e8, 0x3334ecf1, 0x0788272b, 0xe8b84c32, 0x6ef1b09f, 0x81c1db86, 0xb57d105c, 0x5a4d7b45,
                0xbc029ff7, 0x5332f4ee, 0x678e3f34, 0x88be542d, 0x0ef7a880, 0xe1c7c399, 0xd57b0843, 0x3a4b635a,
                0x99fca15b, 0x76ccca42, 0x42700198, 0xad406a81, 0x2b09962c, 0xc439fd35, 0xf08536ef, 0x1fb55df6,
                0xf9fab944, 0x16cad25d, 0x22761987, 0xcd46729e, 0x4b0f8e33, 0xa43fe52a, 0x90832ef0, 0x7fb345e9,
                0x59f09165, 0xb6c0fa7c, 0x827c31a6, 0x6d4c5abf, 0xeb05a612, 0x0435cd0b, 0x308906d1, 0xdfb96dc8,
                0x39f6897a, 0xd6c6e263, 0xe27a29b9, 0x0d4a42a0, 0x8b03be0d, 0x6433d514, 0x508f1ece, 0xbfbf75d7,
                0x120cec3d, 0xfd3c8724, 0xc9804cfe, 0x26b027e7, 0xa0f9db4a, 0x4fc9b053, 0x7b757b89, 0x94451090,
                0x720af422, 0x9d3a9f3b, 0xa98654e1, 0x46b63ff8, 0xc0ffc355, 0x2fcfa84c, 0x1b736396, 0xf443088f,
                0xd200dc03, 0x3d30b71a, 0x098c7cc0, 0xe6bc17d9, 0x60f5eb74, 0x8fc5806d, 0xbb794bb7, 0x544920ae,
                0xb206c41c, 0x5d36af05, 0x698a64df, 0x86ba0fc6, 0x00f3f36b, 0xefc39872, 0xdb7f53a8, 0x344f38b1,
                0x97f8fab0, 0x78c891a9, 0x4c745a73, 0xa344316a, 0x250dcdc7, 0xca3da6de, 0xfe816d04, 0x11b1061d,
                0xf7fee2af, 0x18ce89b6, 0x2c72426c, 0xc3422975, 0x450bd5d8, 0xaa3bbec1, 0x9e87751b, 0x71b71e02,
                0x57f4ca8e, 0xb8c4a197, 0x8c786a4d, 0x63480154, 0xe501fdf9, 0x0a3196e0, 0x3e8d5d3a, 0xd1bd3623,
                0x37f2d291, 0xd8c2b988, 0xec7e7252, 0x034e194b, 0x8507e5e6, 0x6a378eff, 0x5e8b4525, 0xb1bb2e3c
            },
            {
                0x00000000, 0x68032cc8, 0xd0065990, 0xb8057558, 0xa5e0c5d1, 0xcde3e919, 0x75e69c41, 0x1de5b089,
                0x4e2dfd53, 0x262ed19b, 0x9e2ba4c3, 0xf628880b, 0xebcd3882, 0x83ce144a, 0x3bcb6112, 0x53c84dda,
                0x9c5bfaa6, 0xf458d66e, 0x4c5da336, 0x245e8ffe, 0x39bb3f77, 0x51b813bf, 0xe9bd66e7, 0x81be4a2f,
                0xd27607f5, 0xba752b3d, 0x02705e65, 0x6a7372ad, 0x7796c224, 0x1f95eeec, 0xa7909bb4, 0xcf93b77c,
                0x3d5b83bd, 0x5558af75, 0xed5dda2d, 0x855ef6e5, 0x98bb466c, 0xf0b86aa4, 0x48bd1ffc, 0x20be3334,
                0x73767eee, 0x1b755226, 0xa370277e, 0xcb730bb6, 0xd696bb3f, 0xbe9597f7, 0x0690e2af, 0x6e93ce67,
                0xa100791b, 0xc90355d3, 0x7106208b, 0x19050c43, 0x04e0bcca, 0x6ce39002, 0xd4e6e55a, 0xbce5c992,
                0xef2d8448, 0x872ea880, 0x3f2bddd8, 0x5728f110, 0x4acd4199, 0x22ce6d51, 0x9acb1809, 0xf2c834c1,
                0x7ab7077a, 0x12b42bb2, 0xaab15eea, 0xc2b27222, 0xdf57c2ab, 0xb754ee63, 0x0f519b3b, 0x6752b7f3,
                0x349afa29, 0x5c99d6e1, 0xe49ca3b9, 0x8c9f8f71, 0x917a3ff8, 0xf9791330, 0x417c6668, 0x297f4aa0,
                0xe6ecfddc, 0x8eefd114, 0x36eaa44c, 0x5ee98884, 0x430c380d, 0x2b0f14c5, 0x930a619d, 0xfb094d55,
                0xa8c1008f, 0xc0c22c47, 0x78c7591f, 0x10c475d7, 0x0d21c55e, 0x6522e996, 0xdd279cce, 0xb524b006,
                0x47ec84c7, 0x2fefa80f, 0x97eadd57, 0xffe9f19f, 0xe20c4116, 0x8a0f6dde, 0x320a1886, 0x5a09344e,
                0x09c17994, 0x61c2555c, 0xd9c72004, 0xb1c40ccc, 0xac21bc45, 0xc422908d, 0x7c27e5d5, 0x1424c91d,
                0xdbb77e61, 0xb3b452a9, 0x0bb127f1, 0x63b20b39, 0x7e57bbb0, 0x16549778, 0xae51e220, 0xc652cee8,
                0x959a8332, 0xfd99affa, 0x459cdaa2, 0x2d9ff66a, 0x307a46e3, 0x58796a2b, 0xe07c1f73, 0x887f33bb,
                0xf56e0ef4, 0x9d6d223c, 0x25685764, 0x4d6b7bac, 0x508ecb25, 0x388de7ed, 0x808892b5, 0xe88bbe7d,
                0xbb43f3a7, 0xd340df6f, 0x6b45aa37, 0x034686ff, 0x1ea33676, 0x76a01abe, 0xcea56fe6, 0xa6a6432e,
                0x6935f452, 0x0136d89a, 0xb933adc2, 0xd130810a, 0xccd53183, 0xa4d61d4b, 0x1cd36813, 0x74d044db,
                0x27180901, 0x4f1b25c9, 0xf71e5091, 0x9f1d7c59, 0x82f8ccd0, 0xeafbe018, 0x52fe9540, 0x3afdb988,
                0xc8358d49, 0xa036a181, 0x1833d4d9, 0x7030f811, 0x6dd54898, 0x05d66450, 0xbdd31108, 0xd5d03dc0,
                0x8618701a, 0xee1b5cd2, 0x561e298a, 0x3e1d0542, 0x23f8b5cb, 0x4bfb9903, 0xf3feec5b, 0x9bfdc093,
                0x546e77ef, 0x3c6d5b27, 0x84682e7f, 0xec6b02b7, 0xf18eb23e, 0x998d9ef6, 0x2188ebae, 0x498bc766,
                0x1a438abc, 0x7240a674, 0xca45d32c, 0xa246ffe4, 0xbfa34f6d, 0xd7a063a5, 0x6fa516fd, 0x07a63a35,
                0x8fd9098e, 0xe7da2546, 0x5fdf501e, 0x37dc7cd6, 0x2a39cc5f, 0x423ae097, 0xfa3f95cf, 0x923cb907,
                0xc1f4f4dd, 0xa9f7d815, 0x11f2ad4d, 0x79f18185, 0x6414310c, 0x0c171dc4, 0xb412689c, 0xdc114454,
                0x1382f328, 0x7b81dfe0, 0xc384aab8, 0xab878670, 0xb66236f9, 0xde611a31, 0x66646f69, 0x0e6743a1,
                0x5daf0e7b, 0x35ac22b3, 0x8da957eb, 0xe5aa7b23, 0xf84fcbaa, 0x904ce762, 0x2849923a, 0x404abef2,
                0xb2828a33, 0xda81a6fb, 0x6284d3a3, 0x0a87ff6b, 0x17624fe2, 0x7f61632a, 0xc7641672, 0xaf673aba,
                0xfcaf7760, 0x94ac5ba8, 0x2ca92ef0, 0x44aa0238, 0x594fb2b1, 0x314c9e79, 0x8949eb21, 0xe14ac7e9,
                0x2ed97095, 0x46da5c5d, 0xfedf2905, 0x96dc05cd, 0x8b39b544, 0xe33a998c, 0x5b3fecd4, 0x333cc01c,
                0x60f48dc6, 0x08f7a10e, 0xb0f2d456, 0xd8f1f89e, 0xc5144817, 0xad1764df, 0x15121187, 0x7d113d4f
            },
            {
                0x00000000, 0x493c7d27, 0x9278fa4e, 0xdb448769, 0x211d826d, 0x6821ff4a, 0xb3657823, 0xfa590504,
                0x423b04da, 0x0b0779fd, 0xd043fe94, 0x997f83b3, 0x632686b7, 0x2a1afb90, 0xf15e7cf9, 0xb86201de,
                0x847609b4, 0xcd4a7493, 0x160ef3fa, 0x5f328edd, 0xa56b8bd9, 0xec57f6fe, 0x37137197, 0x7e2f0cb0,
                0xc64d0d6e, 0x8f717049, 0x5435f720, 0x1d098a07, 0xe7508f03, 0xae6cf224, 0x7528754d, 0x3c14086a,
                0x0d006599, 0x443c18be, 0x9f789fd7, 0xd644e2f0, 0x2c1de7f4, 0x65219ad3, 0xbe651dba, 0xf759609d,
                0x4f3b6143, 0x06071c64, 0xdd439b0d, 0x947fe62a, 0x6e26e32e, 0x271a9e09, 0xfc5e1960, 0xb5626447,
                0x89766c2d, 0xc04a110a, 0x1b0e9663, 0x5232eb44, 0xa86bee40, 0xe1579367, 0x3a13140e, 0x732f6929,
                0xcb4d68f7, 0x827115d0, 0x593592b9, 0x1009ef9e, 0xea50ea9a, 0xa36c97bd, 0x782810d4, 0x31146df3,
                0x1a00cb32, 0x533cb615, 0x8878317c, 0xc1444c5b, 0x3b1d495f, 0x72213478, 0xa965b311, 0xe059ce36,
                0x583bcfe8, 0x1107b2cf, 0xca4335a6, 0x837f4881, 0x79264d85, 0x301a30a2, 0xeb5eb7cb, 0xa262caec,
                0x9e76c286, 0xd74abfa1, 0x0c0e38c8, 0x453245ef, 0xbf6b40eb, 0xf6573dcc, 0x2d13baa5, 0x642fc782,
                0xdc4dc65c, 0x9571bb7b, 0x4e353c12, 0x07094135, 0xfd504431, 0xb46c3916, 0x6f28be7f, 0x2614c358,
                0x1700aeab, 0x5e3cd38c, 0x857854e5, 0xcc4429c2, 0x361d2cc6, 0x7f2151e1, 0xa465d688, 0xed59abaf,
                0x553baa71, 0x1c07d756, 0xc743503f, 0x8e7f2d18, 0x7426281c, 0x3d1a553b, 0xe65ed252, 0xaf62af75,
                0x9376a71f, 0xda4ada38, 0x010e5d51, 0x48322076, 0xb26b2572, 0xfb575855, 0x2013df3c, 0x692fa21b,
                0xd14da3c5, 0x9871dee2, 0x4335598b, 0x0a0924ac, 0xf05021a8, 0xb96c5c8f, 0x6228dbe6, 0x2b14a6c1,
                0x34019664, 0x7d3deb43, 0xa6796c2a, 0xef45110d, 0x151c1409, 0x5c20692e, 0x8764ee47, 0xce589360,
                0x763a92be, 0x3f06ef99, 0xe44268f0, 0xad7e15d7, 0x572710d3, 0x1e1b6df4, 0xc55fea9d, 0x8c6397ba,
                0xb0779fd0, 0xf94be2f7, 0x220f659e, 0x6b3318b9, 0x916a1dbd, 0xd856609a, 0x0312e7f3, 0x4a2e9ad4,
                0xf24c9b0a, 0xbb70e62d, 0x60346144, 0x29081c63, 0xd3511967, 0x9a6d6440, 0x4129e329, 0x08159e0e,
                0x3901f3fd, 0x703d8eda, 0xab7909b3, 0xe2457494, 0x181c7190, 0x51200cb7, 0x8a648bde, 0xc358f6f9,
                0x7b3af727, 0x32068a00, 0xe9420d69, 0xa07e704e, 0x5a27754a, 0x131b086d, 0xc85f8f04, 0x8163f223,
                0xbd77fa49, 0xf44b876e, 0x2f0f0007, 0x66337d20, 0x9c6a7824, 0xd5560503, 0x0e12826a, 0x472eff4d,
                0xff4cfe93, 0xb67083b4, 0x6d3404dd, 0x240879fa, 0xde517cfe, 0x976d01d9, 0x4c2986b0, 0x0515fb97,
                0x2e015d56, 0x673d2071, 0xbc79a718, 0xf545da3f, 0x0f1cdf3b, 0x4620a21c, 0x9d642575, 0xd4585852,
                0x6c3a598c, 0x250624ab, 0xfe42a3c2, 0xb77edee5, 0x4d27dbe1, 0x041ba6c6, 0xdf5f21af, 0x96635c88,
                0xaa7754e2, 0xe34b29c5, 0x380faeac, 0x7133d38b, 0x8b6ad68f, 0xc256aba8, 0x19122cc1, 0x502e51e6,
                0xe84c5038, 0xa1702d1f, 0x7a34aa76, 0x3308d751, 0xc951d255, 0x806daf72, 0x5b29281b, 0x1215553c,
                0x230138cf, 0x6a3d45e8, 0xb179c281, 0xf845bfa6, 0x021cbaa2, 0x4b20c785, 0x906440ec, 0xd9583dcb,
                0x613a3c15, 0x28064132, 0xf342c65b, 0xba7ebb7c, 0x4027be78, 0x091bc35f, 0xd25f4436, 0x9b633911,
                0xa777317b, 0xee4b4c5c, 0x350fcb35, 0x7c33b612, 0x866ab316, 0xcf56ce31, 0x14124958, 0x5d2e347f,
                0xe54c35a1, 0xac704886, 0x7734cfef, 0x3e08b2c8, 0xc451b7cc, 0x8d6dcaeb, 0x56294d82, 0x1f1530a5
            }
#endif//SIMD_BIG_ENDIAN
        };

        uint32_t Crc32c(const void * src, size_t size)
        {
            const uint8_t * p = (const uint8_t*)src;
            uint32_t crc = 0xFFFFFFFF;

            for (; ((uintptr_t)p & (sizeof(uint32_t) - 1)) != 0 && size > 0; ++p, --size)
            {
#ifdef SIMD_BIG_ENDIAN
                crc = Crc32cTable[0][((crc >> 24) ^ *p) & 0xFF] ^ (crc << 8);
#else
                crc = Crc32cTable[0][(crc ^ *p) & 0xFF] ^ (crc >> 8);
#endif
            }

            for (; size >= sizeof(uint64_t); size -= sizeof(uint64_t))
            {
                crc ^= *(uint32_t *)p;
                p += sizeof(uint32_t);
                uint32_t next = *(uint32_t *)p;
                p += sizeof(uint32_t);
                crc =
#ifdef SIMD_BIG_ENDIAN
                    Crc32cTable[4][(crc) & 0xFF] ^
                    Crc32cTable[5][(crc >> 8) & 0xFF] ^
                    Crc32cTable[6][(crc >> 16) & 0xFF] ^
                    Crc32cTable[7][(crc >> 24)] ^
                    Crc32cTable[0][(next) & 0xFF] ^
                    Crc32cTable[1][(next >> 8) & 0xFF] ^
                    Crc32cTable[2][(next >> 16) & 0xFF] ^
                    Crc32cTable[3][(next >> 24)];
#else
                    Crc32cTable[7][(crc) & 0xFF] ^
                    Crc32cTable[6][(crc >> 8) & 0xFF] ^
                    Crc32cTable[5][(crc >> 16) & 0xFF] ^
                    Crc32cTable[4][(crc >> 24)] ^
                    Crc32cTable[3][(next) & 0xFF] ^
                    Crc32cTable[2][(next >> 8) & 0xFF] ^
                    Crc32cTable[1][(next >> 16) & 0xFF] ^
                    Crc32cTable[0][(next >> 24)];
#endif
            }

#ifdef SIMD_BIG_ENDIAN
            for (; size > 0; ++p, size--)
                crc = Crc32cTable[0][((crc >> 24) ^ *p) & 0xFF] ^ (crc << 8);

            crc =
                ((crc << 24) & 0xFF000000) |
                ((crc << 8) & 0x00FF0000) |
                ((crc >> 8) & 0x0000FF00) |
                ((crc >> 24) & 0x000000FF);
#else
            for (; size > 0; ++p, size--)
                crc = Crc32cTable[0][(crc ^ *p) & 0xFF] ^ (crc >> 8);
#endif
            return (~crc);
        }
    }
}
