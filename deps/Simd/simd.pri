
# include

INCLUDEPATH += $$PWD/
DEPENDPATH  += $$PWD/

HEADERS     += \
            $$PWD/SimdAllocator.hpp \
            $$PWD/SimdArray.h \
            $$PWD/SimdAvx1.h \
            $$PWD/SimdAvx2.h \
            $$PWD/SimdAvx512bw.h \
            $$PWD/SimdAvx512f.h \
            $$PWD/SimdBase.h \
            $$PWD/SimdBayer.h \
            $$PWD/SimdCompare.h \
            $$PWD/SimdConfig.h \
            $$PWD/SimdConst.h \
            $$PWD/SimdContour.hpp \
            $$PWD/SimdConversion.h \
            $$PWD/SimdCpu.h \
            $$PWD/SimdDefs.h \
            $$PWD/SimdDetection.h \
            $$PWD/SimdDetection.hpp \
            $$PWD/SimdDrawing.hpp \
            $$PWD/SimdEnable.h \
            $$PWD/SimdExp.h \
            $$PWD/SimdExtract.h \
            $$PWD/SimdFont.hpp \
            $$PWD/SimdFrame.hpp \
            $$PWD/SimdGemm.h \
            $$PWD/SimdImageMatcher.hpp \
            $$PWD/SimdInit.h \
            $$PWD/SimdIntegral.h \
            $$PWD/SimdLib.h \
            $$PWD/SimdLib.hpp \
            $$PWD/SimdLoad.h \
            $$PWD/SimdLog.h \
            $$PWD/SimdMath.h \
            $$PWD/SimdMemory.h \
            $$PWD/SimdMotion.hpp \
            $$PWD/SimdMsa.h \
            $$PWD/SimdNeon.h \
            $$PWD/SimdNeural.h \
            $$PWD/SimdNeural.hpp \
            $$PWD/SimdParallel.hpp \
            $$PWD/SimdPerformance.h \
            $$PWD/SimdPixel.hpp \
            $$PWD/SimdPoint.hpp \
            $$PWD/SimdPow.h \
            $$PWD/SimdPyramid.hpp \
            $$PWD/SimdRectangle.hpp \
            $$PWD/SimdResizer.h \
            $$PWD/SimdRuntime.h \
            $$PWD/SimdSet.h \
            $$PWD/SimdShift.hpp \
            $$PWD/SimdSse1.h \
            $$PWD/SimdSse2.h \
            $$PWD/SimdSse3.h \
            $$PWD/SimdSse41.h \
            $$PWD/SimdSse42.h \
            $$PWD/SimdSsse3.h \
            $$PWD/SimdStore.h \
            $$PWD/SimdStream.h \
            $$PWD/SimdSynet.h \
            $$PWD/SimdSynetConvolution32f.h \
            $$PWD/SimdSynetConvolution32fCommon.h \
            $$PWD/SimdSynetDeconvolution32f.h \
            $$PWD/SimdSynetMergedConvolution32f.h \
            $$PWD/SimdTime.h \
            $$PWD/SimdTranspose.h \
            $$PWD/SimdUpdate.h \
            $$PWD/SimdVersion.h \
            $$PWD/SimdView.hpp \
            $$PWD/SimdVmx.h \
            $$PWD/SimdVsx.h \
            $$PWD/SimdWinograd.h \
            $$PWD/SimdXml.hpp \

SOURCES     += \
            $$PWD/SimdAvx1Fill.cpp \
            $$PWD/SimdAvx1Float32.cpp \
            $$PWD/SimdAvx1Gemm32f.cpp \
            $$PWD/SimdAvx1HogLite.cpp \
            $$PWD/SimdAvx1Neural.cpp \
            $$PWD/SimdAvx1Resizer.cpp \
            $$PWD/SimdAvx1SquaredDifferenceSum.cpp \
            $$PWD/SimdAvx1Svm.cpp \
            $$PWD/SimdAvx1Synet.cpp \
            $$PWD/SimdAvx1SynetActivation.cpp \
            $$PWD/SimdAvx1SynetConversion.cpp \
            $$PWD/SimdAvx1SynetConvolution32f.cpp \
            $$PWD/SimdAvx1SynetConvolution32fNhwcDirect.cpp \
            $$PWD/SimdAvx1SynetDeconvolution32f.cpp \
            $$PWD/SimdAvx1SynetFused.cpp \
            $$PWD/SimdAvx1SynetMergedConvolution32f.cpp \
            $$PWD/SimdAvx1SynetPooling.cpp \
            $$PWD/SimdAvx1Winograd.cpp \
            $$PWD/SimdAvx2AbsDifference.cpp \
            $$PWD/SimdAvx2AbsDifferenceSum.cpp \
            $$PWD/SimdAvx2AbsGradientSaturatedSum.cpp \
            $$PWD/SimdAvx2AddFeatureDifference.cpp \
            $$PWD/SimdAvx2AlphaBlending.cpp \
            $$PWD/SimdAvx2Background.cpp \
            $$PWD/SimdAvx2BayerToBgr.cpp \
            $$PWD/SimdAvx2BayerToBgra.cpp \
            $$PWD/SimdAvx2BgraToGray.cpp \
            $$PWD/SimdAvx2BgraToYuv.cpp \
            $$PWD/SimdAvx2BgrToBgra.cpp \
            $$PWD/SimdAvx2BgrToGray.cpp \
            $$PWD/SimdAvx2BgrToRgb.cpp \
            $$PWD/SimdAvx2BgrToYuv.cpp \
            $$PWD/SimdAvx2Binarization.cpp \
            $$PWD/SimdAvx2Conditional.cpp \
            $$PWD/SimdAvx2Deinterleave.cpp \
            $$PWD/SimdAvx2Detection.cpp \
            $$PWD/SimdAvx2EdgeBackground.cpp \
            $$PWD/SimdAvx2Fill.cpp \
            $$PWD/SimdAvx2Float16.cpp \
            $$PWD/SimdAvx2Float32.cpp \
            $$PWD/SimdAvx2GaussianBlur3x3.cpp \
            $$PWD/SimdAvx2Gemm32f.cpp \
            $$PWD/SimdAvx2GrayToBgr.cpp \
            $$PWD/SimdAvx2GrayToBgra.cpp \
            $$PWD/SimdAvx2Histogram.cpp \
            $$PWD/SimdAvx2Hog.cpp \
            $$PWD/SimdAvx2HogLite.cpp \
            $$PWD/SimdAvx2Int16ToGray.cpp \
            $$PWD/SimdAvx2Integral.cpp \
            $$PWD/SimdAvx2Interference.cpp \
            $$PWD/SimdAvx2Interleave.cpp \
            $$PWD/SimdAvx2Laplace.cpp \
            $$PWD/SimdAvx2Lbp.cpp \
            $$PWD/SimdAvx2MeanFilter3x3.cpp \
            $$PWD/SimdAvx2MedianFilter.cpp \
            $$PWD/SimdAvx2Neural.cpp \
            $$PWD/SimdAvx2Operation.cpp \
            $$PWD/SimdAvx2Reduce.cpp \
            $$PWD/SimdAvx2ReduceGray2x2.cpp \
            $$PWD/SimdAvx2ReduceGray3x3.cpp \
            $$PWD/SimdAvx2ReduceGray4x4.cpp \
            $$PWD/SimdAvx2ReduceGray5x5.cpp \
            $$PWD/SimdAvx2Reorder.cpp \
            $$PWD/SimdAvx2ResizeBilinear.cpp \
            $$PWD/SimdAvx2Resizer.cpp \
            $$PWD/SimdAvx2Segmentation.cpp \
            $$PWD/SimdAvx2ShiftBilinear.cpp \
            $$PWD/SimdAvx2Sobel.cpp \
            $$PWD/SimdAvx2SquaredDifferenceSum.cpp \
            $$PWD/SimdAvx2Statistic.cpp \
            $$PWD/SimdAvx2StatisticMoments.cpp \
            $$PWD/SimdAvx2StretchGray2x2.cpp \
            $$PWD/SimdAvx2Synet.cpp \
            $$PWD/SimdAvx2SynetActivation.cpp \
            $$PWD/SimdAvx2SynetConversion.cpp \
            $$PWD/SimdAvx2SynetConvolution32f.cpp \
            $$PWD/SimdAvx2SynetConvolution32fNhwcDirect.cpp \
            $$PWD/SimdAvx2SynetDeconvolution32f.cpp \
            $$PWD/SimdAvx2SynetMergedConvolution32f.cpp \
            $$PWD/SimdAvx2SynetPooling.cpp \
            $$PWD/SimdAvx2Texture.cpp \
            $$PWD/SimdAvx2YuvToBgr.cpp \
            $$PWD/SimdAvx2YuvToBgra.cpp \
            $$PWD/SimdAvx2YuvToHue.cpp \
            $$PWD/SimdAvx512bwAbsDifferenceSum.cpp \
            $$PWD/SimdAvx512bwAbsGradientSaturatedSum.cpp \
            $$PWD/SimdAvx512bwAddFeatureDifference.cpp \
            $$PWD/SimdAvx512bwAlphaBlending.cpp \
            $$PWD/SimdAvx512bwBackground.cpp \
            $$PWD/SimdAvx512bwBayerToBgr.cpp \
            $$PWD/SimdAvx512bwBayerToBgra.cpp \
            $$PWD/SimdAvx512bwBgraToBayer.cpp \
            $$PWD/SimdAvx512bwBgraToBgr.cpp \
            $$PWD/SimdAvx512bwBgraToGray.cpp \
            $$PWD/SimdAvx512bwBgraToYuv.cpp \
            $$PWD/SimdAvx512bwBgrToBayer.cpp \
            $$PWD/SimdAvx512bwBgrToBgra.cpp \
            $$PWD/SimdAvx512bwBgrToGray.cpp \
            $$PWD/SimdAvx512bwBgrToRgb.cpp \
            $$PWD/SimdAvx512bwBgrToYuv.cpp \
            $$PWD/SimdAvx512bwBinarization.cpp \
            $$PWD/SimdAvx512bwConditional.cpp \
            $$PWD/SimdAvx512bwDeinterleave.cpp \
            $$PWD/SimdAvx512bwDetection.cpp \
            $$PWD/SimdAvx512bwEdgeBackground.cpp \
            $$PWD/SimdAvx512bwFill.cpp \
            $$PWD/SimdAvx512bwFloat16.cpp \
            $$PWD/SimdAvx512bwFloat32.cpp \
            $$PWD/SimdAvx512bwGaussianBlur3x3.cpp \
            $$PWD/SimdAvx512bwGrayToBgr.cpp \
            $$PWD/SimdAvx512bwGrayToBgra.cpp \
            $$PWD/SimdAvx512bwHistogram.cpp \
            $$PWD/SimdAvx512bwHog.cpp \
            $$PWD/SimdAvx512bwHogLite.cpp \
            $$PWD/SimdAvx512bwInt16ToGray.cpp \
            $$PWD/SimdAvx512bwIntegral.cpp \
            $$PWD/SimdAvx512bwInterference.cpp \
            $$PWD/SimdAvx512bwInterleave.cpp \
            $$PWD/SimdAvx512bwLaplace.cpp \
            $$PWD/SimdAvx512bwLbp.cpp \
            $$PWD/SimdAvx512bwMeanFilter3x3.cpp \
            $$PWD/SimdAvx512bwMedianFilter.cpp \
            $$PWD/SimdAvx512bwNeural.cpp \
            $$PWD/SimdAvx512bwOperation.cpp \
            $$PWD/SimdAvx512bwReduce.cpp \
            $$PWD/SimdAvx512bwReduceGray2x2.cpp \
            $$PWD/SimdAvx512bwReduceGray3x3.cpp \
            $$PWD/SimdAvx512bwReduceGray4x4.cpp \
            $$PWD/SimdAvx512bwReduceGray5x5.cpp \
            $$PWD/SimdAvx512bwReorder.cpp \
            $$PWD/SimdAvx512bwResizeBilinear.cpp \
            $$PWD/SimdAvx512bwResizer.cpp \
            $$PWD/SimdAvx512bwSegmentation.cpp \
            $$PWD/SimdAvx512bwShiftBilinear.cpp \
            $$PWD/SimdAvx512bwSobel.cpp \
            $$PWD/SimdAvx512bwSquaredDifferenceSum.cpp \
            $$PWD/SimdAvx512bwStatistic.cpp \
            $$PWD/SimdAvx512bwStatisticMoments.cpp \
            $$PWD/SimdAvx512bwStretchGray2x2.cpp \
            $$PWD/SimdAvx512bwSynetConversion.cpp \
            $$PWD/SimdAvx512bwTexture.cpp \
            $$PWD/SimdAvx512bwYuvToBgr.cpp \
            $$PWD/SimdAvx512bwYuvToBgra.cpp \
            $$PWD/SimdAvx512bwYuvToHue.cpp \
            $$PWD/SimdAvx512fFill.cpp \
            $$PWD/SimdAvx512fGemm32f.cpp \
            $$PWD/SimdAvx512fNeural.cpp \
            $$PWD/SimdAvx512fResizer.cpp \
            $$PWD/SimdAvx512fSquaredDifferenceSum.cpp \
            $$PWD/SimdAvx512fSvm.cpp \
            $$PWD/SimdAvx512fSynet.cpp \
            $$PWD/SimdAvx512fSynetActivation.cpp \
            $$PWD/SimdAvx512fSynetConversion.cpp \
            $$PWD/SimdAvx512fSynetConvolution32f.cpp \
            $$PWD/SimdAvx512fSynetConvolution32fNhwcDirect.cpp \
            $$PWD/SimdAvx512fSynetDeconvolution32f.cpp \
            $$PWD/SimdAvx512fSynetFused.cpp \
            $$PWD/SimdAvx512fSynetMergedConvolution32f.cpp \
            $$PWD/SimdAvx512fSynetPooling.cpp \
            $$PWD/SimdAvx512fWinograd.cpp \
            $$PWD/SimdBaseAbsDifference.cpp \
            $$PWD/SimdBaseAbsDifferenceSum.cpp \
            $$PWD/SimdBaseAbsGradientSaturatedSum.cpp \
            $$PWD/SimdBaseAddFeatureDifference.cpp \
            $$PWD/SimdBaseAlphaBlending.cpp \
            $$PWD/SimdBaseBackground.cpp \
            $$PWD/SimdBaseBayerToBgr.cpp \
            $$PWD/SimdBaseBayerToBgra.cpp \
            $$PWD/SimdBaseBgraToBayer.cpp \
            $$PWD/SimdBaseBgraToBgr.cpp \
            $$PWD/SimdBaseBgraToGray.cpp \
            $$PWD/SimdBaseBgraToYuv.cpp \
            $$PWD/SimdBaseBgrToBayer.cpp \
            $$PWD/SimdBaseBgrToBgra.cpp \
            $$PWD/SimdBaseBgrToGray.cpp \
            $$PWD/SimdBaseBgrToHsl.cpp \
            $$PWD/SimdBaseBgrToHsv.cpp \
            $$PWD/SimdBaseBgrToRgb.cpp \
            $$PWD/SimdBaseBgrToYuv.cpp \
            $$PWD/SimdBaseBinarization.cpp \
            $$PWD/SimdBaseConditional.cpp \
            $$PWD/SimdBaseCopy.cpp \
            $$PWD/SimdBaseCpu.cpp \
            $$PWD/SimdBaseCrc32.cpp \
            $$PWD/SimdBaseDeinterleave.cpp \
            $$PWD/SimdBaseDetection.cpp \
            $$PWD/SimdBaseEdgeBackground.cpp \
            $$PWD/SimdBaseFill.cpp \
            $$PWD/SimdBaseFloat16.cpp \
            $$PWD/SimdBaseFloat32.cpp \
            $$PWD/SimdBaseGaussianBlur3x3.cpp \
            $$PWD/SimdBaseGemm32f.cpp \
            $$PWD/SimdBaseGrayToBgr.cpp \
            $$PWD/SimdBaseGrayToBgra.cpp \
            $$PWD/SimdBaseHistogram.cpp \
            $$PWD/SimdBaseHog.cpp \
            $$PWD/SimdBaseHogLite.cpp \
            $$PWD/SimdBaseInt16ToGray.cpp \
            $$PWD/SimdBaseIntegral.cpp \
            $$PWD/SimdBaseInterference.cpp \
            $$PWD/SimdBaseInterleave.cpp \
            $$PWD/SimdBaseLaplace.cpp \
            $$PWD/SimdBaseLbp.cpp \
            $$PWD/SimdBaseMeanFilter3x3.cpp \
            $$PWD/SimdBaseMedianFilter.cpp \
            $$PWD/SimdBaseNeural.cpp \
            $$PWD/SimdBaseOperation.cpp \
            $$PWD/SimdBasePerformance.cpp \
            $$PWD/SimdBaseReduce.cpp \
            $$PWD/SimdBaseReduceGray2x2.cpp \
            $$PWD/SimdBaseReduceGray3x3.cpp \
            $$PWD/SimdBaseReduceGray4x4.cpp \
            $$PWD/SimdBaseReduceGray5x5.cpp \
            $$PWD/SimdBaseReorder.cpp \
            $$PWD/SimdBaseResizeBilinear.cpp \
            $$PWD/SimdBaseResizer.cpp \
            $$PWD/SimdBaseSegmentation.cpp \
            $$PWD/SimdBaseShiftBilinear.cpp \
            $$PWD/SimdBaseSobel.cpp \
            $$PWD/SimdBaseSquaredDifferenceSum.cpp \
            $$PWD/SimdBaseStatistic.cpp \
            $$PWD/SimdBaseStatisticMoments.cpp \
            $$PWD/SimdBaseStretchGray2x2.cpp \
            $$PWD/SimdBaseSvm.cpp \
            $$PWD/SimdBaseSynet.cpp \
            $$PWD/SimdBaseSynetActivation.cpp \
            $$PWD/SimdBaseSynetConversion.cpp \
            $$PWD/SimdBaseSynetConvolution32f.cpp \
            $$PWD/SimdBaseSynetDeconvolution32f.cpp \
            $$PWD/SimdBaseSynetFused.cpp \
            $$PWD/SimdBaseSynetMergedConvolution32f.cpp \
            $$PWD/SimdBaseSynetPooling.cpp \
            $$PWD/SimdBaseTexture.cpp \
            $$PWD/SimdBaseThread.cpp \
            $$PWD/SimdBaseTransform.cpp \
            $$PWD/SimdBaseWinograd.cpp \
            $$PWD/SimdBaseYuvToBgr.cpp \
            $$PWD/SimdBaseYuvToBgra.cpp \
            $$PWD/SimdBaseYuvToHsl.cpp \
            $$PWD/SimdBaseYuvToHsv.cpp \
            $$PWD/SimdBaseYuvToHue.cpp \
            $$PWD/SimdLib.cpp \
            $$PWD/SimdMsaOperation.cpp \
            $$PWD/SimdNeonAbsDifferenceSum.cpp \
            $$PWD/SimdNeonAbsGradientSaturatedSum.cpp \
            $$PWD/SimdNeonAddFeatureDifference.cpp \
            $$PWD/SimdNeonAlphaBlending.cpp \
            $$PWD/SimdNeonBackground.cpp \
            $$PWD/SimdNeonBayerToBgr.cpp \
            $$PWD/SimdNeonBayerToBgra.cpp \
            $$PWD/SimdNeonBgraToBayer.cpp \
            $$PWD/SimdNeonBgraToBgr.cpp \
            $$PWD/SimdNeonBgraToGray.cpp \
            $$PWD/SimdNeonBgraToYuv.cpp \
            $$PWD/SimdNeonBgrToBayer.cpp \
            $$PWD/SimdNeonBgrToBgra.cpp \
            $$PWD/SimdNeonBgrToGray.cpp \
            $$PWD/SimdNeonBgrToRgb.cpp \
            $$PWD/SimdNeonBgrToYuv.cpp \
            $$PWD/SimdNeonBinarization.cpp \
            $$PWD/SimdNeonConditional.cpp \
            $$PWD/SimdNeonDeinterleave.cpp \
            $$PWD/SimdNeonDetection.cpp \
            $$PWD/SimdNeonEdgeBackground.cpp \
            $$PWD/SimdNeonFill.cpp \
            $$PWD/SimdNeonFloat16.cpp \
            $$PWD/SimdNeonFloat32.cpp \
            $$PWD/SimdNeonGaussianBlur3x3.cpp \
            $$PWD/SimdNeonGemm32f.cpp \
            $$PWD/SimdNeonGrayToBgr.cpp \
            $$PWD/SimdNeonGrayToBgra.cpp \
            $$PWD/SimdNeonHistogram.cpp \
            $$PWD/SimdNeonHog.cpp \
            $$PWD/SimdNeonHogLite.cpp \
            $$PWD/SimdNeonInt16ToGray.cpp \
            $$PWD/SimdNeonInterference.cpp \
            $$PWD/SimdNeonInterleave.cpp \
            $$PWD/SimdNeonLaplace.cpp \
            $$PWD/SimdNeonLbp.cpp \
            $$PWD/SimdNeonMeanFilter3x3.cpp \
            $$PWD/SimdNeonMedianFilter.cpp \
            $$PWD/SimdNeonNeural.cpp \
            $$PWD/SimdNeonOperation.cpp \
            $$PWD/SimdNeonReduce.cpp \
            $$PWD/SimdNeonReduceGray2x2.cpp \
            $$PWD/SimdNeonReduceGray3x3.cpp \
            $$PWD/SimdNeonReduceGray4x4.cpp \
            $$PWD/SimdNeonReduceGray5x5.cpp \
            $$PWD/SimdNeonReorder.cpp \
            $$PWD/SimdNeonResizeBilinear.cpp \
            $$PWD/SimdNeonResizer.cpp \
            $$PWD/SimdNeonSegmentation.cpp \
            $$PWD/SimdNeonShiftBilinear.cpp \
            $$PWD/SimdNeonSobel.cpp \
            $$PWD/SimdNeonSquaredDifferenceSum.cpp \
            $$PWD/SimdNeonStatistic.cpp \
            $$PWD/SimdNeonStatisticMoments.cpp \
            $$PWD/SimdNeonStretchGray2x2.cpp \
            $$PWD/SimdNeonSvm.cpp \
            $$PWD/SimdNeonSynet.cpp \
            $$PWD/SimdNeonSynetActivation.cpp \
            $$PWD/SimdNeonSynetConversion.cpp \
            $$PWD/SimdNeonSynetConvolution32f.cpp \
            $$PWD/SimdNeonSynetConvolution32fNhwcDirect.cpp \
            $$PWD/SimdNeonSynetDeconvolution32f.cpp \
            $$PWD/SimdNeonSynetFused.cpp \
            $$PWD/SimdNeonSynetMergedConvolution32f.cpp \
            $$PWD/SimdNeonSynetPooling.cpp \
            $$PWD/SimdNeonTexture.cpp \
            $$PWD/SimdNeonTransform.cpp \
            $$PWD/SimdNeonWinograd.cpp \
            $$PWD/SimdNeonYuvToBgr.cpp \
            $$PWD/SimdNeonYuvToBgra.cpp \
            $$PWD/SimdNeonYuvToHue.cpp \
            $$PWD/SimdSse1Fill.cpp \
            $$PWD/SimdSse1Float32.cpp \
            $$PWD/SimdSse1Gemm32f.cpp \
            $$PWD/SimdSse1Hog.cpp \
            $$PWD/SimdSse1Neural.cpp \
            $$PWD/SimdSse1Resizer.cpp \
            $$PWD/SimdSse1SquaredDifferenceSum.cpp \
            $$PWD/SimdSse1Svm.cpp \
            $$PWD/SimdSse1Synet.cpp \
            $$PWD/SimdSse1SynetActivation.cpp \
            $$PWD/SimdSse1SynetConversion.cpp \
            $$PWD/SimdSse1SynetFused.cpp \
            $$PWD/SimdSse1SynetPooling.cpp \
            $$PWD/SimdSse1Winograd.cpp \
            $$PWD/SimdSse2AbsDifferenceSum.cpp \
            $$PWD/SimdSse2AbsGradientSaturatedSum.cpp \
            $$PWD/SimdSse2AddFeatureDifference.cpp \
            $$PWD/SimdSse2AlphaBlending.cpp \
            $$PWD/SimdSse2Background.cpp \
            $$PWD/SimdSse2BayerToBgra.cpp \
            $$PWD/SimdSse2BgraToGray.cpp \
            $$PWD/SimdSse2BgraToYuv.cpp \
            $$PWD/SimdSse2BgrToBgra.cpp \
            $$PWD/SimdSse2BgrToGray.cpp \
            $$PWD/SimdSse2Binarization.cpp \
            $$PWD/SimdSse2Conditional.cpp \
            $$PWD/SimdSse2Deinterleave.cpp \
            $$PWD/SimdSse2EdgeBackground.cpp \
            $$PWD/SimdSse2Fill.cpp \
            $$PWD/SimdSse2Float32.cpp \
            $$PWD/SimdSse2GaussianBlur3x3.cpp \
            $$PWD/SimdSse2GrayToBgra.cpp \
            $$PWD/SimdSse2Histogram.cpp \
            $$PWD/SimdSse2Hog.cpp \
            $$PWD/SimdSse2Int16ToGray.cpp \
            $$PWD/SimdSse2Interference.cpp \
            $$PWD/SimdSse2Interleave.cpp \
            $$PWD/SimdSse2Laplace.cpp \
            $$PWD/SimdSse2Lbp.cpp \
            $$PWD/SimdSse2MeanFilter3x3.cpp \
            $$PWD/SimdSse2MedianFilter.cpp \
            $$PWD/SimdSse2Neural.cpp \
            $$PWD/SimdSse2Operation.cpp \
            $$PWD/SimdSse2Reduce.cpp \
            $$PWD/SimdSse2ReduceGray2x2.cpp \
            $$PWD/SimdSse2ReduceGray3x3.cpp \
            $$PWD/SimdSse2ReduceGray4x4.cpp \
            $$PWD/SimdSse2ReduceGray5x5.cpp \
            $$PWD/SimdSse2Reorder.cpp \
            $$PWD/SimdSse2ResizeBilinear.cpp \
            $$PWD/SimdSse2Resizer.cpp \
            $$PWD/SimdSse2Segmentation.cpp \
            $$PWD/SimdSse2ShiftBilinear.cpp \
            $$PWD/SimdSse2Sobel.cpp \
            $$PWD/SimdSse2SquaredDifferenceSum.cpp \
            $$PWD/SimdSse2Statistic.cpp \
            $$PWD/SimdSse2StatisticMoments.cpp \
            $$PWD/SimdSse2StretchGray2x2.cpp \
            $$PWD/SimdSse2Synet.cpp \
            $$PWD/SimdSse2SynetActivation.cpp \
            $$PWD/SimdSse2SynetConvolution32f.cpp \
            $$PWD/SimdSse2SynetConvolution32fNhwcDirect.cpp \
            $$PWD/SimdSse2SynetDeconvolution32f.cpp \
            $$PWD/SimdSse2SynetMergedConvolution32f.cpp \
            $$PWD/SimdSse2Texture.cpp \
            $$PWD/SimdSse2YuvToBgra.cpp \
            $$PWD/SimdSse2YuvToHue.cpp \
            $$PWD/SimdSse3Gemm32f.cpp \
            $$PWD/SimdSse3Neural.cpp \
            $$PWD/SimdSse3SynetConvolution32f.cpp \
            $$PWD/SimdSse41Detection.cpp \
            $$PWD/SimdSse41Hog.cpp \
            $$PWD/SimdSse41HogLite.cpp \
            $$PWD/SimdSse41Resizer.cpp \
            $$PWD/SimdSse41Segmentation.cpp \
            $$PWD/SimdSse41SynetConversion.cpp \
            $$PWD/SimdSse42Crc32.cpp \
            $$PWD/SimdSsse3AlphaBlending.cpp \
            $$PWD/SimdSsse3BayerToBgr.cpp \
            $$PWD/SimdSsse3BgraToBayer.cpp \
            $$PWD/SimdSsse3BgraToBgr.cpp \
            $$PWD/SimdSsse3BgraToYuv.cpp \
            $$PWD/SimdSsse3BgrToBayer.cpp \
            $$PWD/SimdSsse3BgrToBgra.cpp \
            $$PWD/SimdSsse3BgrToGray.cpp \
            $$PWD/SimdSsse3BgrToRgb.cpp \
            $$PWD/SimdSsse3BgrToYuv.cpp \
            $$PWD/SimdSsse3Deinterleave.cpp \
            $$PWD/SimdSsse3GaussianBlur3x3.cpp \
            $$PWD/SimdSsse3GrayToBgr.cpp \
            $$PWD/SimdSsse3Interleave.cpp \
            $$PWD/SimdSsse3Laplace.cpp \
            $$PWD/SimdSsse3MeanFilter3x3.cpp \
            $$PWD/SimdSsse3Reduce.cpp \
            $$PWD/SimdSsse3ReduceGray2x2.cpp \
            $$PWD/SimdSsse3ReduceGray4x4.cpp \
            $$PWD/SimdSsse3Reorder.cpp \
            $$PWD/SimdSsse3ResizeBilinear.cpp \
            $$PWD/SimdSsse3Resizer.cpp \
            $$PWD/SimdSsse3Sobel.cpp \
            $$PWD/SimdSsse3SquaredDifferenceSum.cpp \
            $$PWD/SimdSsse3Texture.cpp \
            $$PWD/SimdSsse3Transform.cpp \
            $$PWD/SimdSsse3YuvToBgr.cpp \
            $$PWD/SimdVmxAbsDifferenceSum.cpp \
            $$PWD/SimdVmxAbsGradientSaturatedSum.cpp \
            $$PWD/SimdVmxAddFeatureDifference.cpp \
            $$PWD/SimdVmxAlphaBlending.cpp \
            $$PWD/SimdVmxBackground.cpp \
            $$PWD/SimdVmxBgraToBayer.cpp \
            $$PWD/SimdVmxBgraToBgr.cpp \
            $$PWD/SimdVmxBgraToGray.cpp \
            $$PWD/SimdVmxBgraToYuv.cpp \
            $$PWD/SimdVmxBgrToBayer.cpp \
            $$PWD/SimdVmxBgrToBgra.cpp \
            $$PWD/SimdVmxBgrToGray.cpp \
            $$PWD/SimdVmxBgrToYuv.cpp \
            $$PWD/SimdVmxBinarization.cpp \
            $$PWD/SimdVmxConditional.cpp \
            $$PWD/SimdVmxDeinterleave.cpp \
            $$PWD/SimdVmxEdgeBackground.cpp \
            $$PWD/SimdVmxFill.cpp \
            $$PWD/SimdVmxGaussianBlur3x3.cpp \
            $$PWD/SimdVmxGrayToBgr.cpp \
            $$PWD/SimdVmxGrayToBgra.cpp \
            $$PWD/SimdVmxHistogram.cpp \
            $$PWD/SimdVmxInterference.cpp \
            $$PWD/SimdVmxInterleave.cpp \
            $$PWD/SimdVmxLaplace.cpp \
            $$PWD/SimdVmxLbp.cpp \
            $$PWD/SimdVmxMeanFilter3x3.cpp \
            $$PWD/SimdVmxMedianFilter.cpp \
            $$PWD/SimdVmxOperation.cpp \
            $$PWD/SimdVmxReduceGray2x2.cpp \
            $$PWD/SimdVmxReduceGray3x3.cpp \
            $$PWD/SimdVmxReduceGray4x4.cpp \
            $$PWD/SimdVmxReduceGray5x5.cpp \
            $$PWD/SimdVmxReorder.cpp \
            $$PWD/SimdVmxResizeBilinear.cpp \
            $$PWD/SimdVmxSegmentation.cpp \
            $$PWD/SimdVmxShiftBilinear.cpp \
            $$PWD/SimdVmxSobel.cpp \
            $$PWD/SimdVmxSquaredDifferenceSum.cpp \
            $$PWD/SimdVmxStatistic.cpp \
            $$PWD/SimdVmxStretchGray2x2.cpp \
            $$PWD/SimdVmxTexture.cpp \
            $$PWD/SimdVmxYuvToBgr.cpp \
            $$PWD/SimdVmxYuvToBgra.cpp \
            $$PWD/SimdVsxHog.cpp \
            $$PWD/SimdVsxNeural.cpp \
            $$PWD/SimdVsxSquaredDifferenceSum.cpp \
            $$PWD/SimdVsxSvm.cpp \
            $$PWD/SimdVsxYuvToHue.cpp \
