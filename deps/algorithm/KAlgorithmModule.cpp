﻿#pragma execution_character_set("utf-8")
#include "algorithm/KAlgorithmModule.h"
#include "common/KSTLTools.h"

#include <QObject>

class KAlgorithmModulePrivate
{
public:
    KAlgorithmModulePrivate() : _enable(true), _algorithm(0), _sensorParam(0)
    {
        _sensorParam = new KSmartParamInt(50, 1, 100);
        _sensorParam->setInfo({0, 1, "sensor", QObject::tr("灵敏度").toStdString(), QObject::tr("灵敏度").toStdString()});
    }

public:
    bool _enable;
    std::string _id;
    KAlgorithm *_algorithm;
    KSmartParamInt *_sensorParam;
    KParamDictionary _inParams;
    KParamDictionary _outParams;
};

KAlgorithmModule::KAlgorithmModule(KAlgorithm *algo) : p(0)
{
    CREATE_COMMON_PTR(p, KAlgorithmModulePrivate);
    p->_algorithm = algo;
    addInParam(p->_sensorParam);
}

KAlgorithmModule::~KAlgorithmModule()
{
    RELEASE_COMMON_PTR(p);
}

void KAlgorithmModule::setEnable(bool e)
{
    p->_enable = e;
}

bool KAlgorithmModule::enable()
{
    return p->_enable;
}

KAlgorithm *KAlgorithmModule::algorightm()
{
    return p->_algorithm;
}

int KAlgorithmModule::sensor() const
{
    return p->_sensorParam->ivalue();
}

void KAlgorithmModule::setSensor(int sensor)
{
    p->_sensorParam->setValue(sensor);
}

void KAlgorithmModule::setId(const std::string &id)
{
    if (id.empty())
        return;
    p->_id = id;
}

std::string KAlgorithmModule::id() const
{
    return p->_id;
}

int KAlgorithmModule::code() const
{
    return info().Code;
}

int KAlgorithmModule::process(KImageFrame1D &srcFrame, KImageFrame1D &dstFrame, int index)
{
    if (!p->_enable)
        return 0;
    return run(srcFrame, dstFrame, index);
}

KSmartParamInt *KAlgorithmModule::sensorParam() const
{
    return p->_sensorParam;
}

int KAlgorithmModule::inCount() const
{
    return p->_inParams.size();
}

KSmartParam *KAlgorithmModule::inParam(const std::string &name)
{
    return KSTLTools::find_in_spmap(p->_inParams, name);
}

KSmartParam1D KAlgorithmModule::inParams()
{
    KSmartParam1D params = KSTLTools::spmap_to_vec(p->_inParams);
    std::sort(params.begin(), params.end(), [=](KSmartParam *p1, KSmartParam *p2) {
        return p1->info().Index < p2->info().Index;
    });
    return params;
}

void KAlgorithmModule::addInParam(KSmartParam *param)
{
    KSTLTools::insert_in_spmap(p->_inParams, param->info().Id, param);
}

int KAlgorithmModule::outCount() const
{
    return p->_inParams.size();
}

KSmartParam *KAlgorithmModule::outParam(const std::string &name)
{
    return KSTLTools::find_in_spmap(p->_outParams, name);
}

KSmartParam1D KAlgorithmModule::outParams()
{
    return KSTLTools::spmap_to_vec(p->_outParams);
}

void KAlgorithmModule::addOutParam(KSmartParam *param)
{
    KSTLTools::insert_in_spmap(p->_outParams, param->info().Name, param);
}
