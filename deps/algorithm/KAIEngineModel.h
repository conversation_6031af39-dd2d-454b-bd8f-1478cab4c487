﻿#ifndef KAIENGINEMODEL_H
#define KAIENGINEMODEL_H

#include "algorithm/KAIEngine.h"
#include "common/KAIVipFrameworkGlobal.h"
#include <QObject>

class KAIEnginePanel;
class KAIEngineModelPrivate;
class KAIVIPCORE_EXPORT KAIEngineModel : public QObject
{
    Q_OBJECT
public:
    KAIEngineModel(KAIEngine *e);
    ~KAIEngineModel();

    int sensor();
    bool enable();

    void setEnable(bool en);
    void setSensor(int value);
    const KSmartParam &sensorParam();

    QString path() const;
    QString name() const;
    QString id() const;
    QString caption() const;

    KAIEngine *engine() const;
    KAIEnginePanel *requestPanel();

    KAIEngineInferEnity *createEnity();
    KAIEngineInferEnity *createEnity(const QString &id);

public slots:
    int load(const QString &path);
    void setName(const QString &name);
    void setCaption(const QString &text);
    void notifyCheckState(int cindex, bool en);
    void notifyCheckRequest();
    void uploadModel(int cindex);
    void applicateMode(int cindex);

signals:
    void enableChanged(bool en);
    void sensorChanged(int sensor);
    void pathChanged(const QString &path);
    void nameChanged(const QString &text);
    void captionChanged(const QString &text);
    void checkRequestNotified();
    void checkedStateNotified(int cindex, bool en);
    void modelUpload(int cindex);
    void modelApplicated(int cindex);

private:
    KAIEngineModelPrivate *p;
};

// AI模型文件管理器
class KAIEngineManagerModelPrivate;
class KAIVIPCORE_EXPORT KAIEngineModelManager : public QObject
{
    Q_OBJECT
    KDECLARE_SINGLECLASS(KAIEngineModelManager)
public:
    KAIEngineModelManager();
    ~KAIEngineModelManager();

    bool contains(const QString &modelName);
    bool containsPath(const QString &path);
    KAIEngineManager *aiManager();
    int count() const;

    KAIEngineModel *add(KAIEngine *e);
    void remove(const QString &id);

    std::vector<KAIEngineModel *> engines() const;

signals:
    void engineAdded(KAIEngineModel *m);
    void engineRemoved(KAIEngineModel *m);

    void engineRemovedName(const std::string &name);
    void engineRemovedPath(const std::string &modelpath);
    void engineEnableChanged(const QString &modelpath, bool en);
    void engineSensorChanged(const QString &modelpath, int sensor);

private:
    KAIEngineManagerModelPrivate *p;
};
#endif
