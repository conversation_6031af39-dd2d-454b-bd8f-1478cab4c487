﻿#include "algorithm/KImageFrame.h"
#include "common/KDefines.h"

#include <vector>
#include <string.h>

class KImageFramePrivate
{
public:
    unsigned char *cloneData(unsigned char *src, int size)
    {
        if (_buffer.size() < size)
        {
            _buffer.resize(size);
        }

        memcpy(_buffer.data(), src, size);
        return _buffer.data();
    }

public:
    std::vector<unsigned char> _buffer;
};

KImageFrame::KImageFrame() : p(0), BufferSize(0), DataBuffer(0), Width(0), Height(0),
                             FrameId(0), GroupId(0), FrameCount(0), compCount(0), buffIndex(-1)
{
    CREATE_COMMON_PTR(p, KImageFramePrivate);
}

KImageFrame::KImageFrame(int width, int height, unsigned char *buffer) : KImageFrame()
{
    Width = width;
    Height = height;
    BufferSize = Width * Height * 3;

    if (!buffer)
    {
        p->_buffer.resize(Width * Height * 3);
        DataBuffer = p->_buffer.data();
    }
    else
    {
        DataBuffer = buffer;
    }
}

KImageFrame::KImageFrame(const KImageFrame &frame) : KImageFrame()
{
    *this = frame;
}

KImageFrame::~KImageFrame()
{
    RELEASE_COMMON_PTR(p);
}

unsigned char *KImageFrame::data()
{
    return DataBuffer;
}
#include <iostream>
void KImageFrame::reSize(int width, int height)
{
    int oldSize = BufferSize;
    Width = width;
    Height = height;
    BufferSize = width * height * 3;
    if (oldSize < BufferSize)
    {
        p->_buffer.resize(BufferSize);
        DataBuffer = p->_buffer.data();
    }
    memset(p->_buffer.data(), 0, p->_buffer.size());
    BufferSize = p->_buffer.size();
    // std::cout << " ============================= KImageFrame reSize = " << BufferSize << std::endl;
}

KImageFrame &KImageFrame::clone(const KImageFrame &frame)
{
    *this = frame;
    DataBuffer = p->cloneData(frame.DataBuffer, frame.BufferSize);
    return *this;
}

void KImageFrame::copy(unsigned char *buffer, int size)
{
    DataBuffer = p->cloneData(buffer, size);
}

void KImageFrame::setImageId(uint8_t id)
{
    FrameId = ((id << 8) | frameID());
}

void KImageFrame::setFrameId(uint8_t id)
{
    FrameId = (imageID() | id);
}

uint8_t KImageFrame::frameID()
{
    return FrameId & 0xFF; // 取低8位作为curFrameId
}

uint8_t KImageFrame::imageID()
{
    return (FrameId >> 8) & 0xFF; // 取高8位作为imageId
}

KImageFrame &KImageFrame::operator=(const KImageFrame &frame)
{
    DataBuffer = frame.DataBuffer;
    Width = frame.Width;
    Height = frame.Height;
    BufferSize = frame.BufferSize;
    FrameId = frame.FrameId;
    GroupId = frame.GroupId;
    FrameCount = frame.FrameCount;
    compCount = frame.compCount;
    imageFormat = frame.imageFormat;
    stepNo = frame.stepNo;
    Code = frame.Code;
    cameraId = frame.cameraId;
    buffIndex = frame.buffIndex;
    return *this;
}

#pragma region KImageFrame1D Defination
class KImageFrame1DPrivate
{
public:
    std::vector<KImageFrame> _frames;
    int frameCount = 0;
    int compCount = 0;

    int MaxW = 1752;
    int MaxH = 1752;

    u_char *_header = nullptr;
    void *_handlePtr = nullptr;
    void *_devBuffPtr = nullptr;
};

KImageFrame1D::KImageFrame1D(int frameCount /*= 1*/) : p(0)
{
    CREATE_COMMON_PTR(p, KImageFrame1DPrivate);
}

KImageFrame1D::~KImageFrame1D()
{
    RELEASE_COMMON_PTR(p);
}

int KImageFrame1D::frameCount() const
{
    return p->frameCount;
}

int KImageFrame1D::compCount() const
{
    return p->compCount;
}

void KImageFrame1D::resize(int frameCount, int compCount)
{
    int totalCount = frameCount + compCount;
    int curCount = p->frameCount + p->compCount;
    //    int fCount = frameCount();
    if (curCount < totalCount)
    {
        addFrame(totalCount - curCount);
    }
    else
    {
        p->_frames.erase(p->_frames.end() - (totalCount - curCount), p->_frames.end());
    }
    p->frameCount = frameCount;
    p->compCount = compCount;
}

void KImageFrame1D::addFrame(int count)
{
    if (count <= 0)
        return;

    p->_frames.insert(p->_frames.end(), count, KImageFrame());
}

void KImageFrame1D::addFrame(int count, int width, int height)
{
    if (count <= 0)
        return;

    int start = frameCount();
    addFrame(count);

    for (int i = 0; i < count; ++i)
    {
        p->_frames[i + start].reSize(width, height);
    }
}

u_char *KImageFrame1D::header(int frameIndex) const
{
    if (frameIndex < 0 || frameIndex >= p->_frames.size())
        return 0;
    return p->_header + frameIndex * (p->MaxW * p->MaxH * 3);
}

void KImageFrame1D::setBufferHeaders(u_char *header, void *handlePtr, void *devBuffPtr)
{
    if (header && header != p->_header)
        p->_header = header;
    if (handlePtr && handlePtr != p->_handlePtr)
        p->_handlePtr = handlePtr;
    if (devBuffPtr && devBuffPtr != p->_devBuffPtr)
        p->_devBuffPtr = devBuffPtr;
}

void *KImageFrame1D::bmDevBuffPtr()
{
    return p->_devBuffPtr;
}

void *KImageFrame1D::buffHandle()
{
    return p->_handlePtr;
}

KImageFrame *KImageFrame1D::frame(int index)
{
    if (index < 0 || index >= p->_frames.size())
        return 0;

    return p->_frames.data() + index;
}

const KImageFrame *KImageFrame1D::frame(int index) const
{
    if (index < 0 || index >= p->_frames.size())
        return 0;

    return p->_frames.data() + index;
}

KImageFrame *KImageFrame1D::operator[](int index)
{
    return frame(index);
}

const KImageFrame *KImageFrame1D::operator[](int index) const
{
    return frame(index);
}

KImageFrame1D &KImageFrame1D::clone(const KImageFrame1D &frames)
{
    //    if (p->_frames.size() < frames.frameCount())
    //        p->_frames.resize(frames.frameCount());
    //    for (int i = 0; i < p->_frames.size(); ++i)
    resize(frames.frameCount(), frames.compCount());

    for (int i = 0; i < p->_frames.size(); ++i)
        p->_frames[i].clone(*frames[i]);

    return *this;
}

KImageFrame1D &KImageFrame1D::operator=(const KImageFrame1D &frames)
{
    //    p->_frames.resize(frames.frameCount());
    resize(frames.frameCount(), frames.compCount());

    for (int i = 0; i < p->_frames.size(); ++i)
        p->_frames[i] = *frames[i];

    return *this;
}

void KImageFrame1D::resizeImageMaxSize(int maxW, int maxH)
{
    p->MaxW = maxW;
    p->MaxH = maxH;
}
#pragma endregion
