﻿#ifndef KSMARTPARAMGROUP_H
#define KSMARTPARAMGROUP_H

#include "KAIVipAlgorithmGlobal.h"
#include "KAlgorithmTypes.h"

class KSmartParam;
class KSmartParamGroupPrivate;
class KAIVIPALGORITHM_EXPORT KSmartParamGroup
{
public:
	KSmartParamGroup();
	KSmartParamGroup(int id);
	KSmartParamGroup(int id, const KSmartParamGroupInfo& gInfo);
	~KSmartParamGroup();

public:
	int id();
	void setId(int id);
	const KSmartParamGroupInfo& info() const;
	std::string name() const;

	KSmartParamGroup& addParam(KSmartParam* param);
	void removeParam(KSmartParam* param);
	void removeParam(const std::string& pname);
	KSmartParam* param(const std::string& pname) const;
	std::vector<KSmartParam*> params();

	int count() const;

	void setEnable(bool e);
	void setValue(void *val);

private:
	KSmartParamGroupPrivate* p;
};
#endif