﻿#ifndef KAIENGINE_H
#define KAIENGINE_H

#include "KAIVipAlgorithmGlobal.h"
#include "KAlgorithmTypes.h"
#include "KImage2.h"

typedef enum tagKAIEngineErrorCode
{
    K_Engine_OK = 0,
    K_Engine_PathInValid,
    K_Engine_Null,
    K_Engine_NotExit,
    K_Engine_InferException
} KAIEngineErrorCode;

class KAIEngine;
class KAIEngineInferEnity;

typedef std::vector<KAIEngineInferEnity *> KAIEnity1D;

typedef std::vector<KAIEngine *> KAIEngine1D;

//AI推理数据 不同硬件需要的数据不同，由此继承
class KAIVIPALGORITHM_EXPORT KAIEngineInferData
{
public:
    KAIEngineInferData();
    virtual ~KAIEngineInferData();
};

//每种AI硬件的推理执行，由此类实现具体的推理过程
class KAIEngineInferEnityPrivate;
class KAIVIPALGORITHM_EXPORT KAIEngineInferEnity
{
public:
    KAIEngineInferEnity(KAIEngine *e = 0);
    KAIEngineInferEnity(const std::string &id, KAIEngine *e = 0);

    virtual ~KAIEngineInferEnity();

public:
    virtual bool isValid() = 0;
    KAIEngine *engine() const;
    const std::string &id() const;

    void setId(const std::string &id);
    virtual int infer(KImage2 *srcImg) = 0;
    virtual KAIEngineInferData *data() = 0;

protected:
    KAIEngineInferEnityPrivate *p;
};

//每种硬件的引擎，一个模型文件对应一个引擎
//每个引擎延伸多个实体
class KAIEnginePrivate;
class KAIVIPALGORITHM_EXPORT KAIEngine
{
public:
    KAIEngine(const std::string &id);
    virtual ~KAIEngine();

public:
    virtual bool isValid() = 0;
    virtual bool isEnable() = 0;
    virtual const KAIEngineInfo &info() const = 0;
    std::string id() const;
    std::string name() const;
    std::string instruction() const;

    void setName(const std::string &text);
    void setInstruction(const std::string &text);

    //调用设置路径会 启动load。
    int setPath(const std::string &p);
    std::string path() const; //文件路径

    int enityCount() const;

    KAIEngineInferEnity *enity(const std::string &id) const;
    KAIEnity1D enitys() const;

    KAIEngineInferEnity *operator[](const std::string &id);
    const KAIEngineInferEnity *operator[](const std::string &id) const;

    KAIEngineInferEnity *addEnity(const std::string &id);
    int removeEnity(const std::string &id);

    int createEnityRetCode() const; // 给出创建结果的错误码

protected:
    virtual KAIEngineInferEnity *createEnity(const std::string &id) = 0;
    virtual int load(const std::string &path) = 0;
    virtual int unload() = 0;

    void setCreateEnityRetCode(int code);

protected:
    KAIEnginePrivate *p;
};

//AIEngine 的管理类
class KAIEngineManagerPrivate;
class KAIVIPALGORITHM_EXPORT KAIEngineManager
{
public:
    static KAIEngineManager *singleInstance();

public:
    KAIEngineManager();
    ~KAIEngineManager();

    bool add(KAIEngine *e);
    bool remove(const std::string &id);
    bool remove(KAIEngine *engine);

    bool find(const std::string &id) const;
    KAIEngine *engine(const std::string &id) const;

    int count() const;
    KAIEngine *operator[](const std::string &id);
    const KAIEngine *operator[](const std::string &id) const;

    KAIEngine1D engines() const;

public:
    KAIEngineManagerPrivate *p;
};
#endif // KAIENGINE_H
