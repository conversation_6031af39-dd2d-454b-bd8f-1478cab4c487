﻿#ifndef KALGORITHMRESULTMANAGER_H
#define KALGORITHMRESULTMANAGER_H

#include "common/KDefines.h"
#include "KAIVipAlgorithmGlobal.h"

typedef enum tagKAlgorithmResultCode
{
    KCV_NG = -1,
    KCV_OK = 0x0,
    KCV_NG_Test = 0x1,
    KCV_NG_Placeholder,
    KCV_NG_NoMemory,
    KCV_NG_DataError,
    <PERSON><PERSON>_<PERSON><PERSON>_NullPointer,
    <PERSON><PERSON>_NG_FileNoExit,
    KCV_NG_StdException,
    KCV_NG_SysErrorEnd,

    KCV_NG_FrameCountLess,
    KCV_NG_FrameCountMore,
    KCV_N<PERSON>_FrameWidth,
    KC<PERSON>_NG_FrameHeight,

    KCV_FileNotExit,
    KCV_RuntimeError,
    <PERSON><PERSON>_ContextError,
    <PERSON><PERSON>_MemeoryError,
    <PERSON><PERSON>_<PERSON>m<PERSON>rror,
    <PERSON>V_Exception,

    //AI -- Error
    KCV_EngineNotExit,
    KCV_EngineError,
    KCV_AIResultDimNotMatched,
    <PERSON><PERSON>_EngineDisabled,
    KCV_EngineInferException,

    //用户自定义区域
    KCV_NG_User = 0x8000
} KAlgorithmResultCode;

typedef struct tagKAlgorithmResultDescription
{
    unsigned int Code;   //j结果代码   0 ~ 0xFFFFFFFF
    std::string Summary; //
    std::string Instruction;
} KAlgorithmResultDescription;

class KAlgorithmResultManagerPrivate;
class KAIVIPALGORITHM_EXPORT KAlgorithmResultManager
{
    KDECLARE_SINGLECLASS(KAlgorithmResultManager)
public:
    KAlgorithmResultManager();
    ~KAlgorithmResultManager();

    int count() const;
    //某个code的说明
    const std::string &summary(unsigned int code);
    const std::string &instruction(unsigned int code);
    void registerResult(const KAlgorithmResultDescription &desp);
    std::vector<KAlgorithmResultDescription *> results();
    KAlgorithmResultDescription *result(unsigned int code);
    KAlgorithmResultDescription *operator[](unsigned int code);

    void addStatis(unsigned int code); //统计错误， 线程安全
    void updateNgStatisCount(int groupid, unsigned int count);

    unsigned int statisCount(unsigned int code); //
    unsigned int statisSumNgCount(int groupid);
    void resetStatis();
    void resetStatis(unsigned int code);

private:
    KAlgorithmResultManagerPrivate *p;
};
#endif
