﻿#ifndef KIMAGEFRAME_H
#define KIMAGEFRAME_H

#include "KAIVipAlgorithmGlobal.h"
#include <vector>

enum ImageFormat
{
    K_BayerBGGR,   // 0
    K_BayerGBRG,   // 1
    K_BayerGRBG,   // 2
    K_BayerRGGB,   // 3
    K_Reserved,    // 4 预留，暂时没用 2024.9.3
    K_MonoRGBGray, // 5 //单通道灰度图

    K_BGRAlready = 1000, // 已经是转换后的格式，不需要再转换
};

class KImageFramePrivate;
class KAIVIPALGORITHM_EXPORT KImageFrame
{
public:
    KImageFrame();
    KImageFrame(int width, int height, unsigned char *buffer = 0);
    KImageFrame(const KImageFrame &frame);

    ~KImageFrame();

    unsigned char *data();
    void reSize(int width, int height);
    //************************************
    // Method:    深拷贝
    // Returns:   KImageFrame&
    // Qualifier:
    // Parameter: const KImageFrame & frame
    //************************************
    KImageFrame &clone(const KImageFrame &frame);
    //************************************
    // Method:    浅拷贝
    // Returns:   KImageFrame&
    // Qualifier:
    // Parameter: const KImageFrame & frame
    //************************************
    KImageFrame &operator=(const KImageFrame &frame);

    void copy(unsigned char *buffer, int size);

    //方便读写framID和imageID@zyz
    void setImageId(uint8_t id);
    void setFrameId(uint8_t id);
    uint8_t frameID();
    uint8_t imageID();

public:
    unsigned char *DataBuffer;
    unsigned char *DstBuffer;
    int Width;
    int Height;
    int BufferSize;
    int FrameId;
    int GroupId;
    int FrameCount;
    int compCount;
    int compIndex = -1;
    // new function
    int cameraId = 0;
    int buffIndex = 0;
    int Code = 0;

    int stepNo = 0;
    ImageFormat imageFormat;

private:
    KImageFramePrivate *p;
};

class KImageFrame1DPrivate;
class KAIVIPALGORITHM_EXPORT KImageFrame1D
{
public:
    KImageFrame1D(int frameCount = 1);
    ~KImageFrame1D();

    int frameCount() const;
    int compCount() const;
    void resize(int frameCount, int compCount = 0);
    //************************************
    // Method:    增加内存长度为frameBufferSize的帧count个
    // Returns:   void
    // Qualifier:
    // Parameter: int count
    // Parameter: int frameBufferSize
    //************************************
    void addFrame(int count);
    //************************************
    // Method:    重载版本
    // Returns:   void
    // Qualifier:
    // Parameter: int count
    // Parameter: int width
    // Parameter: int height
    //************************************
    void addFrame(int count, int width, int height);

    // new function
    void setBufferHeaders(u_char *header, void *handlePtr, void *devBuffPtr);
    u_char *header(int frameIndex = 0) const;
    void *bmDevBuffPtr();
    void *buffHandle();

    KImageFrame *frame(int index);
    const KImageFrame *frame(int index) const;

    KImageFrame *operator[](int index);
    const KImageFrame *operator[](int index) const;
    //************************************
    // Method:    深拷贝
    // Returns:   KImageFrame1D&
    // Qualifier:
    // Parameter: const KImageFrame1D & frames
    //************************************
    KImageFrame1D &clone(const KImageFrame1D &frames);
    //************************************
    // Method:    浅拷贝
    // Returns:   KImageFrame1D&
    // Qualifier:
    // Parameter: const KImageFrame1D & frames
    //************************************
    KImageFrame1D &operator=(const KImageFrame1D &frames);

    void resizeImageMaxSize(int maxW, int maxH);

private:
    KImageFrame1DPrivate *p;
};

typedef std::vector<KImageFrame1D> KImageFrame2D;
#endif
