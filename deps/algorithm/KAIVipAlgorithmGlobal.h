﻿#ifndef  KAIVIPALGORITHMGLOBAL_H
#define  KAIVIPALGORITHMGLOBAL_H


#ifdef _WIN32
#  ifdef KAIVIPALGORITHM_LIB
#    define KAIVIPALGORITHM_EXPORT __declspec (dllexport)
#  else
#    define KAIVIPALGORITHM_EXPORT __declspec (dllimport)
#  endif
#else
#   define KAIVIPALGORITHM_EXPORT __attribute__((visibility("default")))
#endif

#define KIPMODULE_EXPORT	KAIVIPALGORITHM_EXPORT
#define KTOOLS_EXPORT		KAIVIPALGORITHM_EXPORT

//#include <opencv2/imgproc/imgproc.hpp>
//#include <opencv2/core/core.hpp>
//#include <opencv2/highgui/highgui.hpp>
//#include <opencv2/imgproc/types_c.h>

#include <string>
#include <vector>
#include <map>
#include <memory>

#endif