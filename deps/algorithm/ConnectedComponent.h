﻿#ifndef _CONNECTCOMPONENT_VAR
#define _CONNECTCOMPONENT_VAR

#include "opencv2/opencv.hpp"
#include <opencv2/highgui/highgui_c.h>
#include <opencv2/core/core.hpp>
#include <omp.h>

#define SIMD_STATIC
#define SIMD_OPENCV_ENABLE
#include "Simd/SimdLib.hpp"
#include <vector>

#define MaxRuns 100000 // regions max rows ?
#define MaxRegs 10000  // find regs max count limit
#define Pi 3.1415926f
#define GrayNum 256
#define MinArea 1

#define imRefBP(im, T, x, y) (im.at<T>(y, x))

using namespace std;
using namespace cv;
//////////////////////////////////////////////////////////////////////////
struct run
{
    short x, y, width; // location and width of run
    short color;       // which color(s) this run represents
    int parent, next;  // parent run and next run in run list
    short xend;
};
struct region
{
    int color;          // id of the color
    int x1, y1, x2, y2; // bounding box (x1,y1) - (x2,y2) 这里要和blepo兼容的话需要x2+1,y2+1
    int area;           // occupied area in pixels
    int run_start;      // first run index for this region
    int iterator_id;    // id to prevent duplicate hits by an iterator

    float m10, m01;          // first-order moments in x and y
    long long m11, m20, m02; // second-order moments in x and y
    float mu11, mu20, mu02;  // central moments
    float cen_x, cen_y;      // centroid

    float lambda1, lambda2; // major and minor axes scaled by the standard deviation = sqrt(eigenvalue)
    float l1, l2;           // 用像素表示的椭圆长短半径(包含开方运算，所以旧版本没有包括，而用lamda1、lamda2表示)
    float eccentricity;     // ratio of major axis length to minor axis length
    float orientation;      // 方向
    float linearity;        // 线性率
    float circularity;      // 圆形率
    float compactness;      // 紧密度
};
inline int range_sum(int x, int w) // x...(x+w-1)
{
    return (w * (2 * x + w - 1) / 2);
}
inline long long range_sum2(int x, int w) // x^2...(x+w-1)^2=> n(n+1)(2n+1)/6
{
    return ((long long)w * (2 * w * w + 6 * w * x - 3 * w + 6 * x * x - 6 * x + 1) / 6);
}
inline int range_sum3(int x, int w) // x^2...(x+w-1)^2=> n(n+1)(2n+1)/6
{
    return (w * (2 * w * w + 6 * w * x - 3 * w + 6 * x * x - 6 * x + 1) / 6);
}
template <class T> // win:两侧跳过像素数，不能等于0
int EncodeRuns(run *rle, T *map_src, int width, int height, int max_runs, int win)
{
    T m, *pMap, *map = map_src;
    int x, y, j, l;
    run r;

    r.next = 0;
    j = 0;
    for (y = 0; y < height; y++, map += width)
    {
        r.y = y;
        x = 0;
        pMap = &map[x];
        map[width - win] = 10;

        while (x < width - win)
        {
            m = *pMap;
            l = x;
            while (*pMap == m)
            {
                pMap++;
                x++;
            }
            if (m != 0 || x >= width - win)
            {
                r.x = l;
                r.color = m;
                r.width = x - l;
                r.xend = x - 1;
                r.parent = j;
                rle[j++] = r;
                if (j >= max_runs)
                    return (j);
            }
        }
    }
    return (j);
}
template <class rle_t>
void ConnectComponents(rle_t *map, int num)
{
    int l1 = 1, l2 = 0, i, j, s, xsign = 0;
    run r1, r2;
    while (map[l1].y == 0)
        l1++; // skip first line

    r1 = map[l1];
    r2 = map[l2];
    s = l1;
    while (l1 < num)
    {
        if (r1.color == r2.color && r1.color)
        {
            if ((r2.x <= r1.x && r1.x < r2.x + r2.width) || (r1.x <= r2.x && r2.x < r1.x + r1.width))
            {
                if (s != l1)
                { // if we didn't have a parent already, just take this one
                    map[l1].parent = r1.parent = r2.parent;
                    s = l1;
                }
                else if (r1.parent != r2.parent)
                {
                    i = r1.parent;
                    while (i != map[i].parent)
                        i = map[i].parent;
                    j = r2.parent;
                    while (j != map[j].parent)
                        j = map[j].parent;
                    if (i < j)
                    {
                        map[j].parent = i;
                        map[l1].parent = map[l2].parent = r1.parent = r2.parent = i;
                    }
                    else
                    {
                        map[i].parent = j;
                        map[l1].parent = map[l2].parent = r1.parent = r2.parent = j;
                    }
                }
            }
        }
        // Move to next point where values may change
        i = (r2.x + r2.width) - (r1.x + r1.width);
        if (i >= 0)
            r1 = map[++l1];
        if (i <= 0)
            r2 = map[++l2];
    }
    for (i = 0; i < num; i++)
    {
        j = map[i].parent;
        map[i].parent = map[j].parent;
    }
}
template <class region_t, class rle_t>
int ExtractRegions(region_t *reg, int max_reg, rle_t *rmap, int num)
{
    int b, i, n, area, ri, m10, m01;
    run r;
    n = 0;
    float a, b1, c, alpha, delta;

    for (i = 0; i < num; i++)
    {
        if (rmap[i].color)
        {
            r = rmap[i];
            if (r.parent == i)
            {
                // Add new region if this run is a root (i.e. self parented)
                rmap[i].parent = b = n; // renumber to point to region id
                reg[b].color = r.color;
                reg[b].area = r.width;
                reg[b].x1 = r.x;
                reg[b].y1 = r.y;
                reg[b].x2 = r.x + r.width;
                reg[b].y2 = r.y;

                reg[b].m10 = range_sum(r.x, r.width);
                reg[b].m01 = (r.y * r.width);
                reg[b].m11 = reg[b].m10 * r.y;
                reg[b].m20 = range_sum2(r.x, r.width);
                reg[b].m02 = reg[b].m01 * r.y;

                reg[b].run_start = i;
                reg[b].iterator_id = i; // temporarily use to store last run
                n++;
                if (n >= max_reg)
                    return (max_reg);
            }
            else
            {
                // Otherwise update region stats incrementally
                b = rmap[r.parent].parent; //	rmap[i].parent = b; // update parent to identify region id
                reg[b].area += r.width;
                reg[b].x2 = max(r.x + r.width, reg[b].x2);
                reg[b].x1 = min((int)r.x, reg[b].x1);
                reg[b].y2 = r.y; // last set by lowest run

                m10 = range_sum(r.x, r.width);
                m01 = r.y * r.width;
                reg[b].m10 += m10;
                reg[b].m01 += m01;
                reg[b].m11 += m10 * r.y;
                reg[b].m20 += range_sum2(r.x, r.width);
                reg[b].m02 += m01 * r.y;

                // set previous run to point to this one as next
                rmap[reg[b].iterator_id].next = i;
                reg[b].iterator_id = i;
            }
        }
    }

    // calculate centroids from stored sums
    for (i = 0; i < n; i++)
    {
        area = reg[i].area;
        ri = reg[i].iterator_id;
        rmap[ri].next = -1;
        reg[i].iterator_id = 0;
        reg[i].x2--; // 收缩
        reg[i].cen_x = (float)reg[i].m10 / area;
        reg[i].cen_y = (float)reg[i].m01 / area;
        if (area >= MinArea)
        {
            reg[i].mu11 = reg[i].m11 - reg[i].cen_y * reg[i].m10;
            reg[i].mu20 = reg[i].m20 - reg[i].cen_x * reg[i].m10;
            reg[i].mu02 = reg[i].m02 - reg[i].cen_y * reg[i].m01;

            a = reg[i].mu20 + reg[i].mu02;
            b1 = reg[i].mu20 - reg[i].mu02;
            c = sqrt(4 * reg[i].mu11 * reg[i].mu11 + b1 * b1);
            reg[i].lambda1 = (a + c) / (2 * area);
            reg[i].lambda2 = (a - c) / (2 * area);
            reg[i].eccentricity = sqrt((reg[i].lambda1 - reg[i].lambda2) / reg[i].lambda1);
            reg[i].circularity = ((float)area * area) / (2 * Pi * a);
            reg[i].linearity = c / a;
            reg[i].compactness = area / ((float)(reg[i].x2 - reg[i].x1 + 1) * (reg[i].y2 - reg[i].y1 + 1));
            a = reg[i].mu11 / b1;
            alpha = atan(2 * a) * 180 / (2 * Pi);
            delta = b1 + reg[i].mu11 * a;
            if (delta < 0)
                alpha += 90;
            reg[i].orientation = fmod(180 - alpha, 180);
            reg[i].l1 = 2 * sqrt(reg[i].lambda1);
            reg[i].l2 = 2 * sqrt(reg[i].lambda2);
        }
        else
        {
            reg[i].area = 0; // if not conform to area limit ,set area to 0
            reg[i].lambda1 = 0;
            reg[i].lambda2 = 0;
            reg[i].eccentricity = 0;
            reg[i].circularity = 0;
            reg[i].linearity = 0;
            reg[i].compactness = 0;
        }
    }
    return (n);
}
template <class T>
int SegmentImage(T *map, run *rlt, region *reg, int &reg_num, int w, int h, int ww)
{
    int rlt_num = EncodeRuns(rlt, map, w, h, MaxRuns, ww);
    ConnectComponents(rlt, rlt_num);
    reg_num = ExtractRegions(reg, MaxRegs, rlt, rlt_num);

    return rlt_num;
}
#endif
