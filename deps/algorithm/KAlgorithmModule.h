﻿#ifndef KALGORITHMFUNCTIONMODULE_H
#define KALGORITHMFUNCTIONMODULE_H

#include "common/KDefines.h"
#include "KAIVipAlgorithmGlobal.h"
#include "KSmartParam.h"
#include "KImageFrame.h"
//#include "KImage2.h"

class KAlgorithm;
class KAlgorithmModule;

typedef std::vector<KAlgorithmModule*> KAlgorithmModule1D;

//算法中的独立功能模块
typedef struct tagKAlgorithmModuleInfo
{
public:
	unsigned int		Code;  //Code 算法编码   A0032-32323;之类TODO
	std::string			Id;   //Id，编程者手动创建的ID 不可更改, 可以是固定格式的编码 也可以是GUID
	std::string			Name; //简短的功能抽炼 比如黑点，缺口等名称
	std::string			Instruction; //详细的功能说明，可省略
	std::string			Author;  //作者
	std::string			DateTime; //创建时间
}KAlgorithmModuleInfo;

class KAlgorithmModulePrivate;
class KAIVIPALGORITHM_EXPORT KAlgorithmModule
{
public:
	KAlgorithmModule(KAlgorithm* algo);
	~KAlgorithmModule();

public:
	void setEnable(bool e);
	bool enable();
	KAlgorithm* algorightm();

	int sensor() const;
    void setSensor(int sensor);
	void setId(const std::string& id);
	std::string id() const;
	int code() const;
	virtual const KAlgorithmModuleInfo& info() const = 0;

	//核心处理程序
	//virtual int process(KImage2s& src, KImage2s& dst) = 0;
	int process(KImageFrame1D& srcFrame, KImageFrame1D& dstFrame, int index);

	//添加及获取参数
	KSmartParamInt*  sensorParam() const;

	int inCount() const;
	KSmartParam *inParam(const std::string& name);
	KSmartParam1D inParams();
	void addInParam(KSmartParam* param);

	int outCount() const;
	KSmartParam *outParam(const std::string& name);
	KSmartParam1D outParams();
	void addOutParam(KSmartParam* param);

protected:
	virtual int run(KImageFrame1D& srcFrame, KImageFrame1D& dstFrame, int index) = 0;

private:
	KAlgorithmModulePrivate *p;
};



#endif
