﻿#include "algorithm/KImageMarker.h"
#include "common/KDefines.h"
#include "cvText/CvxText.h"

#define KIM_CHECK_IMG() \
    if (!p->_image)     \
        return *this;

struct TextStruct
{
    //    const char *text;
    // public:
    //    TextStruct()
    //    {
    //    }
    //    TextStruct(const char *t, KPoint p, KColor c, int s)
    //        : /*text(t),*/ point(p), color(c), size(s)
    //    {
    //        strcpy(&text[0], t);
    //    }

    //    char text[100];
    std::string text;
    KPoint point;
    KColor color;
    int size;
};

struct RectStruct
{
    KRect rect;
    KColor color;
    int lineWidth;
};

struct CircleStruct
{
    KCircle circle;
    KColor color;
    int lineWidth;
};

struct LineStruct
{
    cv::Point p1;
    cv::Point p2;
    KColor color;
    int lineWidth;
};

const int MaxDrawCount = 100;
const int MaxFrameCount = 32;

class KImageMarkerPrivate
{
public:
    KImageMarkerPrivate() : _width(3), _color(KCV_GREEN), _image(0)
    {
        _shift.x = _shift.y = 0;
        _cvText = new CvxText("/usr/share/fonts/opentype/noto/NotoSerifCJK-Regular.ttc");
        cv::Scalar fontSize(45, 0.5, 0.1, 0);
        _cvText->setFont(&fontSize);

        _textVec.resize(MaxFrameCount);
        _rectVec.resize(MaxFrameCount);
        _circleVec.resize(MaxFrameCount);
        _lineVec.resize(MaxFrameCount);
        for (int i = 0; i < MaxFrameCount; ++i)
        {
            _textVec[i].reserve(MaxDrawCount);
            _rectVec[i].reserve(MaxDrawCount);
            _circleVec[i].reserve(MaxDrawCount);
            _lineVec[i].reserve(MaxDrawCount);
        }
    }

public:
    bool _drawEnable = true;
    int _width;
    int _curFrameIndex = 0;
    cv::Scalar _color;
    cv::Point _shift;
    cv::Mat *_image;
    CvxText *_cvText;

    std::vector<std::vector<TextStruct>> _textVec;
    std::vector<std::vector<RectStruct>> _rectVec;
    std::vector<std::vector<CircleStruct>> _circleVec;
    std::vector<std::vector<LineStruct>> _lineVec;
};

KImageMarker::KImageMarker() : p(0)
{
    CREATE_COMMON_PTR(p, KImageMarkerPrivate);
}

KImageMarker::~KImageMarker()
{
    RELEASE_COMMON_PTR(p);
}

void KImageMarker::setImage(cv::Mat *src)
{
    if (!src)
        return;
    p->_image = src;
}

cv::Mat *KImageMarker::image()
{
    return p->_image;
}

const cv::Mat *KImageMarker::image() const
{
    return p->_image;
}

void KImageMarker::setWidth(int w)
{
    p->_width = w;
}

KColor KImageMarker::width() const
{
    return p->_width;
}

void KImageMarker::setColor(KColor c)
{
    p->_color = cv::Scalar((c & 0xFF0000) >> 16, (c & 0xFF00) >> 8, c & 0xFF);
}

void KImageMarker::setFontSize(cv::Scalar s)
{
    p->_cvText->setFont(&s);
}

int KImageMarker::color() const
{
    return (static_cast<uchar>(p->_color.val[0]) << 16) |
           (static_cast<uchar>(p->_color.val[1]) << 8) |
           (static_cast<uchar>(p->_color.val[2]));
}

KImageMarker &KImageMarker::drawCenterCross()
{
    KIM_CHECK_IMG();
    return *this;
}

KImageMarker &KImageMarker::drawText(const char *text, int x, int y, int size)
{
    KIM_CHECK_IMG();
    setFontSize(cv::Scalar(size));
    p->_cvText->putText(*p->_image, text, cv::Point(x, y), p->_color);
    return *this;
}

KImageMarker &KImageMarker::drawText(const char *text, const KPoint &point, int size)
{
    KIM_CHECK_IMG();
    setFontSize(cv::Scalar(size));
    p->_cvText->putText(*p->_image, text, point, p->_color);
    return *this;
}

KImageMarker &KImageMarker::drawRect(int x1, int y1, int x2, int y2)
{
    KIM_CHECK_IMG()
    // cv::rectangle(*p->_image, )
    cv::rectangle(*p->_image, KPoint(x1, y1), KPoint(x2, y2), p->_color, p->_width, cv::LINE_8, 0);
    return *this;
}

KImageMarker &KImageMarker::drawRect(const KRect &rect)
{
    KIM_CHECK_IMG()
    cv::rectangle(*p->_image, rect, p->_color, p->_width, cv::LINE_8, 0);
    return *this;
}

KImageMarker &KImageMarker::drawCircle(int x, int y, int r)
{
    KIM_CHECK_IMG()
    cv::circle(*p->_image, cv::Point(x, y), r, p->_color, p->_width, cv::LINE_8, 0);
    return *this;
}

KImageMarker &KImageMarker::drawCircle(const KCircle &circle)
{
    KIM_CHECK_IMG()
    cv::circle(*p->_image, circle.center(), circle.r, p->_color, p->_width);
    return *this;
}

KImageMarker &KImageMarker::drawLine(int x1, int y1, int x2, int y2)
{
    KIM_CHECK_IMG()
    cv::line(*p->_image, cv::Point(x1, y1), cv::Point(x2, y2), p->_color, p->_width);
    return *this;
}

KImageMarker &KImageMarker::drawLine(const KLine &line)
{
    KIM_CHECK_IMG()
    cv::line(*p->_image, line.p1(), line.p2(), p->_color, p->_width);
    return *this;
}

KImageMarker &KImageMarker::drawLine(cv::Point &p1, cv::Point &p2)
{
    KIM_CHECK_IMG()
    cv::line(*p->_image, p1, p2, p->_color, p->_width);
    return *this;
}

KImageMarker &KImageMarker::drawRings(int x, int y, int r1, int r2)
{
    KIM_CHECK_IMG()
    cv::circle(*p->_image, cv::Point(x, y), r1, p->_color, p->_width);
    cv::circle(*p->_image, cv::Point(x, y), r2, p->_color, p->_width);
    return *this;
}

KImageMarker &KImageMarker::drawRings(const KRing &ring)
{
    KIM_CHECK_IMG()
    cv::circle(*p->_image, ring.center(), ring.r1, p->_color, p->_width);
    cv::circle(*p->_image, ring.center(), ring.r2, p->_color, p->_width);
    return *this;
}

KImageMarker &KImageMarker::drawContour(KContours &c, int idx /*= -1*/)
{
    cv::drawContours(*p->_image, c.coutours, idx, p->_color, p->_width);
    return *this;
}

void KImageMarker::setCurFrameIndex(int frameIndex)
{
    if (0 <= frameIndex && MaxFrameCount > frameIndex)
        p->_curFrameIndex = frameIndex;
}

void KImageMarker::addText(const char *text, KPoint point, KColor color, int size)
{
    p->_textVec[p->_curFrameIndex].emplace_back(TextStruct{text, point, color, size});
}

void KImageMarker::addRect(const KRect &rect, KColor color, int lineWidth)
{
    p->_rectVec[p->_curFrameIndex].emplace_back(RectStruct{rect, color, lineWidth});
}

void KImageMarker::addCircle(const int x, const int y, const int r, KColor color, int lineWidth)
{
    p->_circleVec[p->_curFrameIndex].emplace_back(CircleStruct{KCircle(x, y, r), color, lineWidth});
}

void KImageMarker::addLine(const Point &p1, const Point &p2, KColor color, int lineWidth)
{
    p->_lineVec[p->_curFrameIndex].emplace_back(LineStruct{p1, p2, color, lineWidth});
}

void KImageMarker::drawImage()
{
    if (!p->_drawEnable)
        return;
    for (TextStruct &textS : p->_textVec[p->_curFrameIndex])
    {
        setColor(textS.color);
        drawText(textS.text.c_str(), textS.point, textS.size);
    }
    for (RectStruct &rectS : p->_rectVec[p->_curFrameIndex])
    {
        setColor(rectS.color);
        setWidth(rectS.lineWidth);
        drawRect(rectS.rect);
    }
    for (CircleStruct &circleS : p->_circleVec[p->_curFrameIndex])
    {
        setColor(circleS.color);
        setWidth(circleS.lineWidth);
        drawCircle(circleS.circle);
    }
    for (LineStruct &lineS : p->_lineVec[p->_curFrameIndex])
    {
        setColor(lineS.color);
        setWidth(lineS.lineWidth);
        drawLine(lineS.p1, lineS.p2);
    }
}

void KImageMarker::clearDrwaBuffer()
{
    for (int i = 0; i < MaxFrameCount; ++i)
    {
        p->_textVec[i].clear();
        p->_rectVec[i].clear();
        p->_circleVec[i].clear();
        p->_lineVec[i].clear();
    }
}

void KImageMarker::setDrawEnable(bool enable)
{
    p->_drawEnable = enable;
}

KSmartParamMarker::KSmartParamMarker(int width /*= 4*/, KColor c /*= KCV_GREEN*/)
{
    _maker.setWidth(width);
    _maker.setColor(c);
}

bool KSmartParamMarker::setValue(void *val)
{
    return true;
}

int KSmartParamMarker::type() const
{
    return SPT_Marker;
}

void *KSmartParamMarker::value()
{
    return &_maker;
}

void KSmartParamMarker::setWidth(int val)
{
    _maker.setWidth(val);
}

void KSmartParamMarker::setColor(KColor c)
{
    _maker.setColor(c);
}

int KSmartParamMarker::width() const
{
    return _maker.width();
}

KColor KSmartParamMarker::color() const
{
    return _maker.color();
}

KImageMarker &KSmartParamMarker::marker()
{
    return _maker;
}

const KImageMarker &KSmartParamMarker::marker() const
{
    return _maker;
}
