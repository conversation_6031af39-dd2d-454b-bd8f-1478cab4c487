﻿#include "KAIEngine.h"
#include "common/KDefines.h"
#include "common/KSTLTools.h"
#include <memory>
#include <map>
#include <iostream>

#include <mutex>

KAIEngineInferData::KAIEngineInferData()
{
}

KAIEngineInferData::~KAIEngineInferData()
{
}

/////////////////////////////////////////////

class KAIEngineInferEnityPrivate
{
public:
    KAIEngineInferEnityPrivate(KAIEngine *e) : _engine(e)
    {
    }

    KAIEngineInferEnityPrivate(const std::string &id, KAIEngine *e) : _id(id), _engine(e)
    {
    }

    ~KAIEngineInferEnityPrivate()
    {
        //_engine->removeEnity(_id);
    }

public:
    std::string _id;
    KAIEngine *_engine = nullptr;
};

KAIEngineInferEnity::KAIEngineInferEnity(KAIEngine *e)
{
    p = new KAIEngineInferEnityPrivate(e);
}

KAIEngineInferEnity::KAIEngineInferEnity(const std::string &id, KAIEngine *e)
{
    p = new KAIEngineInferEnityPrivate(id, e);
}

KAIEngineInferEnity::~KAIEngineInferEnity()
{
    RELEASE_COMMON_PTR(p);
}

KAIEngine *KAIEngineInferEnity::engine() const
{
    return p->_engine;
}

const std::string &KAIEngineInferEnity::id() const
{
    return p->_id;
}

void KAIEngineInferEnity::setId(const std::string &id)
{
    p->_id = id;
}

/////////////////////////////////////////////

class KAIEnginePrivate
{
public:
    std::mutex _mutex;
    std::string _path;
    std::string _id;
    std::string _name;
    std::string _instruction;
    int _createEnityCode = 0; //
    std::map<std::string, std::shared_ptr<KAIEngineInferEnity>> _enitys;
    typedef std::map<std::string, std::shared_ptr<KAIEngineInferEnity>>::iterator KEnityIterator;
};

KAIEngine::KAIEngine(const std::string &id) : p(0)
{
    CREATE_COMMON_PTR(p, KAIEnginePrivate);
    p->_id = id;
}

KAIEngine::~KAIEngine()
{
    RELEASE_COMMON_PTR(p);
}

std::string KAIEngine::id() const
{
    return p->_id;
}

std::string KAIEngine::name() const
{
    return p->_name;
}

std::string KAIEngine::instruction() const
{
    return p->_instruction;
}

void KAIEngine::setName(const std::string &text)
{
    p->_name = text;
}

void KAIEngine::setInstruction(const std::string &text)
{
    p->_instruction = text;
}

std::string KAIEngine::path() const
{
    return p->_path;
}

int KAIEngine::enityCount() const
{
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return p->_enitys.size();
}

KAIEngineInferEnity *KAIEngine::enity(const std::string &id) const
{
    if (id.empty())
        return 0;

    // 	KAIEnginePrivate::KEnityIterator ite = p->_enitys.find(id);
    // 	if (ite != p->_enitys.end())
    // 		return ite->second.get();
    // 	return 0;
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return KSTLTools::find_in_spmap(p->_enitys, id);
}

KAIEnity1D KAIEngine::enitys() const
{
    // 	KAIEnity1D allEntiys(enityCount(), 0);
    // 	KAIEnginePrivate::KEnityIterator ite = p->_enitys.begin();
    // 	for (int i = 0; ite != p->_enitys.end(); ++i, ++ite)
    // 	{
    // 		allEntiys[i] = ite->second.get();
    // 	}
    // 	return allEntiys;
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return KSTLTools::spmap_to_vec(p->_enitys);
}

KAIEngineInferEnity *KAIEngine::operator[](const std::string &id)
{
    return enity(id);
}

const KAIEngineInferEnity *KAIEngine::operator[](const std::string &id) const
{
    return enity(id);
}

int KAIEngine::removeEnity(const std::string &id)
{
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    KSTLTools::remove_from_spmap(p->_enitys, id);
    if (enityCount() == 0)
    {
        //没有推理实体时，释放所有资源.
        int ret = unload();
        return ret;
    }
    return K_Engine_OK;
}

int KAIEngine::createEnityRetCode() const
{
    return p->_createEnityCode;
}

void KAIEngine::setCreateEnityRetCode(int code)
{
    p->_createEnityCode = code;
}

KAIEngineInferEnity *KAIEngine::addEnity(const std::string &id)
{
    //    if (enityCount() == 0)
    //    {
    //        //运行时初始化
    //        if (load(p->_path))
    //        {
    //            return 0;
    //        }
    //    }

    KAIEngineInferEnity *e = 0;
    if (id.empty() || !(e = createEnity(id)))
        return 0;
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    if (!KSTLTools::insert_in_spmap(p->_enitys, id, e))
    {
        delete e;
        return p->_enitys[id].get();
    }
    return e;
}

int KAIEngine::setPath(const std::string &enginePath)
{
    if (enginePath.empty())
        return K_Engine_PathInValid;
    p->_path = enginePath;
    return K_Engine_OK;
}

///////////////////////////////////////////
class KAIEngineManagerPrivate
{

public:
    std::mutex _mutex;
    std::map<std::string, std::shared_ptr<KAIEngine>> _engines;
};

KAIEngineManager *KAIEngineManager::singleInstance()
{
    static std::shared_ptr<KAIEngineManager> instance(new KAIEngineManager());
    return instance.get();
}

KAIEngineManager::KAIEngineManager() : p(0)
{
    CREATE_COMMON_PTR(p, KAIEngineManagerPrivate);
}

KAIEngineManager::~KAIEngineManager()
{
    RELEASE_COMMON_PTR(p);
}

bool KAIEngineManager::add(KAIEngine *e)
{
    if (!e)
        return false;
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return KSTLTools::insert_in_spmap(p->_engines, e->id(), e);
}

bool KAIEngineManager::remove(const std::string &id)
{
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return KSTLTools::remove_from_spmap(p->_engines, id);
}

bool KAIEngineManager::remove(KAIEngine *engine)
{
    return remove(engine->id());
}

bool KAIEngineManager::find(const std::string &id) const
{
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return p->_engines.find(id) != p->_engines.end();
}

KAIEngine *KAIEngineManager::engine(const std::string &id) const
{
    // 	auto ite = p->_engines.find(id);
    // 	if (ite == p->_engines.end())
    // 		return 0;
    //
    // 	return ite->second.get();
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return KSTLTools::find_in_spmap(p->_engines, id);
}

int KAIEngineManager::count() const
{
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return p->_engines.size();
}

KAIEngine *KAIEngineManager::operator[](const std::string &id)
{
    return engine(id);
}

const KAIEngine *KAIEngineManager::operator[](const std::string &id) const
{
    return engine(id);
}

KAIEngine1D KAIEngineManager::engines() const
{
    // 	KAIEngine1D allEngines(count(), 0);
    // 	auto ite = p->_engines.begin();
    // 	for (int i = 0; ite != p->_engines.end(); ++ite, ++i)
    // 	{
    // 		allEngines[i] = ite->second.get();
    // 	}
    // 	return allEngines;
    //    std::lock_guard<std::mutex> lock(p->_mutex);
    return KSTLTools::spmap_to_vec(p->_engines);
}
