﻿#include "algorithm/KAlgorithmResultManager.h"
#include "common/KSTLTools.h"
#include <atomic>
#include <numeric>

#ifdef <PERSON>
#define Valve_Output_Count 10
#else
#define Valve_Output_Count 8
#endif
class KAlgorithmResultManagerPrivate
{
public:
    KAlgorithmResultManagerPrivate() : _ngStatisCount(0)
    {
        _ngStatisCount.resize(Valve_Output_Count);
        //        _dataCleaned.resize(Valve_Output_Count);
        for (int i = 0; i < Valve_Output_Count; ++i)
            _dataCleaned[i] = false;
    }

public:
    std::array<std::atomic_bool, Valve_Output_Count> _dataCleaned;

    std::vector<unsigned int> _ngStatisCount;
    std::map<unsigned int, std::atomic_uint, std::greater<unsigned int>> _statis;
    std::map<unsigned int, KAlgorithmResultDescription, std::greater<unsigned int>> _results;
};
//////////////////////////////////////////////////////////////////////////
KDEFINITION_SINGLECLASS(KAlgorithmResultManager)

KAlgorithmResultManager::KAlgorithmResultManager() : p(0)
{
    CREATE_COMMON_PTR(p, KAlgorithmResultManagerPrivate);
}

KAlgorithmResultManager::~KAlgorithmResultManager()
{
    RELEASE_COMMON_PTR(p);
}

int KAlgorithmResultManager::count() const
{
    return p->_results.size();
}

const std::string &KAlgorithmResultManager::summary(unsigned int code)
{
    return p->_results[code].Summary;
}

const std::string &KAlgorithmResultManager::instruction(unsigned int code)
{
    return p->_results[code].Instruction;
}

void KAlgorithmResultManager::registerResult(const KAlgorithmResultDescription &desp)
{
    p->_results[desp.Code] = desp;
    if (p->_statis.find(desp.Code) != p->_statis.end())
        return;

    p->_statis[desp.Code] = 0;
}

std::vector<KAlgorithmResultDescription *> KAlgorithmResultManager::results()
{
    std::vector<KAlgorithmResultDescription *> allResult(count());
    int index = -1;
    auto ite = p->_results.begin();
    while (ite != p->_results.end())
    {
        allResult[++index] = &(ite->second);
        ++ite;
    }
    return allResult;
}

KAlgorithmResultDescription *KAlgorithmResultManager::result(unsigned int code)
{
    if (p->_results.find(code) == p->_results.end())
        //if (std::find(p->_results.begin(), p->_results.end(), code) == p->_results.end())
        return 0;
    return &p->_results[code];
}

KAlgorithmResultDescription *KAlgorithmResultManager::operator[](unsigned int code)
{
    return result(code);
}

void KAlgorithmResultManager::addStatis(unsigned int code)
{
    ++p->_statis[code];
}

void KAlgorithmResultManager::updateNgStatisCount(int groupid, unsigned int count)
{
    if (p->_dataCleaned[groupid])
    {
        p->_dataCleaned[groupid] = false;
        return;
    }
    p->_ngStatisCount[groupid] = count;
}

unsigned int KAlgorithmResultManager::statisCount(unsigned int code)
{
    return p->_statis[code];
}

unsigned int KAlgorithmResultManager::statisSumNgCount(int groupid)
{
#ifdef JiaoSai
    return p->_ngStatisCount[groupid];
#else
    return std::accumulate(p->_ngStatisCount.begin(), p->_ngStatisCount.end(), 0);
#endif
}

void KAlgorithmResultManager::resetStatis()
{
    auto ite = p->_statis.begin();
    while (ite != p->_statis.end())
    {
        ite->second = 0;
        ++ite;
    }
    for (int i = 0; i < Valve_Output_Count; ++i)
    {
        p->_ngStatisCount[i] = 0;
        p->_dataCleaned[i] = true;
    }
}

void KAlgorithmResultManager::resetStatis(unsigned int code)
{
    p->_statis[code] = 0;
}
