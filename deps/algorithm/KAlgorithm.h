﻿#ifndef KALGORITHM_H
#define KALGORITHM_H

#include "KAIVipAlgorithmGlobal.h"
#include "KAlgorithmTypes.h"
#include "KSmartParam.h"
#include "KImageFrame.h"
#include "KAlgorithmModule.h"
#include "KImageMarker.h"

class bm_image;

//-------------------------------------------------------------------------//
//算法结果可继承，可传递自定义数据
class KAlgorithmProcessResult
{
public:
    bool IsOk;
    unsigned int Code; //错误代码 参加枚举KAlgorithmResultCode.
};

class KAlgorithmPrivate;
class KAIVIPALGORITHM_EXPORT KAlgorithm
{
public:
    KAlgorithm();
    virtual ~KAlgorithm();

    void setGroupId(int groupid);
    int groupId();

    void setId(const std::string &aid);
    std::string id() const;

    void setInfo(const KAlgorithmInfo &info);
    const KAlgorithmInfo &info();

    virtual const KAlgorithmProcessResult *process(KImageFrame1D &srcFrame, KImageFrame1D &dstFrame, int index) = 0;

    bm_image *getBmImage(int frameIndex = 0) const;
    void setBmImagePtr(bm_image *image, int frameIndex = 0);
    void clearBmImagePtrs();

    virtual void setTemplateDatas(const void *data);
    bool sendTempDatasState() const;
    void setSendTempDatasState(bool send);

    int moduleCount();
    void addModule(KAlgorithmModule *m);
    KAlgorithmModule *module(const std::string &id);
    KAlgorithmModule1D modules() const;

    KSmartParam1D inParams() const;
    KSmartParam1D outParams() const;

    KImageMarker &imageMarker();

    void refreshAllParams();

protected:
    void addInParams(KSmartParam *param);
    void addOutParams(KSmartParam *param);

private:
    KAlgorithmPrivate *p;
};
#endif
