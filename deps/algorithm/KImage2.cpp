﻿#include "algorithm/KImage2.h"
#include "common/KDefines.h"

#define KCHECK_MARKER() \
    if (!marker)        \
        return;         \
    marker->setImage(&_src);

KImage2::KImage2()
{
}

KImage2::KImage2(const KImage2 &img) : _src(img._src)
{
}

KImage2::KImage2(cv::Mat &mat) : _src(mat)
{
}

KImage2::KImage2(int width, int height, int type, void *buffer, int step) : _src(height, width, type, buffer, step)
{
}

KImage2::KImage2(const KImageFrame &frame) : KImage2(frame.Width, frame.Height, CV_8UC3, frame.DataBuffer)
{
}

KImage2::KImage2(const std::string &filename) : KImage2()
{
    load(filename);
}

KImage2::~KImage2()
{
}

void KImage2::load(const std::string &filename)
{
    _src = cv::imread(filename);
}

void KImage2::load(int width, int height, unsigned *buffer)
{
    if (!buffer || width == 0 || height == 0)
        return;
    _src = cv::Mat(height, width, CV_8UC3, buffer);
}

void KImage2::load(int width, int height, float *buffer)
{
    if (!buffer || width == 0 || height == 0)
        return;
    _src = cv::Mat(height, width, CV_32FC1, buffer);
}

unsigned char *KImage2::data() const
{
    return _src.data;
}

int KImage2::width() const
{
    return _src.cols;
}

int KImage2::height() const
{
    return _src.rows;
}

int KImage2::channel() const
{
    return _src.channels();
}

cv::Mat &KImage2::src()
{
    return _src;
}

KImage2 &KImage2::zero()
{
    memset(_src.data, 0, _src.rows * _src.cols * _src.channels());
    return *this;
}

KPoint KImage2::center() const
{
    return KPoint(_src.cols / 2, _src.rows / 2);
}

KImage2 &KImage2::set(unsigned char val)
{
    *this = val;
    return *this;
}

KImage2 &KImage2::flip(int flag)
{
    cv::flip(_src, _src, flag);
    return *this;
}

KImage2 &KImage2::crossRotate(int flag)
{
    cv::rotate(_src, _src, flag);
    return *this;
}

KImage2 &KImage2::rotate(float angle)
{
    cv::Point2f center(_src.cols >> 1, _src.rows >> 1);
    cv::Mat rotateMatrix = cv::getRotationMatrix2D(center, angle, 1.0);
    cv::warpAffine(_src, _src, rotateMatrix, cv::Size(_src.cols, _src.rows));
    return *this;
}

KImage2 &KImage2::scale(float ratio)
{
    _src.resize((int)(_src.cols * ratio), (int)(_src.rows * ratio));
    return *this;
}

KImage2 &KImage2::resize(int w, int h)
{
    _src.resize(w, h);
    return *this;
}

KImage2 &KImage2::resizeTo(KImage2 &dst, int w, int h)
{
    cv::resize(_src, dst.src(), cv::Size(w, h));
    return *this;
}

KContours KImage2::contours()
{
    KContours cs;
    cv::findContours(_src, cs.coutours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    return cs;
}

KRegions KImage2::regions()
{
    run runs[MaxRuns];
    KRegions regions;
    SegmentImage(data(), runs, regions.regions, regions.regionsCount, width(), height(), 1);
    return regions;
}

KImage2 KImage2::cropRectangle(const KRect &rect)
{
    cv::Mat dst = _src(rect).clone();
    return KImage2(dst);
}

KImage2 KImage2::antiCropRectangle(const KRect &rect)
{
    cv::Mat mask(_src.rows, _src.cols, _src.type(), cv::Scalar(0xff, 0xff, 0xff)), dst;
    cv::rectangle(mask, rect, cv::Scalar(0, 0, 0), -1);
    _src.copyTo(dst, mask);

    return KImage2(dst);
}

KImage2 KImage2::cropCircle(const KCircle &c)
{
    cv::Mat mask(_src.rows, _src.cols, _src.type(), cv::Scalar(0, 0, 0)), dst;
    cv::circle(mask, c.center(), c.r, cv::Scalar(0xff, 0xff, 0xff), -1);
    _src.copyTo(dst, mask);

    cv::Mat dst1 = dst(cv::Rect(c.x - c.r, c.y - c.r, c.r << 1, c.r << 1)).clone();
    return KImage2(dst1);
}

KImage2 KImage2::antiCropCircle(const KCircle &c)
{
    cv::Mat mask(_src.rows, _src.cols, _src.type(), cv::Scalar(0xff, 0xff, 0xff)), dst;
    cv::circle(mask, c.center(), c.r, cv::Scalar(0, 0, 0), -1);
    _src.copyTo(dst, mask);
    return KImage2(dst);
}

KImage2 KImage2::cropRing(const KRing &ring)
{
    cv::Mat mask2(_src.rows, _src.cols, _src.type(), cv::Scalar(0, 0, 0)), dst2, dst1;
    cv::Mat mask1(_src.rows, _src.cols, _src.type(), cv::Scalar(0xff, 0xff, 0xff));
    cv::circle(mask2, ring.center(), ring.r2, cv::Scalar(0xff, 0xff, 0xff), -1);
    cv::circle(mask1, ring.center(), ring.r1, cv::Scalar(0, 0, 0), -1);
    _src.copyTo(dst2, mask2);
    dst2.copyTo(dst1, mask1);

    cv::Mat dst = dst1(cv::Rect(ring.x - ring.r2, ring.y - ring.r2, ring.r2 << 1, ring.r2 << 1)).clone();
    return KImage2(dst);
}

KImage2 KImage2::antiCropRing(const KRing &ring)
{
    cv::Mat mask1(_src.rows, _src.cols, _src.type(), cv::Scalar(0, 0, 0)), dst2, dst1;
    cv::Mat mask2(_src.rows, _src.cols, _src.type(), cv::Scalar(0xff, 0xff, 0xff));
    cv::circle(mask1, ring.center(), ring.r1, cv::Scalar(0xff, 0xff, 0xff), -1);
    cv::circle(mask2, ring.center(), ring.r2, cv::Scalar(0, 0, 0), -1);
    _src.copyTo(dst2, mask2);
    _src.copyTo(dst1, mask1);

    //各自反向切后相加
    cv::Mat dst = dst1 + dst2;
    return KImage2(dst);
}

void KImage2::drawCenterCross(KImageMarker *marker)
{
    KCHECK_MARKER();
    marker->drawCenterCross();
}

void KImage2::drawRectangle(KImageMarker *marker, const KRect &rect)
{
    KCHECK_MARKER();
    marker->drawRect(rect);
}

void KImage2::drawCircle(KImageMarker *marker, const KCircle &c)
{
    KCHECK_MARKER();
    marker->drawCircle(c);
}

void KImage2::drawCircle(KImageMarker *marker, const KPoint &c, int radius)
{
    KCHECK_MARKER();
    marker->drawCircle(c.x, c.y, radius);
}

void KImage2::drawLine(KImageMarker *marker, const KLine &l)
{
    KCHECK_MARKER();
    marker->drawLine(l);
}

void KImage2::drawRing(KImageMarker *marker, const KRing &ring)
{
    KCHECK_MARKER();
    marker->drawRings(ring);
}

void KImage2::drawRing(KImageMarker *marker, const KPoint &c, int r1, int r2)
{
    KCHECK_MARKER();
    marker->drawRings(c.x, c.y, r1, r2);
}

void KImage2::drawText(KImageMarker *marker, const char *text, const KPoint &point, int fontSize)
{
    KCHECK_MARKER();
    marker->drawText(text, point, fontSize);
}

KImage2 &KImage2::clone(const KImage2 &img)
{
    _src = img._src.clone();
    return *this;
}

KImage2 &KImage2::operator=(unsigned char val)
{
    memset(_src.data, val, _src.rows * _src.cols * _src.channels());
    return *this;
}

KImage2 &KImage2::operator=(const KImage2 &img)
{
    _src = img._src;
    return *this;
}

KImage2 &KImage2::operator=(const cv::Mat &mat)
{
    _src = mat;
    return *this;
}
