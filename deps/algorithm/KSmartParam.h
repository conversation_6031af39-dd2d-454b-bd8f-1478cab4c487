﻿#ifndef KSMARTPARAM_H
#define KSMARTPARAM_H

#include "KAIVipAlgorithmGlobal.h"
#include "KAlgorithmTypes.h"

using std::string;

//////////////////////////////////////////////////////////////////////////
//表示两个参数的依存关系
class KSmartParam;
class KSmartParamGroup;
class KSmartParamValidator;

typedef std::vector<KSmartParam *> KSmartParam1D;
typedef std::vector<KSmartParamGroup *> KSmartParamGroup1D;
typedef std::map<std::string, std::shared_ptr<KSmartParam>> KParamDictionary;
typedef KParamDictionary::iterator KParamDictionaryIterator;

class KAIVIPALGORITHM_EXPORT KSmartParamRelation
{
public:
    KSmartParamRelation(KSmartParam *src);

public:
    //关系码 系统内置见KSmartParamRelationCode 可以自定义
    virtual int code() const = 0;

    //业务逻辑
    virtual void action(void *val) = 0;

    void setSrc(KSmartParam *src);

    KSmartParam *src();

protected:
    KSmartParam *_src;
};

//////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////
class KSmartParamPrivate;
class KAIVIPALGORITHM_EXPORT KSmartParam
{
public:
    KSmartParam();
    virtual ~KSmartParam();

    void setInfo(const KSmartParamInfo &pInfo);
    const KSmartParamInfo &info() const;

    void setGroup(KSmartParamGroup *g);
    KSmartParamGroup *group();

    int groupId();
    std::string name() const;
    std::string id() const;

    bool isSave() const;
    bool isVisible() const;
    int level() const;

    void setValidator(KSmartParamValidator *v);
    KSmartParamValidator *validator();

public:
    virtual bool setValue(void *val) = 0;
    virtual int type() const = 0;
    virtual void refresh() = 0;

    virtual void *value() = 0;
    bool setEnable(bool e);
    bool enable();

    KSmartParamRelation *addRelation(KSmartParamRelation *r);
    std::vector<KSmartParamRelation *> relations(int code);

protected:
    void triger(int code, void *context);
    bool validate(void *val = 0);

protected:
    KSmartParamPrivate *p;
};
//////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////
class KAIVIPALGORITHM_EXPORT KSmartParamBool : public KSmartParam
{
public:
    KSmartParamBool(bool b = true);

    bool setValue(void *val) override;
    bool setValue(bool b);
    int type() const override;

    void refresh() override;
    bool bvalue() const;
    void *value() override;

public:
    bool _b;
};

//////////////////////////////////////////////////////////////////////////
class KAIVIPALGORITHM_EXPORT KSmartParamInt : public KSmartParam
{
public:
    KSmartParamInt(int val = 0, int min = 0, int max = 100);

    virtual int type() const override;

public:
    bool setValue(void *val) override;
    void *value() override;
    bool setValue(int val);
    int ivalue() const;
    void refresh() override;

    void setRange(int min, int max);
    void setStep(int s);
    int step() const;

    void setMaximum(int m);
    void setMinimum(int m);
    int maximum();
    int minimum();

    bool increase();
    bool decrease();

protected:
    int _i;
    int _max;
    int _min;
    int _step;
};

//////////////////////////////////////////////////////////////////////////
class KAIVIPALGORITHM_EXPORT KSmartParamDouble : public KSmartParam
{
public:
    KSmartParamDouble(double d = 0.5, double min = 0.0, double max = 1.0);

public:
    bool setValue(void *val) override;
    void *value() override;
    bool setValue(double val);
    double dvalue() const;
    int type() const override;
    void refresh() override;

    void setRange(double min, double max);
    void setStep(double s);
    double step() const;

    void setMaximum(double m);
    void setMinimum(double m);
    double maximum();
    double minimum();

    void increase();
    void decrease();

protected:
    double _d;
    double _max;
    double _min;
    double _step;
};

//////////////////////////////////////////////////////////////////////////
class KAIVIPALGORITHM_EXPORT KSmartParamString : public KSmartParam
{
public:
    KSmartParamString(const string &str = "");

public:
    bool setValue(void *val) override;
    void *value() override;
    bool setValue(const string &val);
    string svalue() const;
    int type() const override;

    void refresh() override;

protected:
    string _s;
};

//////////////////////////////////////////////////////////////////////////
class KAIEngine;
class KAIEngineInferEnity;
class KAIVIPALGORITHM_EXPORT KSmartParamAI : public KSmartParam
{
public:
    KSmartParamAI(KAIEngineInferEnity *e = 0);

public:
    bool setValue(void *val) override;
    void *value() override;
    //enity要设置为空的情况有两种：重置模型、模型管理器里删除模型。重置时引擎还在，模型管理器删除的时候engine也删除了。处理逻辑不一样，否则
    // 模型管理器删除设置空enity的时候，会获取engine，但是此时 engine已经不存在了，不能再用engine指针去处理。
    // engineRemoved 为true的时候，e一定是 0！
    bool setEnity(KAIEngineInferEnity *e, bool engineRemoved = false);
    KAIEngineInferEnity *enity();
    int type() const override;
    KAIEngine *engine() const;
    void refresh() override;
    //enity要设置为空的情况有两种：重置模型、模型管理器里删除模型。重置时引擎还在，模型管理器删除的时候engine也删除了。处理逻辑不一样，否则
    // 模型管理器删除设置空enity的时候，会获取engine，但是此时 engine已经不存在了，不能再用engine指针去处理。
    // engineRemoved 为true的时候，e一定是 0！
    void clearEntity(bool engineRemoved = false);
    void initEnity();

protected:
    KAIEngineInferEnity *_e;
    KAIEngineInferEnity *_lastEnity;
};

class KSmartParamCollectionPrivate;
class KAIVIPALGORITHM_EXPORT KSmartParamCollection
{
public:
    KSmartParamCollection();

    void addParam(KSmartParam *param);
    void addParams(KSmartParamGroup *params);

    KSmartParam *param(const std::string &name);
    int paramCount() const;
    int groupCount() const;

    KSmartParam1D params() const;
    KSmartParamGroup1D groups() const;

    KSmartParamGroup *group(int id);
    KSmartParamGroup *group(const std::string &name);

private:
    KSmartParamCollectionPrivate *p;
};
#endif
