﻿#include "algorithm/KSmartParam.h"
#include "algorithm/KAIEngine.h"
#include "algorithm/KSmartParamGroup.h"
#include "algorithm/KSmartParamValidator.h"
#include "common/KDefines.h"
#include <vector>
#include <map>
#include <memory>
#include <algorithm>

#include <iostream>

class KSmartParamPrivate
{
public:
    KSmartParamPrivate() : _enable(true), _groupid(0), _validator(0)
    {
    }

    ~KSmartParamPrivate()
    {
        RELEASE_COMMON_PTR(_validator);
    }

public:
    bool _enable;
    int _groupid;
    // _relations;
    KSmartParamInfo _info;
    KSmartParamGroup *_group;
    KSmartParamValidator *_validator;
    std::map<int, std::vector<KSmartParamRelation *>> _relations;
};

KSmartParam::KSmartParam() : p(0)
{
    p = new KSmartParamPrivate();
}

KSmartParam::~KSmartParam()
{
    if (p)
    {
        delete p;
        p = 0;
    }
}

void KSmartParam::setInfo(const KSmartParamInfo &pInfo)
{
    p->_info = pInfo;
}

const KSmartParamInfo &KSmartParam::info() const
{
    return p->_info;
}

void KSmartParam::setGroup(KSmartParamGroup *g)
{
    p->_group = g;
}

KSmartParamGroup *KSmartParam::group()
{
    return p->_group;
}

int KSmartParam::groupId()
{
    if (!p->_group)
        return -1;
    return p->_group->id();
}

std::string KSmartParam::name() const
{
    return p->_info.Name;
}

std::string KSmartParam::id() const
{
    return p->_info.Id;
}

bool KSmartParam::isSave() const
{
    return (((p->_info.Flag) & KSMARTPARAM_SAVE) > 0);
}

bool KSmartParam::isVisible() const
{
    return (((p->_info.Flag) & KSMARTPARAM_UNVISIBLE) == 0);
}

int KSmartParam::level() const
{
    return 0;
}

void KSmartParam::setValidator(KSmartParamValidator *v)
{
    if (!v)
        return;
    v->setParam(this);
    p->_validator = v;
}

KSmartParamValidator *KSmartParam::validator()
{
    return p->_validator;
}

bool KSmartParam::setEnable(bool e)
{
    if (e == p->_enable)
        return false;

    p->_enable = e;
    triger(SPRC_Enable, &p->_enable);
    return true;
}

bool KSmartParam::enable()
{
    return p->_enable;
}

void KSmartParam::triger(int code, void *context)
{
    auto ite = p->_relations.find(code);
    if (ite == p->_relations.end())
        return;
    for (KSmartParamRelation *p : ite->second)
    {
        p->action(context);
    }
}

bool KSmartParam::validate(void *val)
{
    if (!p->_validator)
        return true;

    return p->_validator->valiate(val ? val : value());
}

KSmartParamRelation *KSmartParam::addRelation(KSmartParamRelation *r)
{
    auto ite = p->_relations.find(r->code());
    if (ite == p->_relations.end())
        p->_relations.insert(std::make_pair<int, std::vector<KSmartParamRelation *>>(r->code(), std::vector<KSmartParamRelation *>()));

    p->_relations[r->code()].push_back(r);
    return r;
}

std::vector<KSmartParamRelation *> KSmartParam::relations(int code)
{
    auto ite = p->_relations.find(code);
    if (ite == p->_relations.end())
        return std::vector<KSmartParamRelation *>();
    return p->_relations[code];
}

//////
KSmartParamInt::KSmartParamInt(int val /*= 0*/, int min /*= 0*/, int max /*= 100*/) : KSmartParam(), _i(val), _min(min), _max(max), _step(1)
{
}

int KSmartParamInt::type() const
{
    return SPT_Int;
}

bool KSmartParamInt::setValue(int val)
{
    //	if (val == _i)
    //		return false;
    if (val < _min)
    {
        triger(SPRC_OverMin, &val);
        return false;
    }
    if (val > _max)
    {
        triger(SPRC_OverMax, &val);
        return false;
    }
    if (!validate(&val))
        return false;
    _i = val;
    triger(SPRC_ValueChanged, &_i);
    return true;
}

bool KSmartParamInt::setValue(void *val)
{
    return setValue(*static_cast<int *>(val));
}

int KSmartParamInt::ivalue() const
{
    return _i;
}

void KSmartParamInt::refresh()
{
    triger(SPRC_ValueChanged, &_i);
}

void *KSmartParamInt::value()
{
    return &_i;
}

void KSmartParamInt::setRange(int min, int max)
{
    _min = min;
    _max = max;
    // 如果当前值大于重设的范围值，要重设当前值
    if (_i < min)
        setValue(min);
    else if (_i > max)
        setValue(max);
}

void KSmartParamInt::setStep(int s)
{
    _step = s;
}

int KSmartParamInt::step() const
{
    return _step;
}

void KSmartParamInt::setMaximum(int m)
{
    _max = m;
}

void KSmartParamInt::setMinimum(int m)
{
    _min = m;
}

int KSmartParamInt::maximum()
{
    return _max;
}

int KSmartParamInt::minimum()
{
    return _min;
}

bool KSmartParamInt::increase()
{
    return setValue(_i + _step);
}

bool KSmartParamInt::decrease()
{
    return setValue(_i - _step);
}

KSmartParamDouble::KSmartParamDouble(double d /*= 0.5*/, double min /*= 0.0*/, double max /*= 1.0*/) : KSmartParam(), _d(d), _min(min), _max(max), _step(0.05)
{
}

bool KSmartParamDouble::setValue(void *val)
{
    return setValue(*static_cast<double *>(val));
}

bool KSmartParamDouble::setValue(double val)
{
    //	if (val == _d)
    //		return false;

    if (!validate())
        return false;

    if (val < _min)
    {
        triger(SPRC_OverMin, &val);
        return false;
    }

    if (val > _max)
    {
        triger(SPRC_OverMax, &val);
        return false;
    }
    if (!validate(&val))
        return false;

    _d = val;
    triger(SPRC_ValueChanged, &_d);
    return true;
}

void *KSmartParamDouble::value()
{
    return &_d;
}

double KSmartParamDouble::dvalue() const
{
    return _d;
}

int KSmartParamDouble::type() const
{
    return SPT_Double;
}

void KSmartParamDouble::refresh()
{
    triger(SPRC_ValueChanged, &_d);
}

void KSmartParamDouble::setRange(double min, double max)
{
    if (min > max)
        return;
    _min = min;
    _max = max;
    // 如果当前值大于重设的范围值，要重设当前值
    if (_d < min)
        setValue(min);
    else if (_d > max)
        setValue(max);
}

void KSmartParamDouble::setStep(double s)
{
    _step = s;
}

double KSmartParamDouble::step() const
{
    return _step;
}

void KSmartParamDouble::setMaximum(double m)
{
    _max = m;
}

void KSmartParamDouble::setMinimum(double m)
{
    _min = m;
}

double KSmartParamDouble::maximum()
{
    return _max;
}

double KSmartParamDouble::minimum()
{
    return _min;
}

void KSmartParamDouble::increase()
{
    setValue(_d + _step);
}

void KSmartParamDouble::decrease()
{
    setValue(_d - _step);
}

KSmartParamRelation::KSmartParamRelation(KSmartParam *src) : _src(src)
{
}

void KSmartParamRelation::setSrc(KSmartParam *src)
{
    _src = src;
}

KSmartParam *KSmartParamRelation::src()
{
    return _src;
}

//////////////////////////////////////////////////////////////////////////
KSmartParamBool::KSmartParamBool(bool b) : KSmartParam(), _b(b)
{
}

bool KSmartParamBool::setValue(void *val)
{
    return setValue(*static_cast<bool *>(val));
}

bool KSmartParamBool::setValue(bool b)
{
    //	if (_b == b)
    //	{
    //		return false;
    //	}

    if (!validate(&b))
        return false;

    _b = b;
    triger(SPRC_ValueChanged, &_b);
    return true;
}

int KSmartParamBool::type() const
{
    return SPT_Bool;
}

void KSmartParamBool::refresh()
{
    triger(SPRC_ValueChanged, &_b);
}

void *KSmartParamBool::value()
{
    return &_b;
}

bool KSmartParamBool::bvalue() const
{
    return _b;
}

//////////////////////////////////////////////////////////////////////////
KSmartParamString::KSmartParamString(const string &str) : KSmartParam(), _s(str)
{
}

bool KSmartParamString::setValue(void *val)
{
    return setValue(*static_cast<string *>(val));
}

bool KSmartParamString::setValue(const string &val)
{
    //	if (_s == val)
    //	{
    //		return false;
    //	}

    if (!validate(const_cast<std::string *>(&val)))
        return false;

    _s = val;
    triger(SPRC_ValueChanged, &_s);
    return true;
}

void *KSmartParamString::value()
{
    return &_s;
}

string KSmartParamString::svalue() const
{
    return _s;
}

int KSmartParamString::type() const
{
    return SPT_String;
}

void KSmartParamString::refresh()
{
    triger(SPRC_ValueChanged, &_s);
}

//////////////////////////////////////////////////////////////////////////
KSmartParamAI::KSmartParamAI(KAIEngineInferEnity *e /*= 0*/) : _e(e), _lastEnity(0)
{
    _lastEnity = nullptr;
}

bool KSmartParamAI::setValue(void *val)
{
    return setEnity(static_cast<KAIEngineInferEnity *>(val));
}

bool KSmartParamAI::setEnity(KAIEngineInferEnity *e, bool engineRemoved)
{
    if (e == _e)
        return true;
    if (!validate(e))
        return false;
    // 模型相同不重复载入
    if ((e && _e) && e->engine() == _e->engine())
        return false;
    _e = e;
    if (_e != _lastEnity)
    {
        if (!engineRemoved) // 模型管理器删除的时候engine也删除了,不能再用engine指针去处理。
        {
            enity();
        }
    }

    triger(SPRC_ValueChanged, _e);
    return true;
}

void *KSmartParamAI::value()
{
    return enity();
}

KAIEngineInferEnity *KSmartParamAI::enity()
{
    if (_e != _lastEnity && _lastEnity)
    {
        if (_lastEnity->engine())
        {
            _lastEnity->engine()->removeEnity(_lastEnity->id());
        }
        _lastEnity = 0;
    }
    _lastEnity = _e;
    // std::cout << "************ KAIEngineInferEnity *KSmartParamAI::enity()--> lastEnity is seted, engine path= "
    //<< _lastEnity->engine()->path() << std::endl;
    return _lastEnity;
}

int KSmartParamAI::type() const
{
    return SPT_AI;
}

KAIEngine *KSmartParamAI::engine() const
{
    if (!_e)
        return 0;
    return _e->engine();
}

void KSmartParamAI::refresh()
{
    triger(SPRC_ValueChanged, _e);
}

void KSmartParamAI::clearEntity(bool engineRemoved)
{
    setEnity(nullptr, engineRemoved);
    _lastEnity = 0;
}

void KSmartParamAI::initEnity()
{
    //_lastEnity = nullptr;
}

//////////////////////////////////////////////////////////////////////////
