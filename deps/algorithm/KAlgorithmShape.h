﻿#ifndef KALGORITHMSHAPE_H
#define KALGORITHMSHAPE_H

#include "KAIVipAlgorithmGlobal.h"
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc/types_c.h>
#include <opencv2/imgproc/imgproc.hpp>

typedef struct tagKMarkImagePen
{
    int Thickness;
    uint Color; //
} KMarkImagePen;

typedef cv::Rect KRect;
typedef std::vector<cv::Rect> KRect1D;
typedef std::vector<KRect1D> KRect2D;
typedef cv::Point KPoint;
typedef cv::Point2f KPointf;

#ifdef _WIN32
KPoint KAIVIPALGORITHM_EXPORT centerRect(KRect &rect);               // { return KPoint(rect.x + (rect.width >> 1), rect.y + (rect.height >> 1)); }
void KAIVIPALGORITHM_EXPORT scaleRect(KRect &rect, float ratio);     // { rect.x *= ratio; rect.y *= ratio; rect.width *= ratio; rect.height *= ratio; }
void KAIVIPALGORITHM_EXPORT scaleRect(KRect &rect, int wr, int hr);  //{ rect.x *= wr; rect.y *= hr; rect.width *= wr; rect.height *= hr; }
void KAIVIPALGORITHM_EXPORT shiftRect(KRect &rect, const KPoint &p); //{ rect.x = p.x; rect.y = p.y; }
void KAIVIPALGORITHM_EXPORT shiftRect(KRect &rect, int wDis, int hDis);
#else
static KPoint KAIVIPALGORITHM_EXPORT centerRect(KRect &rect);                  // { return KPoint(rect.x + (rect.width >> 1), rect.y + (rect.height >> 1)); }
static void KAIVIPALGORITHM_EXPORT scaleRect(KRect &rect, float ratio);        // { rect.x *= ratio; rect.y *= ratio; rect.width *= ratio; rect.height *= ratio; }
static void KAIVIPALGORITHM_EXPORT scaleRect(KRect &rect, int wr, int hr);     //{ rect.x *= wr; rect.y *= hr; rect.width *= wr; rect.height *= hr; }
static void KAIVIPALGORITHM_EXPORT shiftRect(KRect &rect, const KPoint &p);    //{ rect.x = p.x; rect.y = p.y; }
static void KAIVIPALGORITHM_EXPORT shiftRect(KRect &rect, int wDis, int hDis); //{ rect.x += wDis; rect.y += hDis; }
#endif

//
template <typename T>
double distance(T x1, T y1, T x2, T y2)
{
    return std::sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
}

template <typename T>
double distance(cv::Point_<T> &p1, cv::Point_<T> &p2)
{
    return distance<T>(p1.x, p1.y, p2.x, p2.y);
}

//仿opencv的写法
template <typename T>
class KCircle_
{
public:
    KCircle_() : x(T()), y(T()), r(T()) {}
    KCircle_(T _x, T _y, T _r) : x(_x), y(_y), r(_r) {}

    cv::Point_<T> center() const { return cv::Point_<T>(x, y); }
    double area() { return K_PI * r * r; }

    bool isIn(const cv::Point_<T> &p)
    {
        return distance(p.x, p.y, x, y) < r;
    }

    T x;
    T y;
    T r;
};

typedef KCircle_<int> KCircle;
typedef KCircle_<float> KCirclef;

template <typename T>
class KLine_
{
public:
    KLine_() : x1(T()), x2(T()), y1(T()), y2(T()) {}
    KLine_(T _x1, T _y1, T _x2, T _y2) : x1(_x1), x2(_x2), y1(_y1), y2(_y2) {}
    cv::Point_<T> p1() const { return cv::Point_<T>(x1, y1); }
    cv::Point_<T> p2() const { return cv::Point_<T>(x2, y2); }
    double length() const { return distance(x1, y1, x2, y2); }

    T x1;
    T y1;
    T x2;
    T y2;
};
typedef KLine_<int> KLine;
typedef KLine_<float> KLinef;
typedef KLine_<double> KLined;

template <typename T>
class KRing_
{
public:
    KRing_() : x(T()), y(T()), r1(T()), r2(T()) {}
    KRing_(T _x, T _y, T _r1, T _r2) : x(_x), y(_y), r1(_r1), r2(_r2) {}

    cv::Point_<T> center() const { return cv::Point_<T>(x, y); }
    double area() { return K_PI * (r1 - r2) * (r1 - r2); }
    bool isIn(cv::Point_<int> &p)
    {
        double d = distance(p.x, p.y, x, y);
        return d > r1 && d < r2;
    }

    T x;
    T y;
    T r1; //inner
    T r2; //outer r2 > r1;
};
typedef KRing_<int> KRing;
typedef KRing_<float> KRingf;

class KContours
{
public:
    int maxArea(int *area = 0)
    {
        int contoursCount = count(), idx = -1;
        if (contoursCount <= 0)
            return 0;
        std::vector<int> areas(contoursCount, 0);
        for (KContour &c : coutours)
        {
            areas[++idx] = cv::contourArea(c);
        }

        idx = std::max_element(areas.begin(), areas.end()) - areas.begin();
        if (area)
            *area = areas[idx];

        return idx;
    }

    KRect1D rects()
    {
        KRect1D _rects(count());
        int idx = -1;
        for (KContour &c : coutours)
        {
            _rects[++idx] = cv::boundingRect(c);
        }
        return _rects;
    }
    int count() const { return coutours.size(); }

public:
    typedef std::vector<cv::Point> KContour;
    std::vector<KContour> coutours;
};

#include "ConnectedComponent.h"
class KRegions
{
public:
    int maxArea(int *area = 0)
    {
        if (regionsCount <= 0)
            return 0;
        int idx = 0;
        std::vector<int> areas(regionsCount, 0);
        for (int i = 0; i < regionsCount; ++i)
            areas[i] = (regions[i].x2 - regions[i].x1 + 1) * (regions[i].y2 - regions[i].y1 + 1);

        idx = std::max_element(areas.begin(), areas.end()) - areas.begin();
        if (area)
            *area = areas[idx];
        return idx;
    }

    KRect1D rects()
    {
        KRect1D _rects; //(regionsCount)
        _rects.reserve(regionsCount);
        cv::Rect tempRect;
        for (int i = 0; i < regionsCount; ++i)
        {
            if (regions[i].area > 0)
            {
                tempRect.x = regions[i].x1;
                tempRect.y = regions[i].y1;
                tempRect.height = regions[i].y2 - regions[i].y1 + 1;
                tempRect.width = regions[i].x2 - regions[i].x1 + 1;

                _rects.emplace_back(tempRect);
            }
        }
        return _rects;
    }

    int count() const { return regionsCount; }

public:
    int regionsCount = 0;
    region regions[MaxRegs];
};

#endif
