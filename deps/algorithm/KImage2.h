﻿#ifndef KIMAGE2_H
#define KIMAGE2_H

#include "KAIVipAlgorithmGlobal.h"
#include "KAlgorithmTypes.h"
#include "KImageMarker.h"
#include "KImageFrame.h"
#include "ConnectedComponent.h"

typedef enum tagKImageFlipFlag
{
    KFlip_UpDown = 0,    //上下镜像
    KFlip_LeftRight = 1, //左右镜像
    KFlip_All = 2
} KImageFlipFlag;

typedef enum tagKImageCrossRotateFlag
{
    KRotate_CLK_90 = 0,        //顺时针90
    KRotate_180 = 1,           //180
    KRotate_Counter_CLK_90 = 2 //逆时针90
} KImageCrossRotateFlag;

class KImage2;
class KImage2Private;
typedef std::vector<KImage2> KImage2s;

class KAIVIPALGORITHM_EXPORT KImage2
{
public:
    KImage2();
    KImage2(const std::string &filename);
    KImage2(const KImageFrame &frame);
    KImage2(const KImage2 &img);
    KImage2(cv::Mat &mat);
    KImage2(int width, int height, int type, void *buffer, int step = cv::Mat::AUTO_STEP);
    ~KImage2();

    void load(const std::string &filename);
    void load(int width, int height, unsigned *buffer);
    void load(int width, int height, float *buffer);

    //常用属性
    unsigned char *data() const;
    int width() const;
    int height() const;
    int channel() const;
    cv::Mat &src();
    KImage2 &zero();
    KPoint center() const;

    //常用数值化操作
    KImage2 &set(unsigned char val);
    KImage2 &flip(int flag);                       //镜像
    KImage2 &crossRotate(int flag);                //正交旋转，每90度旋转一次
    KImage2 &rotate(float angle);                  //旋转 弧度
    KImage2 &scale(float ratio);                   //等比例缩放
    KImage2 &resize(int w, int h);                 //任意尺寸
    KImage2 &resizeTo(KImage2 &dst, int w, int h); //自身不变化，将结果输出到另外一个image

    //切图
    KContours contours(); //找区域
    KRegions regions();
    KImage2 cropRectangle(const KRect &rect);     //切出来一个矩形
    KImage2 antiCropRectangle(const KRect &rect); //镂空一个矩形 下同理
    KImage2 cropCircle(const KCircle &c);
    KImage2 antiCropCircle(const KCircle &c);
    KImage2 cropRing(const KRing &ring);
    KImage2 antiCropRing(const KRing &ring);

    //绘图
    void drawCenterCross(KImageMarker *marker);
    void drawRectangle(KImageMarker *marker, const KRect &rect);
    void drawCircle(KImageMarker *marker, const KCircle &c);
    void drawCircle(KImageMarker *marker, const KPoint &p, int radius);
    void drawLine(KImageMarker *marker, const KLine &l);
    void drawRing(KImageMarker *marker, const KRing &ring);
    void drawRing(KImageMarker *marker, const KPoint &c, int r1, int r2);
    void drawText(KImageMarker *marker, const char *text, const KPoint &p, int fontSize);

    KImage2 &clone(const KImage2 &img);
    KImage2 &operator=(const cv::Mat &mat);
    KImage2 &operator=(const KImage2 &img);
    KImage2 &operator=(unsigned char val);

private:
    cv::Mat _src;
};
#endif
