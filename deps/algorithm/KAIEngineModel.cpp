﻿#pragma execution_character_set("utf-8")
#include "KAIEngineModel.h"
#include <QUuid>

class KSensorReleation : public KSmartParamRelation
{
public:
    KSensorReleation(KAIEngineModel *aimodel, KSmartParam *param) : KSmartParamRelation(param), mAiEngineModel(aimodel)
    {
    }

    int code() const
    {
        return SPRC_ValueChanged;
    }

    //业务逻辑
    void action(void *val)
    {
    }

public:
    KAIEngineModel *mAiEngineModel;
};

class KAIEngineModelPrivate
{
public:
    KAIEngineModelPrivate(KAIEngine *e) : _engine(e), _panel(0), _isEnable(true)
    {
        _sensor.setInfo(KSmartParamInfo{0, 1, "Sensor", "Sensor", "Sensor"});
        _sensor.setRange(0, 100);
        _sensor.setValue(50);
    }

public:
    bool _isEnable;
    KAIEngine *_engine;
    KAIEnginePanel *_panel;
    KSmartParamInt _sensor;
};
//////////////////////////////////////////////////////////////////////////
KAIEngineModel::KAIEngineModel(KAIEngine *e) : p(0)
{
    p = new KAIEngineModelPrivate(e);
}

KAIEngineModel::~KAIEngineModel()
{
    RELEASE_COMMON_PTR(p);
}

int KAIEngineModel::sensor()
{
    return *(int *)p->_sensor.value();
}

bool KAIEngineModel::enable()
{
    return p->_isEnable;
}

void KAIEngineModel::setEnable(bool en)
{
    if (p->_isEnable != en)
    {
        p->_isEnable = en;
        emit enableChanged(p->_isEnable);
    }
}

void KAIEngineModel::setSensor(int value)
{
    if (*(int *)p->_sensor.value() != value)
    {
        p->_sensor.setValue(value);
        emit sensorChanged(value);
    }
}

const KSmartParam &KAIEngineModel::sensorParam()
{
    return p->_sensor;
}

QString KAIEngineModel::path() const
{
    return QString::fromStdString(p->_engine->path());
}

QString KAIEngineModel::name() const
{
    return StdStr2QStr(p->_engine->name());
}

QString KAIEngineModel::id() const
{
    return StdStr2QStr(p->_engine->id());
}

QString KAIEngineModel::caption() const
{
    return StdStr2QStr(p->_engine->instruction());
}

KAIEngine *KAIEngineModel::engine() const
{
    return p->_engine;
}

KAIEnginePanel *KAIEngineModel::requestPanel()
{
    return p->_panel;
}

KAIEngineInferEnity *KAIEngineModel::createEnity()
{
    return createEnity(QUuid::createUuid().toString());
}

KAIEngineInferEnity *KAIEngineModel::createEnity(const QString &id)
{
    //return p->_engine->setPath(id.toStdString());
    return p->_engine->addEnity(id.toStdString());
}

int KAIEngineModel::load(const QString &path)
{
    std::string strPath = path.toStdString();
    int rlt = p->_engine->setPath(strPath);
    if (!rlt)
        emit pathChanged(path);
    return rlt;
}

void KAIEngineModel::setName(const QString &text)
{
    p->_engine->setName(text.toStdString());
    emit nameChanged(text);
}

void KAIEngineModel::setCaption(const QString &text)
{
    p->_engine->setInstruction(text.toStdString());
    emit captionChanged(text);
}

void KAIEngineModel::notifyCheckState(int cindex, bool en)
{
    emit checkedStateNotified(cindex, en);
}

void KAIEngineModel::notifyCheckRequest()
{
    emit checkRequestNotified();
}

void KAIEngineModel::uploadModel(int cindex)
{
    emit modelUpload(cindex);
}

void KAIEngineModel::applicateMode(int cindex)
{
    emit modelApplicated(cindex);
}

class KAIEngineManagerModelPrivate
{
public:
    KAIEngineManagerModelPrivate() : _aiManager(KAIEngineManager::singleInstance())
    {
        KAIEngine1D &&allEngines = _aiManager->engines();
        for (KAIEngine *e : allEngines)
        {
            _engineModels.insert(std::make_pair(e->id(), new KAIEngineModel(e)));
        }
    }

public:
    std::map<std::string, std::shared_ptr<KAIEngineModel>> _engineModels;
    std::map<std::string, std::string> _engineNameInfos;
    KAIEngineManager *_aiManager;
};

KDEFINITION_SINGLECLASS(KAIEngineModelManager)

KAIEngineModelManager::KAIEngineModelManager() : p(0)
{
    p = new KAIEngineManagerModelPrivate();
}

KAIEngineModelManager::~KAIEngineModelManager()
{
    RELEASE_COMMON_PTR(p);
}

bool KAIEngineModelManager::contains(const QString &modelName)
{
    return p->_engineNameInfos.count(QStr2StdStr(modelName));
}

bool KAIEngineModelManager::containsPath(const QString &path)
{
    return false;
}

KAIEngineManager *KAIEngineModelManager::aiManager()
{
    return p->_aiManager;
}

int KAIEngineModelManager::count() const
{
    return p->_aiManager->count();
}

KAIEngineModel *KAIEngineModelManager::add(KAIEngine *e)
{
    if (!p->_aiManager->add(e))
        return 0;

    KAIEngineModel *eM = new KAIEngineModel(e);
    connect(eM, &KAIEngineModel::enableChanged, [=](bool en) {
        emit engineEnableChanged(eM->path(), en);
    });
    connect(eM, &KAIEngineModel::sensorChanged, [=](int sensor) {
        emit engineSensorChanged(eM->path(), sensor);
    });

    //    std::vector<std::string> existIds;
    //    for (auto iter = p->_engineNameInfos.begin(); iter != p->_engineNameInfos.end(); ++iter)
    //    {
    //        if (eM->path().toStdString() == iter->second)
    //        {
    //            existIds.push_back(iter->first);
    //        }
    //    }

    //    for (std::string &id : existIds)
    //    {
    //        remove(QString::fromStdString(id));
    //    }

    p->_engineNameInfos.insert(std::make_pair(QStr2StdStr(eM->name()), QStr2StdStr(eM->path())));
    p->_engineModels.insert(std::make_pair(e->id(), eM));
    emit engineAdded(eM);
    return eM;
}

void KAIEngineModelManager::remove(const QString &id)
{
    std::string strId = id.toStdString();
    KAIEngine *e = p->_aiManager->engine(id.toStdString());
    if (e)
    {
        auto nite = p->_engineNameInfos.find(e->name());
        if (nite != p->_engineNameInfos.end())
            p->_engineNameInfos.erase(nite);

        emit engineRemovedName(e->name());
        emit engineRemovedPath(e->path());
    }

    if (!p->_aiManager->remove(id.toStdString()))
        return;

    auto ite = p->_engineModels.find(strId);
    if (ite == p->_engineModels.end())
        return;

    emit engineRemoved(ite->second.get());

    p->_engineModels.erase(ite);
}

std::vector<KAIEngineModel *> KAIEngineModelManager::engines() const
{
    std::vector<KAIEngineModel *> allEngines;
    auto ite = p->_engineModels.begin();

    for (; ite != p->_engineModels.end(); ++ite)
    {
        allEngines.push_back(ite->second.get());
    }
    return allEngines;
}
