#include "algorithm/KImage2.h"
#include "common/KDefines.h"
#include "algorithm/KAlgorithmShape.h"

KPoint centerRect(KRect& rect) 
{ 
	return KPoint(rect.x + (rect.width >> 1), rect.y + (rect.height >> 1));
}

void scaleRect(KRect& rect, float ratio)
{
	rect.x *= ratio; rect.y *= ratio; rect.width *= ratio; rect.height *= ratio; 
}

void scaleRect(KRect& rect, int wr, int hr)
{ 
	rect.x *= wr; 
	rect.y *= hr; 
	rect.width *= wr;
	rect.height *= hr; 
}

void shiftRect(KRect& rect, const KPoint& p) 
{ 
	rect.x = p.x; 
	rect.y = p.y; 
}

void shiftRect(KRect& rect, int wDis, int hDis) 
{ 
	rect.x += wDis;
	rect.y += hDis; 
}
