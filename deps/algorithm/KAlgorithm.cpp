﻿#include "algorithm/KSmartParam.h"
#include "algorithm/KAlgorithm.h"
#include "algorithm/KAlgorithmResultManager.h"
#include "common/KSTLTools.h"
#include <map>

class KAlgorithmPrivate
{
public:
    KAlgorithmPrivate() : _groupId(0)
    {
    }

public:
    int _groupId;
    KAlgorithmInfo _info;
    std::string _id;
    KSmartParam1D _inParams;
    KSmartParam1D _outParams;
    bool _sendTempDataState = false;

    std::map<int, bm_image *> _bmImages;
    //    bm_image *_bmImage = nullptr;

    KImageMarker _marker;

    std::map<std::string, std::shared_ptr<KAlgorithmModule>> _modules;
};

KAlgorithm::KAlgorithm() : p(0)
{
    p = new KAlgorithmPrivate();
}

KAlgorithm::~KAlgorithm()
{
    delete p;
}

void KAlgorithm::setGroupId(int groupid)
{
    p->_groupId = groupid;
}

int KAlgorithm::groupId()
{
    return p->_groupId;
}

void KAlgorithm::setId(const std::string &aid)
{
    p->_id = aid;
}

std::string KAlgorithm::id() const
{
    return p->_id;
}

void KAlgorithm::setInfo(const KAlgorithmInfo &info)
{
    p->_info = info;
}

const KAlgorithmInfo &KAlgorithm::info()
{
    return p->_info;
}

bm_image *KAlgorithm::getBmImage(int frameIndex) const
{
    if (p->_bmImages.count(frameIndex) > 0)
        return p->_bmImages[frameIndex];
    return nullptr;
}

void KAlgorithm::setBmImagePtr(bm_image *image, int frameIndex)
{
    p->_bmImages[frameIndex] = image;
}

void KAlgorithm::clearBmImagePtrs()
{
    if (!p->_bmImages.empty())
        p->_bmImages.clear();
}

//#include <iostream>
void KAlgorithm::setSendTempDatasState(bool send)
{
    p->_sendTempDataState = send;
    //    std::cout << "camIndex=" << groupId() << ", sendTempState=" << send << std::endl;
}

void KAlgorithm::setTemplateDatas(const void *data)
{
    // do nothing here
}

bool KAlgorithm::sendTempDatasState() const
{
    return p->_sendTempDataState;
}

int KAlgorithm::moduleCount()
{
    return p->_modules.size();
}

void KAlgorithm::addModule(KAlgorithmModule *f)
{
    if (!KSTLTools::insert_in_spmap(p->_modules, f->info().Id, f))
        return;

    //    const KAlgorithmModuleInfo &moduleInfo = f->info();
    //    KAlgorithmResultManager::singleInstance()->registerResult(
    //        {moduleInfo.Code, moduleInfo.Name, moduleInfo.Instruction});
}

KAlgorithmModule *KAlgorithm::module(const std::string &id)
{
    KAlgorithmModule1D ms = modules();
    for (KAlgorithmModule *m : ms)
    {
        if (m->id() == id)
            return m;
    }
    return 0;

    //	return KSTLTools::find_in_spmap(p->_modules, id);
}

KAlgorithmModule1D KAlgorithm::modules() const
{
    return KSTLTools::spmap_to_vec(p->_modules);
}

KSmartParam1D KAlgorithm::inParams() const
{
    return p->_inParams;
}

KSmartParam1D KAlgorithm::outParams() const
{
    return p->_outParams;
}

KImageMarker &KAlgorithm::imageMarker()
{
    return p->_marker;
}

void KAlgorithm::refreshAllParams()
{
    KAlgorithmModule1D allModule = modules();
    for (KAlgorithmModule *m : allModule)
    {
        if (m)
        {
            KSmartParam1D allParams = m->inParams();
            for (KSmartParam *para : allParams)
            {
                if (para)
                    para->refresh();
            }
        }
    }
}

void KAlgorithm::addInParams(KSmartParam *param)
{
    p->_inParams.push_back(param);
}

void KAlgorithm::addOutParams(KSmartParam *param)
{
    p->_outParams.push_back(param);
}
