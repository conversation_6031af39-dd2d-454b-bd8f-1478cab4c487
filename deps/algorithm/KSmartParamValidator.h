﻿#ifndef KSMARTPARAMVALIDATOR_H
#define KSMARTPARAMVALIDATOR_H
#include "KAIVipAlgorithmGlobal.h"

//参数验证器
class KSmartParam;
class KAIVIPALGORITHM_EXPORT KSmartParamValidator
{
public:
	KSmartParamValidator(KSmartParam* param = 0);
	virtual ~KSmartParamValidator();

	KSmartParam *param();
	void setParam(KSmartParam *p);

	virtual bool valiate(void *val) = 0;

protected:
	KSmartParam *_param;
};
#endif