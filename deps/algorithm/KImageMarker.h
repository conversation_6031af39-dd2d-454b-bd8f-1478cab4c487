﻿#ifndef KIMAGEMAKER_H
#define KIMAGEMAKER_H

#include "KAIVipAlgorithmGlobal.h"
#include "KAlgorithmTypes.h"
#include "KSmartParam.h"
#include "KAlgorithmShape.h"

//#include <opencv2/imgproc/imgproc.hpp>
//#include <opencv2/core/core.hpp>
//#include <opencv2/highgui/highgui.hpp>
//#include <opencv2/imgproc/types_c.h>

class KImageMarkerPrivate;
class KAIVIPALGORITHM_EXPORT KImageMarker
{
public:
    KImageMarker();
    ~KImageMarker();

    void setImage(cv::Mat *src);
    cv::Mat *image();
    const cv::Mat *image() const;

    void setWidth(int w);
    int width() const;
    void setColor(KColor c);
    void setFontSize(cv::Scalar s);
    KColor color() const;

    KImageMarker &drawCenterCross();                                     //TODO 中心画个十字叉
    KImageMarker &drawText(const char *text, int x, int y, int size);    //TODO 写文字
    KImageMarker &drawText(const char *text, const KPoint &p, int size); //TODO 写文字
    KImageMarker &drawRect(int x1, int y1, int x2, int y2);
    KImageMarker &drawRect(const KRect &rect);
    KImageMarker &drawCircle(int x, int y, int r);
    KImageMarker &drawCircle(const KCircle &circle);
    KImageMarker &drawLine(int x1, int y1, int x2, int y2);
    KImageMarker &drawLine(cv::Point &p1, cv::Point &p2);
    KImageMarker &drawLine(const KLine &line);
    KImageMarker &drawRings(int x, int y, int r1, int r2);
    KImageMarker &drawRings(const KRing &ring);
    KImageMarker &drawContour(KContours &c, int idx = -1);

    void setCurFrameIndex(int frameIndex);
    void addText(const char *text, KPoint point, KColor color, int size = 40);
    void addRect(const KRect &rect, KColor color, int lineWidth = 2);
    void addCircle(const int x, const int y, const int r, KColor color, int lineWidth = 2);
    void addLine(const cv::Point &p1, const cv::Point &p2, KColor color, int lineWidth = 2);
    void drawImage();
    void clearDrwaBuffer();
    void setDrawEnable(bool enable);

private:
    KImageMarkerPrivate *p;
};

class KAIVIPALGORITHM_EXPORT KSmartParamMarker : public KSmartParam
{
public:
    KSmartParamMarker(int width = 4, KColor c = KCV_GREEN);

public:
    virtual bool setValue(void *val) override;
    virtual int type() const override;
    virtual void *value() override;

    void setWidth(int val);
    void setColor(KColor c);

    int width() const;
    KColor color() const;

    KImageMarker &marker();
    const KImageMarker &marker() const;

private:
    KImageMarker _maker;
};

#endif
