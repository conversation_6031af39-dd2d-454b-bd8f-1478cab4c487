﻿#ifndef KALGORITHMTYPES_H
#define KALGORITHMTYPES_H

#include "KAIVipAlgorithmGlobal.h"

#define K_PI 3.1415926
#define K_PI_C2 1.5707963
#define K_PI_C4 0.7853982
#define K_PI_X2 6.2831852

#define K_1DEGREE 0.017453 //一度多少弧度
#define K_1RADIAN 57.3     //一弧度多少度

typedef unsigned char uchar;
typedef unsigned int uint;
typedef unsigned long ulong;

typedef enum tagKSmartParamRelationCode
{
    SPRC_None = 0x0,
    SPRC_Enable = 0x1,

    SPRC_ValueChanged = 0x3,
    SPRC_OverMax = 0x4,
    SPRC_OverMin = 0x5
} KSmartParamRelationCode;

typedef enum tagKSmartParamType
{
    SPT_None = 0x0,
    SPT_Bool = 0x1,
    SPT_Int = 0x2,
    SPT_Double = 0x3,
    SPT_String = 0x4,
    SPT_AI = 0x5,
    SPT_Marker = 0x6
} KSmartParamType;

/*
typedef enum tagKAlgorithmResultCode
{
    KCV_OK						= 0x0,
    KCV_NG_Test					= 0x1,
    KCV_NG_Placeholder,
    KCV_NG_NoMemory,
    KCV_NG_DataError,
    KCV_NG_NullPointer,
    KCV_NG_FileNoExit,
    KCV_NG_StdException,
    KCV_NG_SysErrorEnd,

    KCV_NG_FrameCountLess,
    KCV_NG_FrameCountMore,
    KCV_NG_FrameWidth,
    KCV_NG_FrameHeight,

    KCV_FileNotExit,
    KCV_RuntimeError,
    KCV_ContextError,
    KCV_MemeoryError,
    KCV_ParamError,
    KCV_Exception,

    //AI -- Error
    KCV_EngineNotExit,
    KCV_EngineError,
    KCV_AIResultDimNotMatched,
    KCV_EngineDisabled,
    KCV_EngineInferException,

    //用户自定义区域
    KCV_NG_User					= 0x8000
}KAlgorithmResultCode;
*/
typedef enum tagKAlgorithmExceptionCode
{
    KEC_Ok = 0x0,
    KEC_ParamExit,
    KEC_ParamGroupExit,
    KEC_ParamGroupNotExit
} KAlgorithmExceptionCode;

#define KSMARTPARAM_SAVE 0x00000001
#define KSMARTPARAM_UNVISIBLE 0x00000002
#define KSMARTPARAM_LEVEL1 0x00001000
#define KSMARTPARAM_LEVEL2 0x00002000
#define KSMARTPARAM_LEVEL3 0x00003000

typedef struct tagKSmartParamInfo
{
    int Index;         //参数索引
    unsigned int Flag; //参数等级
    std::string Id;
    std::string Name;
    std::string Instruction; //参数描述，界面显示，方便用户调试
} KSmartParamInfo;

typedef struct tagKSmartParamGroupInfo
{
    int Id;
    std::string Name;
    std::string Instruction; //参数描述，界面显示，方便用户调试
} KSmartParamGroupInfo;

typedef struct tagKAlgorithmInfo
{
    std::string Name;  //dll`s name
    std::string Title; //include english, chinese or other lang
    std::string Instruction;
    std::string Author;  //eng
    std::string Version; //
} KAlgorithmInfo;

typedef struct tagKAlgorithmResult
{
    bool IsOk;
    int ResultCode;
    double Period;
    int Reserved[4];
} KAlgorithmResult;

typedef struct tagKAIEngineIODimension
{
    int Dimension;
    int Channel;
    int Width;
    int Height;
    int Reserved[6];
} KAIEngineIODimension;

typedef struct tagKAIEngineInfo
{
    // 	int DownSample; //降采样数值
    // 	int InWidth; //输入模型的高度
    // 	int InHeight; //输出模型的宽度
    // 	int OutWidth;
    // 	int OutHeight;
    // 	int SectionDimension; //数据维度
    // 	int SectionSize;
    // 	int ResultSize; //数据总长度 = with * height / (DownSample * DownSample) * SectionDimension
    std::vector<KAIEngineIODimension> InputDim;  //输入维度
    std::vector<KAIEngineIODimension> OutputDim; //输出维度
} KAIEngineInfo;

typedef int KColor;

#define KCV_R(c) (((c)&0xFF000000) >> 24)
#define KCV_G(c) (((c)&0x00FF0000) >> 16)
#define KCV_B(c) (((c)&0x0000FF00) >> 8)
#define KCV_RED 0xFF0000
#define KCV_GREEN 0x00FF00
#define KCV_BLUE 0x0000FF

#endif
