#ifndef  KAIVIPFRAMEWORKGLOBAL_H
#define  KAIVIPFRAMEWORKGLOBAL_H

#include <QtCore/qglobal.h>

#ifndef BUILD_STATIC
# if defined(KAIVIPFRAMEWORK_LIB)
#  define KAIVIPFRAMEWORK_EXPORT Q_DECL_EXPORT
# else
#  define KAIVIPFRAMEWORK_EXPORT Q_DECL_IMPORT
# endif
#else
# define KAIVIPFRAMEWORK_EXPORT
#endif

#define KPLUGIN_EXPORT			KAIVIPFRAMEWORK_EXPORT
#define KAIVIPGUI_EXPORT		KAIVIPFRAMEWORK_EXPORT
#define KAIVIPCORE_EXPORT		KAIVIPFRAMEWORK_EXPORT
#define KSTATV2_EXPORT                  KAIVIPFRAMEWORK_EXPORT

#ifdef _WIN32
#include <qt_windows.h>
#endif

#include "KDefines.h"
#include "KTypes.h"
#include "KEnvironmentVariant.h"

#include <QString>
#include <QStringList>

#endif

