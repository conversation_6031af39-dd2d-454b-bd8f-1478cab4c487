﻿#ifndef KENVIROMENTVARIANT_H
#define KENVIROMENTVARIANT_H

#include "KAIVipFrameworkGlobal.h"

class KAIVIPFRAMEWORK_EXPORT KEnviromentVariants
{
public:
    static KEnviromentVariants *singleInstance();
    static KEnviromentVariants& singleInstanceRef();

public:
    unsigned int MaxCameraCount;
    unsigned int MaxGroupCount;
    unsigned int MaxBufferCount;
    unsigned int BufferShiftFlag;
    unsigned int BufferIndexFlag;

private:
    explicit KEnviromentVariants();
};

typedef KEnviromentVariants KEV;

#endif
