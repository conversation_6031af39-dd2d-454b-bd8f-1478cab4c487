﻿#pragma once

#ifdef _WIN32
#ifdef KASSEMBLEALGORITHM_LIB
#define KASSEMBLEALGORITHM_EXPORT __declspec(dllexport)
#else
#define KASSEMBLEALGORITHM_IMPORT __declspec(dllimport)
#endif
#else
#define KASSEMBLEALGORITHM_EXPORT __attribute__((visibility("default")))
#endif

#define Id_Key "id"
#define Name_Key "name"
#define Comment_Key "comment"
#define Type_Key "type"
#define ICon_Key "icon" // 图标的名称，路径都是同一放在一个路径里，根据这个图标路径来自动加载图标
#define Config_Key "config"
#define Value_Key "value"
#define Class_Key "class"
#define Object_Key "object"
#define Condition_Key "condition"

// 算法信息
#define AsmbAlgoInfo_Key "asmbAlgoInfo"
#define Title_Key "title"
#define Instruction_Key "instruction"
#define Author_Key "author"
#define Version_Key "version"

// 入口模块和主链
#define EntranceModule_Key "entranceModule"
#define MainChain_Key "mainChain"

// 各大模块
#define AsmbModule_Key "asmbModule"
#define AsmbChain_Key "asmbChain"
#define AsmbCondition_Key "asmbCondition"
#define ChainObject_Key "chainObject"
#define BindParam_Key "bindParam"
#define SetParam_Key "setParam"
#define ObjectLink_Key "objectLink"

// module 模块

// chain 模块
#define ForceRun_Key "forceRun"

// 参数模块
#define Parameter_Key "parameter"
#define Result_Key "result"

// switch 模块
#define DefaultObject_Key "defaultObject"
#define CondObject_Key "condObject"

// ifelse 模块
#define TrueObject_Key "trueObject"
#define FalseObject_Key "falseObject"

// multi ifelse 模块 前面已经有全部字段定义

#define ModuleType_Key "moduleType" // 模块类别，常规模块、跳转模块、ifelse 模块等

// condiiton 模块
#define CondType_Key "condType"
#define ConditionId_Key "conditionId"
#define Binding_Key "binding"
#define SrcBinding_Key "srcBinding"
#define DstBinding_Key "dstBinding"

// compare condiiton
#define CompareType_Key "compareType"
#define SrcDataModuleId_Key "srcDataModuleId"
#define SrcDataId_Key "srcDataId"
#define DstDataModuleId_Key "dstDataModuleId"
#define DstDataId_Key "dstDataId"

// logic condiiton // switch condiiton
#define LogicType_Key "logicType"
#define LogicData_Key "logicData"

#define DataModuleId_Key "dataModuleId"
#define DataId_Key "dataId"

#define SrcCondition_Key "srcCondition"
#define DstCondition_Key "dstCondition"

// 设置参数范围
#define MinData_Key "min"
#define MaxData_Key "max"
#define DefaultData_Key "default"
#define StepData_Key "step"
#define AiPath_Key "aiPath"

// 绑定参数
#define SrcParamId_Key "srcParamId"
#define DstParamId_Key "dstParamId"
#define SrcModuleId_Key "srcModuleId"
#define DstModuleId_Key "dstModuleId"
#define ToBindParam_Key "toBindPrames"

// 链接chain的object
#define LinkChain_Key "chain"
#define LinkObjects_Key "objects"
#define LinkSrc_Key "src"
#define LinkDst_Key "dst"

#define OkObjId_Key "okObjId"
#define NgObjId_Key "ngObjId"

//#ifdef AutoLoadEnity
//#include "KControllerAIEngineManager.h"
//#define EnityPath_Key "path"
//#endif

// 数据类型枚举
enum EDataType
{
    KData_Null, // 不是一个类型
    KData_Int,
    KData_Bool,
    KData_Float,
    KData_Double,
    KData_Char,
    KData_String,

    KData_SmartAi = 15,

    KData_CharPtr = 25,
    KData_VoidPtr,

    KData_Image = 50,
    KData_Image2,    // KImage2
    KData_Image2Vec, // KImage2 vector
    KData_Frame,     // KFrame
    KData_FramePtr,
    KData_Frame1D, // 55
    KData_Frame1DPtr,
    KData_Frame2D,
    KData_Frame2DPtr,
    KData_BmImagePtr,
    KData_AiInferDataPtr,

    KData_Rect, // = 61
    KData_Rect1D,

    KData_User = 100, // 用户自定义
};

enum EConditionType
{
    CompareCond,
    LogicCond,
    SwitchCond,
    MultiCond, // 组合条件

    ConditionCount
};

enum ECompareType
{
    Compare_equal,
    Compare_bigger,
    Compare_smaller,
    Compare_biggerEqual,
    Compare_smallerEqual,

    compareCount
};
enum ELogicType
{
    Logic_and,
    Logic_or,
    Logic_not,
    Logic_NotAType,
    LogicCount
};

enum ECondtionResult
{
    Cond_error = -1,
    Cond_false = false,
    Cond_true = true,
};

enum EModuleType
{
    NormalModule,
    ParamModule,
    SwitchModule,
    MultiIfElseModule,

};
