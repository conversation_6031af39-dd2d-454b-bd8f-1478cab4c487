﻿#ifndef KSTLTOOS_H
#define KSTLTOOS_H

//对于STL 模板库的一些操作
//智能指针的map 转为 vector
#include <vector>
#include <map>
#include <memory>
#include <string>
#include <algorithm>

namespace KSTLTools
{
	template<typename Key, typename Value>
	std::vector<Value*> spmap_to_vec(std::map<Key, std::shared_ptr<Value>>& dict)
	{
		std::vector<Value*> items(dict.size(), 0);
		typename std::map<Key, std::shared_ptr<Value>>::iterator ite = dict.begin();
		for (int i = 0; ite != dict.end(); ++i, ++ite)
		{
			items[i] = ite->second.get();
		}

		return items;
	}

	template<typename Key, typename Value>
	Value* find_in_spmap(std::map<Key, std::shared_ptr<Value>>& dict, const Key& key)
	{
		typename std::map<Key, std::shared_ptr<Value>>::iterator ite = dict.find(key);
		if (ite == dict.end())
			return 0;
		return ite->second.get();
	}

	template<typename Key, typename Value>
	bool insert_in_spmap(std::map<Key, std::shared_ptr<Value>>& dict, const Key& key, Value* val)
	{
		typename std::map<Key, std::shared_ptr<Value>>::iterator ite = dict.find(key);
		if (ite != dict.end())
			return false;
		
		dict.insert(std::make_pair(key, std::shared_ptr<Value>(val)));
		return true;
	}

	template<typename Key, typename Value>
	bool remove_from_spmap(std::map<Key, std::shared_ptr<Value>>& dict, const Key& key)
	{
		typename std::map<Key, std::shared_ptr<Value>>::iterator ite = dict.find(key);
		if (ite == dict.end())
			return false;
//                ite->second.reset();
		dict.erase(ite);
		return true;
	}

	template<typename Key, typename Value>
	Value find_in_map(std::map<Key, Value>& dict, const Key& key)
	{
		typename std::map<Key, Value>::iterator ite = dict.find(key);
		if (ite == dict.end())
			return Value();
		return ite->second;
	}

	template<typename Key, typename Value>
	bool insert_in_map(std::map<Key, Value>& dict, const Key& key, const Value& val)
	{
		typename std::map<Key, Value>::iterator ite = dict.find(key);
		if (ite != dict.end())
			return false;
		dict.insert(std::make_pair(key, val));
	}

	template<typename Key, typename Value>
	bool set_in_map(std::map<Key, Value>& dict, const Key& key, const Value& val)
	{
		typename std::map<Key, Value>::iterator ite = dict.find(key);
		if (ite == dict.end())
			return false;
		ite->second = val;
		return true;
	}

	template<typename Type>
	void erase_in_vec(std::vector<Type*>& container)
	{
		for (Type* val : container)
		{
			delete val;
			val = 0;
		}
	}

	template<typename Type>
	bool find_int_vec(std::vector<Type>& container, const Type& val)
	{
		return std::find(container.begin(), container.end(), val) != container.end();
	}
}
#endif
