﻿/****************************************************************************
**
** 该文件整合了 vis系统中一些常用的控件窗体。
** 作者：黄炜
** $K_END_LICENSE$
**
****************************************************************************/
#ifndef KQTGUIHEADERS_H
#define KQTGUIHEADERS_H

#include <QButtonGroup>
#include <QWidget>
#include <QFrame>
#include <QDialog>
#include <QPushButton>
#include <QRadioButton>
#include <QToolButton>
#include <QCheckBox>
#include <QCommandLinkButton>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QGroupBox>
#include <QTabBar>
#include <QTableWidget>
#include <QTableView>
#include <QTableWidgetItem>
#include <QHeaderView>
#include <QMessageBox>
#include <QScrollArea>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QStackedLayout>
#include <QFormLayout>
#include <QSpinBox>
#include <QGraphicsItem>
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QGraphicsWidget>
#include <QMainWindow>
#include <QProgressBar>

#include <QPainter>
#include <QImage>
#include <QPixmap>
#include <QIcon>

//Dialogs
#include <QFileDialog>
#include <QColorDialog>
#include <QProgressDialog>

typedef void (QButtonGroup::*QBGButtonClickedFunc)(int);
#endif
