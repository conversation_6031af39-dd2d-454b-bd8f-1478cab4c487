﻿#ifndef KTYPES_H
#define KTYPES_H

#include <string>
#include <vector>

typedef unsigned char uchar;
typedef unsigned int uint;
typedef unsigned short ushort;

typedef std::vector<char> KBytes;
typedef std::vector<uchar> KUBytes;

typedef std::vector<int> KInt1D;
typedef std::vector<double> KDouble1D;

typedef struct tagKIPModuleInfo
{
    std::string ModuleNumb;  //dll`s numb;
    std::string ModuleName;  //dll`s name
    std::string ModuleTitle; //include english, chinese or other lang
    std::string ModuleInstr;
    std::string Author;  //eng
    std::string Version; //
} KIPModuleInfo;

typedef struct tagKIPModuleResult
{
    bool IsOk;
    int ResultCode;
    double Period;
    int Reserved[4];
} KIPModuleResult;

typedef struct tagKIPModuleParamGroup
{
    int GroupId;
    std::string GroupName;
    std::string GroupInstr;
} KIPModuleParamGroup;

typedef struct tagKIPModuleResultInfo
{
    int ResultCode;
    std::string ResultText;  //err code
    std::string ResultInstr; //code instruction
} KIPModuleResultInfo;

class KMissionResult
{
public:
    bool IsOk;
    int ResultCode;
    int Period;
    int Reserved1;
    int FrameId;
};

typedef std::vector<KIPModuleResultInfo> KIPModuleResultInfo1D;

typedef enum tagKSystemState
{
    StopState = 0,
    RunningState = 1,
    AdjustState = 2 | RunningState,
    DetectState = 4 | RunningState,
    SampleState = 8 | DetectState,
    RestoreSystem = 10 | RunningState,
    LockState = 16 | RunningState,
    PauseState = 32 | RunningState,
    ResumeState = 64 | RunningState,
    StopCAlgoThread = 128 | RunningState
} KSystemState;

//系统消息等级
typedef enum tagKMessageLevel
{
    MSG_COMMON = 0,
    MSG_INFORMATION,
    MSG_WARNING,
    MSG_ERROR
} KMessageLevel;

enum KDispachCommand
{
    DC_ResetStatis = 0,
    DC_ResetIoCount,

    DC_RefreshAIEngine,
    DC_UpdateAIEngineName,
    DC_GenerateAIModule,
    DC_RemoveAIEngine,
    DC_RemoveAIModule,

    DC_SpeedStatisData,

    DC_LackOfSample,
    DC_CameraError,

    DC_SendDownMcCfgFile = 1000 + 1,
    DC_SendAiModelFile,
    DC_DeleteDownMcFile,

};

//任务的结果标志
typedef enum tagKMissionResultResetMode
{
    KMRM_AllOk = 1,
    KMRM_AllNg = 2,
    KMRM_Normal = 4
} KMissionResultResetMode;

//任务图像存储的方式
typedef enum tagKControllerImageSaveMode
{
    KCISF_None = 0,
    KCISF_Ok = 1,
    KCISF_Ng = 2,
    KCISF_Src = KCISF_Ok | KCISF_Ng,
    KCISF_Mark_Ok = 4,
    KCISF_Mark_Ng = 8
} KControllerImageSaveMode;

//任务图像显示的方式
typedef enum tagKControllerImageDisplayMode
{
    KCIDM_None = 0,
    KCIDM_Ok = 1,
    KCIDM_Ng = 2,
    KCIDM_All = KCIDM_Ok | KCIDM_Ng,
    KCIDM_Specify = 4 //指定某个类型显示
} KControllerImageDisplayMode;

//任务存储图像的类型
typedef enum tagKImageSaveFormat
{
    KISF_BMP = 0,
    KISF_JPG = 1,
    KISF_PNG = 2,
} KImageSaveFormat;

//图像数据的格式
typedef enum tagKImageDataFormat
{
    KIDF_Mono = 0,
    KIDF_RGB888 = 1,
    KIDF_BGR888 = 2
} KImageDataFormat;
#endif
