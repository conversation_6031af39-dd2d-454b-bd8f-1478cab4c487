﻿#ifndef KDEFINES_HPP
#define KDEFINES_HPP

//信号量以及原子操作的跨平台定义
#ifdef _WIN32
#include <Windows.h>
#include <memory>
typedef HANDLE KSemaphoreHandle;

#define KCreateSemaphore(sem)                   \
    {                                           \
        if (sem)                                \
        {                                       \
            CloseHandle(sem);                   \
            sem = 0;                            \
        }                                       \
        sem = CreateSemaphore(0, 0, 0xFFFF, 0); \
    }
#define KReleaseSemaphore(sem) ReleaseSemaphore(sem, 1, 0)
#define KDestroySemaphore(sem) CloseHandle(sem)
#define KWaitForSemaphore(sem) WaitForSingleObject(sem, INFINITE)
#define KWaitForSemaphoreT(sem, t) WaitForSingleObject(sem, t)
#define KSEM_WAIT_OK WAIT_OBJECT_0
#define KSEM_WAIT_TIMEOUT WAIT_TIMEOUT

#define KSync_Atomic_Add(src, val) InterlockedAdd(&src, val)
#define KSync_Atomic_Increment(src) InterlockedIncrement(&src)
#define KSync_Atomic_Exchange(src, dst) InterlockedExchange(&src, dst)
#define KSync_Atomic_Or(val1, val2) InterlockedOr(&val1, val2)
#define KSync_Atomic_And(val1, val2) InterlockedAnd(&val1, val2)

#else
#include <memory>
#include <semaphore.h>
#include <unistd.h>

typedef sem_t *KSemaphoreHandle;

#define MillSecondsUnit 1000000

static int semwait_timeout(sem_t *sem, int msc)
{
    struct timespec tsc;
    struct timespec out_time;
    if (clock_gettime(CLOCK_REALTIME, &tsc) < 0)
        return -1;
    out_time.tv_sec = tsc.tv_sec + msc / 1000;
    out_time.tv_nsec = tsc.tv_nsec + (msc % 1000) * MillSecondsUnit;
    return sem_timedwait(sem, &out_time);
}

#define KCreateSemaphore(sem) \
    {                         \
        if (sem)              \
            sem_close(sem);   \
        sem = new sem_t();    \
        sem_init(sem, 0, 0);  \
    }
#define KReleaseSemaphore(sem) sem_post(sem)
#define KIncrementSemaphore(sem) sem_post(sem)
#define KDestroySemaphore(sem) \
    {                          \
        if (sem)               \
            sem_close(sem);    \
    }
#define KWaitForSemaphore(sem) sem_wait(sem)
#define KWaitForSemaphoreT(sem, t) semwait_timeout(sem, t)

#define KSEM_WAIT_OK 0
#define KSEM_WAIT_TIMEOUT -1

#define KSync_Atomic_Add(src, val) __sync_add_and_fetch(&src, val)
#define KSync_Atomic_Increment(src) __sync_add_and_fetch(&src, 1)
#define KSync_Atomic_Exchange(src, dst) __sync_lock_test_and_set(&src, dst)
#define KSync_Atomic_Or(val1, val2) __sync_fetch_and_or(&val1, val2)
#define KSync_Atomic_And(val1, val2) __sync_fetch_and_and(&val1, val2)

#endif

//释放普通指针
#define RELEASE_COMMON_PTR(ptr) \
    {                           \
        if (ptr)                \
        {                       \
            delete ptr;         \
            ptr = 0;            \
        }                       \
    }

//释放数组
#define RELEASE_COMMON_ARRAY(ptr) \
    {                             \
        if (ptr)                  \
        {                         \
            delete[](ptr);        \
            (ptr) = 0;            \
        }                         \
    }

//创建类型为T的指针，将地址赋给ptr
#define CREATE_COMMON_PTR(ptr, t) \
    {                             \
        RELEASE_COMMON_PTR(ptr);  \
        ptr = new t();            \
    }

//创建类型为T，长度为l的数组，将地址赋给ptrs，
#define CREATE_COMMON_ARRAY(ptrs, t, l) \
    {                                   \
        RELEASE_COMMON_ARRAY(ptrs);     \
        ptrs = new t[(l)];              \
        memset(ptrs, 0, sizeof(t) * l); \
    }

//初始化数组为0
#define ZERO_MEMORY(ptr, size)  \
    {                           \
        memset(ptr, 0, (size)); \
    }

//创建类型为T，长度为l的数组，将地址赋给ptrs，
#define CREATE_COMMON_ARRAY_ZEROINIT(ptrs, t, l) \
    {                                            \
        RELEASE_COMMON_ARRAY(ptrs);              \
        ptrs = new t[(l)];                       \
        ZERO_MEMORY(ptrs, sizeof(t) * (l));      \
    }

//将标准map转化为vector容器。
#define STDMAP2STDVEC(_map, _vec, T)   \
    T::iterator ite = (_map).begin();  \
    while (ite != (_map).end())        \
    {                                  \
        (_vec).push_back(ite->second); \
        ++ite;                         \
    }

//将标准map转化为vector容器。
#define STDMAP2STDVEC_PTR(_map, _vec, T) \
    T::iterator ite = (_map).begin();    \
    while (ite != (_map).end())          \
    {                                    \
        (_vec).push_back(&ite->second);  \
        ++ite;                           \
    }

//返回值占位符
#define KDFLT_MODULE_RTNVAL_PLACEHOLDER 1

#define KTOOL_CHECK_ULMT(_val, _max) ((_val) < (_max))
#define KTOOL_CHECK_LLMT(_min, _val) ((_val) > (_min))
#define KTOOL_CHECK_RANGE(_min, _val, _max) ((_val) > (_min) && (_val) < (_max))

#define KTOOL_CHECK_ULMT_A(_val, _max) ((_val) <= (_max))
#define KTOOL_CHECK_LLMT_A(_min, _val) ((_val) >= (_min))
#define KTOOL_CHECK_RANGE_A(_min, _val, _max) ((_val) >= (_min) && (_val) <= (_max))

#define KTOOL_POINT2_DISTANCE(_x1, _y1, _x2, _y2) (sqrt(((_x1) - (_x2)) * ((_x1) - (_x2)) + ((_y1) - (_y2)) * ((_y1) - (_y2))))

#define StdStr2QStr(str) QString::fromStdString(str)
#define QStr2StdStr(str) str.toLocal8Bit().toStdString()

#define QDataTimeCommonFormat QString("yyyy-MM-dd hh:mm:ss")
#define QDataTimeTextFormat QString("yyyyMMdd_hhmmss_zzz")

#define StdStringTransWrapper(str) QObject::tr(str).toStdString()

//常用的类声明
#define KDECLARE_SINGLECLASS(className) \
public:                                 \
    static className *singleInstance();

#define KDEFINITION_SINGLECLASS(className)                                          \
    className *className::singleInstance()                                          \
    {                                                                               \
        static std::shared_ptr<className> instance = std::make_shared<className>(); \
        return instance.get();                                                      \
    }

#endif
