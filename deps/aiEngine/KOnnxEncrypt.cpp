﻿#include "KOnnxEncrypt.h"
#include "KMd5.h"

#include <fstream>
#include <numeric>
#include <vector>
#include <map>
#include <string>
#include <string.h>
#include <memory>
#include <ctime>
#include <iostream>

#ifdef KDEBUG
#include <iostream>
#endif

using std::vector;

#define KDFLT_SEGMENT_LEN 256
#define KDFLT_MD5_LEN 32

#define KDFLT_ENCRYPT_HFMT_LEN 4     // knef
#define KDFLT_ENCRYPT_HEADER_LEN 256 //
#define KDFLT_ENCRYPT_MD5_LEN 512

#define KDFLT_ENCRYPT_MUL 0

char Base64PlaceholderChar = '@';

class KKeyTable
{
public:
    static KKeyTable *singleInstance()
    {
        static std::shared_ptr<KKeyTable> instance(new KKeyTable());
        return instance.get();
    }

    std::vector<char> &randomByteTable()
    {
        return mRandomByteTable;
    }

    std::vector<char> &byteToBase64Table()
    {
        return mByteToBase64Table;
    }

    std::map<char, char> &base64ToByteTable()
    {
        return mBase64ToByteTable;
    }

    std::vector<char> &createRandomBytes(std::vector<char> &src, int length)
    {
        if (src.size() < length)
            src.resize(length);

        for (int i = 0; i < length; ++i)
        {
            src[i] = mRandomByteTable[rand() & 63];
        }
        return src;
    }

private:
    explicit KKeyTable()
    {
        srand(time(0));
        mRandomByteTable = mByteToBase64Table =
            {
                'p',
                'O',
                '*',
                'W',
                'b',
                'R',
                'y',
                '0',
                't',
                '6',
                'h',
                'K',
                'u',
                'E',
                'w',
                '3',
                'B',
                'x',
                'I',
                'm',
                'o',
                'j',
                'g',
                'C',
                'D',
                'd',
                'a',
                '7',
                '&',
                'f',
                'J',
                'z',
                'Q',
                'U',
                'Z',
                'q',
                'e',
                'r',
                'F',
                'l',
                'k',
                'L',
                '8',
                'N',
                'i',
                'T',
                'Y',
                '5',
                'S',
                '9',
                'v',
                'V',
                'X',
                '1',
                'M',
                'H',
                '2',
                'A',
                'P',
                'G',
                'c',
                'n',
                's',
                '4',
            };

        for (int i = 0; i < mByteToBase64Table.size(); ++i)
        {
            mBase64ToByteTable[mByteToBase64Table[i]] = i;
        }

        mBase64ToByteTable[Base64PlaceholderChar] = 0;
    }

private:
    std::vector<char> mRandomByteTable;
    std::vector<char> mByteToBase64Table;
    std::map<char, char> mBase64ToByteTable;
};
// 密码表

std::vector<char> &randomBytes(std::vector<char> &src, int length)
{
    if (src.size() < length)
        src.resize(length);

    std::vector<char> &table = KKeyTable::singleInstance()->randomByteTable();
    for (int i = 0; i < length; ++i)
    {
        src[i] = table[rand() & 63];
    }
    return src;
}

// 字节流转base64
void bytesToBase64(std::vector<char> &bytes, std::vector<char> &base64)
{
    if (bytes.empty())
        return;

    int endCount = bytes.size() % 3;
    if (endCount >= 1)
    {
        bytes.push_back(0);
    }

    if (endCount == 1)
    {
        bytes.push_back(0);
    }

    int bytesCount = bytes.size();
    base64.resize(bytesCount / 3 * 4, 0);
    int base64Count = base64.size();

    std::vector<char> &table = KKeyTable::singleInstance()->byteToBase64Table();

    for (int i = 0, j = 0; i < bytesCount; i += 3, j += 4)
    {
        unsigned int tmp = ((static_cast<unsigned char>(bytes[i]) << 16));
        tmp |= (static_cast<unsigned char>(bytes[i + 1]) << 8);
        tmp |= static_cast<unsigned char>(bytes[i + 2]);

        base64[j + 0] = table[(tmp >> 18) & 0x3F];
        base64[j + 1] = table[(tmp >> 12) & 0x3F];
        base64[j + 2] = table[(tmp >> 6) & 0x3F];
        base64[j + 3] = table[tmp & 0x3F];
    }

    if (endCount >= 1)
    {
        base64[base64Count - 1] = Base64PlaceholderChar;
        bytes.pop_back();
    }

    if (endCount == 1)
    {
        base64[base64Count - 2] = Base64PlaceholderChar;
        bytes.pop_back();
    }
}

// base64转字节流
int base64ToBytes(std::vector<char> &base64, std::vector<char> &bytes)
{
    // 尺寸
    int base64Count = base64.size();
    if (base64Count == 0 || base64Count & 0x3)
    {
        return KE_FileDamaged;
    }
    bytes.resize(base64Count / 4 * 3, 0);

    char a1, a2, a3, a4;
    auto &table = KKeyTable::singleInstance()->base64ToByteTable();
    for (int i = 0, j = 0; i < base64Count; i += 4, j += 3)
    {
        a1 = table[base64[i]];
        a2 = table[base64[i + 1]];
        a3 = table[base64[i + 2]];
        a4 = table[base64[i + 3]];

        unsigned int tmp = (a1 << 18);
        tmp |= (a2 << 12);
        tmp |= (a3 << 6);
        tmp |= (a4);

        bytes[j] = ((tmp >> 16) & 0xFF);
        bytes[j + 1] = ((tmp >> 8) & 0xFF);
        bytes[j + 2] = (tmp & 0xFF);
    }

    if (base64[base64Count - 1] == Base64PlaceholderChar)
    {
        bytes.pop_back();
    }

    if (base64[base64Count - 2] == Base64PlaceholderChar)
    {
        bytes.pop_back();
    }

    return KE_OK;
}

int encode1(const char *srcFilename, std::vector<char> &dst)
{
    std::ifstream fHandle(srcFilename, std::ios::in | std::ios::binary);
    if (!fHandle.is_open())
    {
        return KE_FileNotExit;
    }

    fHandle.seekg(0, std::ios::end);
    int bufferSize = fHandle.tellg();
    fHandle.seekg(0, std::ios::beg);

    vector<char> buffer(bufferSize, 0);
    fHandle.read(buffer.data(), bufferSize);
    fHandle.close();

    return encode3(buffer, dst);
}

int encode2(const char *srcFilename, const char *dstFilename)
{
    if (!srcFilename || !dstFilename)
    {
        return KE_NullPtr;
    }

    std::vector<char> buffer;
    int rlt = KE_OK;
    if (rlt = encode1(srcFilename, buffer) != 0)
    {
        return rlt;
    }

    std::ofstream fileHandle(dstFilename, std::ios::out | std::ios::binary);
    ;
    if (!fileHandle.is_open())
    {
        return KE_FileNotExit;
    }

    fileHandle.write(buffer.data(), buffer.size());
    fileHandle.close();
    return KE_OK;
}

int encode3(const std::vector<char> &src, std::vector<char> &dst)
{
    if (src.size() <= 0)
        return KE_DataError;

    dst.clear();
    // add header 'knef'.
    dst.push_back('k');
    dst.push_back('n');
    dst.push_back('e');
    dst.push_back('f');

    std::vector<char> buffer, base64, encryptBase64;
    // add random 256 chars
    randomBytes(buffer, 256);
    dst.insert(dst.end(), buffer.begin(), buffer.begin() + 256);

    // add 15 * 32 random
    randomBytes(buffer, 15 * 32);
    dst.insert(dst.end(), buffer.begin(), buffer.begin() + 15 * 32);

#ifdef KDEBUG
    buffer.push_back(0);
    std::cout << "Encode Placeholder ::" << buffer.data() << std::endl;
#endif

    // add base64
    bytesToBase64(const_cast<std::vector<char> &>(src), base64);

    std::vector<char> &randomTable = KKeyTable::singleInstance()->byteToBase64Table();
    std::map<char, char> &baseToBayteTable = KKeyTable::singleInstance()->base64ToByteTable();

    encryptBase64.resize(2 + base64.size(), 0);

    // put random 1 char  and  1 start char
    char startC = randomTable[rand() & 0x3F];
    encryptBase64[0] = (randomTable[src.size() % 3]);
    encryptBase64[1] = (startC);

    // convert size;
    unsigned int j = static_cast<unsigned char>(startC);
    for (int i = 0; i < base64.size(); ++i, ++j)
    {
        // char c = rand() & 0x3;
        // encryptBase64.push_back(randomTable[c]);
        encryptBase64[i + 2] = randomTable[(
                                               static_cast<unsigned char>(baseToBayteTable[base64[i]]) + (j & 0x3F)) &
                                           0x3F];
    }
    dst.insert(dst.end(), encryptBase64.begin(), encryptBase64.end());

    // createMd5;
    char Md5[33] = {0};
    createMd5(Md5, dst.data(), dst.size());

#ifdef KDEBUG
    std::cout << "Encode Md5 :: " << Md5 << std::endl;
#endif // KDEBUG

    // compose Md5
    if (buffer.size() < 512)
    {
        buffer.resize(16 * 32, 0);
    }

    for (int i = 0; i < 32; ++i)
    {
        buffer[i << 4] = Md5[i]; // 首位置赋值MD5
        memcpy(buffer.data() + (i << 4) + 1, dst.data() + 260 + 15 * i, 15);
    }

    // remove old
    dst.erase(dst.begin() + 260, dst.begin() + 260 + 15 * 32);
    dst.insert(dst.begin() + 260, buffer.begin(), buffer.end());

    return KE_OK;
}

int decode1(const char *filename, std::vector<char> &dst)
{
    std::ifstream fHandle(filename, std::ios::in | std::ios::binary);
    if (!fHandle.is_open())
    {
        std::cout << "*************** decode1  !fHandle.is_open 0000 " << std::endl;
        return KE_FileNotExit;
    }

    fHandle.seekg(0, std::ios::end);
    int bufferSize = fHandle.tellg();

    if (0 >= bufferSize)
    {
        fHandle.close();
        return KE_FileDamaged_SizeError;
    }

    fHandle.seekg(0, std::ios::beg);

    vector<char> buffer(bufferSize, 0);
    fHandle.read(buffer.data(), bufferSize);
    fHandle.close();
    std::cout << "*************** decode1 ready decode3 " << std::endl;

    return decode3(buffer, dst);
}

int decode2(const char *srcFilename, const char *dstFilename)
{
    if (!dstFilename)
    {
        return KE_NullPtr;
    }

    std::vector<char> buffer;
    int rlt = KE_OK;
    if (rlt = decode1(srcFilename, buffer) != 0)
    {
        return rlt;
    }

    std::string newDstFilename = std::string(dstFilename);

    std::ofstream fileHandle(newDstFilename.c_str(), std::ios::out | std::ios::binary);
    ;
    if (!fileHandle.is_open())
    {
        return KE_FileNotExit;
    }

    fileHandle.write(buffer.data(), buffer.size());
    fileHandle.close();
    return KE_OK;
}

int decode3(const std::vector<char> &src, std::vector<char> &dst)
{
    if (src.size() <= 0)
        return KE_DataError;

    if (src.size() <= 256 + 4 + 512)
    {
        return KE_FileDamaged_SizeError;
    }

    // check header knef
    if (src[0] != 'k' || src[1] != 'n' || src[2] != 'e' || src[3] != 'f')
    {
        return KE_FileDamaged_FormatNotMatched;
    }

    // check md5;
    int start = 260;
    char md5Value[33] = {0};
    std::vector<char> md5Buffer(15 * 32, 0);

    for (int i = 0; i < KDFLT_MD5_LEN; ++i)
    {
        md5Value[i] = src[start + (i << 4)];
        memcpy(md5Buffer.data() + i * 15, src.data() + start + (i << 4) + 1, 15);
    }

#ifdef KDEBUG
    std::vector<char> md5BufferCopy(md5Buffer);
    md5BufferCopy.push_back(0);
    std::cout << "Decode Placeholder ::" << md5BufferCopy.data() << std::endl;
#endif

    std::vector<char> strCopy(src);
    strCopy.erase(strCopy.begin() + 260, strCopy.begin() + 260 + 512);
    strCopy.insert(strCopy.begin() + 260, md5Buffer.begin(), md5Buffer.end());
    createMd5(md5Buffer.data(), strCopy.data(), strCopy.size());
    md5Buffer[32] = 0;

#ifdef KDEBUG
    std::cout << "Decode Md5-1 :: " << md5Value << std::endl;
    std::cout << "Decode Md5-2 :: " << md5Buffer.data() << std::endl;

#endif // KDEBUG

    if (strcmp(md5Value, md5Buffer.data()) != 0)
    {
        return KE_FileDamaged_Md5NotMatched;
    }

    // create base64
    // std::map<char, char> table = KKeyTable::singleInstance()->base64ToByteTable();
    std::vector<char> base64, srcBase64;
    base64.insert(base64.begin(), src.begin() + 260 + 512, src.end());
    std::map<char, char> base64ToByteTable = KKeyTable::singleInstance()->base64ToByteTable();
    std::vector<char> byteToBase64Table = KKeyTable::singleInstance()->byteToBase64Table();

    unsigned char flag = base64ToByteTable[base64[0]];
    unsigned char startC = base64[1];

    srcBase64.resize(base64.size() - 2, 0);

    for (int i = 2, j = startC; i < base64.size(); ++i, ++j)
    {
        unsigned int idx = static_cast<unsigned int>(base64ToByteTable[base64[i]]);
        idx += 64;
        idx -= ((j) & 0x3F);
        srcBase64[i - 2] = byteToBase64Table[idx & 0x3F];
    }

    if (flag >= 1)
    {
        srcBase64[srcBase64.size() - 1] = Base64PlaceholderChar;
    }

    if (flag == 1)
    {
        srcBase64[srcBase64.size() - 2] = Base64PlaceholderChar;
    }

    return base64ToBytes(srcBase64, dst);
}
