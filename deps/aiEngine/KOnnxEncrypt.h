#pragma once
#include <vector>

#ifdef _WIN32
#ifdef KONNXENCRYPT_LIB 
#define KTAPI __declspec(dllexport)
#else
#define KTAPI __declspec(dllimport)
#endif

#else
#define KTAPI
#endif



typedef enum tagKDecodeResult
{
	KE_OK = 0,
	KE_NullPtr,
	KE_DataError,
	KE_FileNotExit,
	KE_FileDamaged,
	KE_FileDamaged_FormatNotMatched,
	KE_FileDamaged_SizeError,
	KE_FileDamaged_Md5NotMatched,
}KDecodeResult;

//编码
extern "C" int KTAPI encode1(const char* srcFilename, std::vector<char>& dst);
extern "C" int KTAPI encode2(const char* srcFilename, const char* dstFilename);
extern "C" int KTAPI encode3(const std::vector<char>& src, std::vector<char>& dst);
		   
//解码
extern "C" int KTAPI decode1(const char* srcFilename, std::vector<char>& dst);
extern "C" int KTAPI decode2(const char* srcFilename, const char* dstFilename);
extern "C" int KTAPI decode3(const std::vector<char>& src, std::vector<char>& dst);
