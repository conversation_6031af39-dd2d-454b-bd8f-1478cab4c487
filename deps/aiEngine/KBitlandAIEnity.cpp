﻿#include "KBitlandAIEnity.h"
#include "common/KDefines.h"
#include <iostream>
#include <atomic>
#include <thread>
#include <future>
#include <chrono>
#include <bmruntime_cpp.h>
#include "KOnnxEncrypt.h"
// #include "Simd/SimdLib.h"

// #define MAX_BUFF_WIDTH 1752
// #define MAX_BUFF_HEIGHT 1752
#define MAX_BATCH 1

using namespace bmruntime;
using namespace std;
using namespace cv;

#define BIT_ASSERT(rlt)    \
    if (BM_SUCCESS != rlt) \
        return rlt;

class KBitlandAIEngineInferEnityPrivate
{
public:
    KBitlandAIEngineInferEnityPrivate() : IsValid(false)
    {
        IsInfer = false;
    }

    ~KBitlandAIEngineInferEnityPrivate()
    {
        for (int frameIndex = 0; frameIndex < 5; frameIndex++)
        {
            if (0 < InputMems[frameIndex].size)
                bm_free_device(Handle, InputMems[frameIndex]);
            bm_image_destroy(InputImgs[frameIndex]);
            if (0 < InputMems_r[frameIndex].size)
                bm_free_device(Handle, InputMems_r[frameIndex]);
            bm_image_destroy(InputImgs_r[frameIndex]);
        }
    }

    void unload()
    {
        // bm_mem_unmap_device_mem(Handle, (int8_t *)RltAddr, bm_mem_get_device_size(OutputTensor->device_mem));
        for (int i = 0; i < OutputTensorNum; i++)
        {
            bm_mem_unmap_device_mem(Handle, (int8_t *)RltAddrs[i], bm_mem_get_device_size(OutputTensors[i]->device_mem));
        }
        if (0 < Dev_mem_rsz.size)
            bm_free_device(Handle, Dev_mem_rsz);
        if (0 < Dev_mem_cvt.size)
            bm_free_device(Handle, Dev_mem_cvt);
        if (0 < Dev_mem_input.size)
            bm_free_device(Handle, Dev_mem_input);
        for (int i = 0; i < MAX_BATCH; i++)
        {
            bm_image_destroy(Resized_imgs[i]);
            bm_image_destroy(Converto_imgs[i]);
            bm_image_destroy(Input_Imgs[i]);
        }
        bm_image_destroy(SpecialResizeImage);
        delete Net;
        Net = nullptr;
    }

    int initHandle(const char *modelpath)
    {
        IsValid = false;
        InputImgs.resize(5);
        InputMems.resize(5);
        InputImgs_r.resize(5);
        InputMems_r.resize(5);

        bm_status_t status;
        std::string path = modelpath;
        std::cout << "---------------initHandle:: model path= " << path << std::endl;
        if (path.find(".bmodel") == std::string::npos)
        {
            std::vector<char> dstData;
            int rlt = decode1(modelpath, dstData);
            if (rlt != KE_OK || !dstData.data())
            {
                std::cout << "***************initHandle:: decode1 kcloudnef failed, ret = " << rlt << std::endl;
                return -1;
            }
            std::cout << "--------------- initHandle:: before Ctx.load_bmodel from data " << std::endl;

            try
            {
                status = Ctx.load_bmodel(dstData.data(), dstData.size());
            }
            catch (...)
            {
                std::cout << "initHandle Ctx.load_bmodel from data, catch some error,return." << std::endl;
                return KE_DataError;
            }
        }
        else
        {
            std::cout << "---------------initHandle:: before Ctx.load_bmodel from model, path= " << path << std::endl;
            try
            {
                status = Ctx.load_bmodel(modelpath);
            }
            catch (...)
            {
                std::cout << "initHandle Ctx.load_bmodel from model, catch some error,return." << std::endl;
                return KE_DataError;
            }
        }

        KAIEngineIODimension inputDims;
        KAIEngineIODimension outputDims;

        BIT_ASSERT(status);
        Net = new Network(Ctx, "cotton");
        Handle = Ctx.handle();
        auto &inputs = Net->Inputs();
        auto inputTensor = (bm_tensor_t *)inputs[0]->tensor();

        // input info
        inputDims.Channel = net_c = inputTensor->shape.dims[1];
        inputDims.Height = net_h = inputTensor->shape.dims[2];
        inputDims.Width = net_w = inputTensor->shape.dims[3];

        float input_scale = Net->info()->input_scales[0];
        float input_scale1 = input_scale; // > 127 ? 127 : input_scale;
        int coef = (int)round(input_scale1 * 1024 / 255.0f) & 0x1fff;
        Coefs.csc_add0 = Coefs.csc_add1 = Coefs.csc_add2 = 0;
        Coefs.csc_coe00 = Coefs.csc_coe11 = Coefs.csc_coe22 = coef;
        Coefs.csc_coe10 = Coefs.csc_coe01 = Coefs.csc_coe02 = 0;
        Coefs.csc_coe20 = Coefs.csc_coe21 = Coefs.csc_coe12 = 0;

        input_scale1 = input_scale * (float)1.0 / 255;
        converto_attr.alpha_0 = input_scale1;
        converto_attr.beta_0 = 0;
        converto_attr.alpha_1 = input_scale1;
        converto_attr.beta_1 = 0;
        converto_attr.alpha_2 = input_scale1;
        converto_attr.beta_2 = 0;

        inputs[0]->Reshape({4, {inputTensor->shape.dims[0], 3, net_h, net_w}});
        int aligned_net_w = FFALIGN(net_w, 64);
        int strides[3] = {aligned_net_w, aligned_net_w, aligned_net_w};

        Resized_imgs.resize(MAX_BATCH);
        Converto_imgs.resize(MAX_BATCH);
        Input_Imgs.resize(MAX_BATCH);

        //        ResizeImage = cv::Mat(net_h, net_w, CV_8UC3);
        //        ResizeImage = cv::Mat(MAX_BUFF_HEIGHT, MAX_BUFF_WIDTH, CV_8UC3);

        for (int i = 0; i < MAX_BATCH; ++i)
        {
            std::cout << " create resize image >>>>>>>>>>>>>>" << std::endl;
            status = bm_image_create(Handle, net_h, net_w, FORMAT_BGR_PLANAR, DATA_TYPE_EXT_1N_BYTE,
                                     &Resized_imgs[i], strides);

            std::cout << " create convert image >>>>>>>>>>>>>>" << std::endl;
            if (0 == inputTensor->dtype)
            {
                status = bm_image_create(Handle, net_h, net_w, FORMAT_BGR_PLANAR, DATA_TYPE_EXT_FLOAT32,
                                         &Converto_imgs[i] /*, strides*/);
            }
            else if (3 == inputTensor->dtype)
            {
                status = bm_image_create(Handle, net_h, net_w, FORMAT_BGR_PLANAR, DATA_TYPE_EXT_1N_BYTE_SIGNED,
                                         &Converto_imgs[i], strides);
            }

            std::cout << " create input image >>>>>>>>>>>>>>" << std::endl;
            status = bm_image_create(Handle, net_h, net_w, FORMAT_BGR_PACKED,
                                     DATA_TYPE_EXT_1N_BYTE, &Input_Imgs[i]);
        }

        bm_image_create(Handle, 32, 32, FORMAT_BGR_PLANAR, DATA_TYPE_EXT_1N_BYTE, &SpecialResizeImage);
        bm_image_alloc_dev_mem(SpecialResizeImage, BMCV_HEAP_ANY);

        bm_image_alloc_contiguous_mem(MAX_BATCH, Resized_imgs.data(), 1);
        bm_image_alloc_contiguous_mem(MAX_BATCH, Converto_imgs.data(), 0);
        bm_image_alloc_contiguous_mem(MAX_BATCH, Input_Imgs.data());

        bm_image_get_contiguous_device_mem(MAX_BATCH, Resized_imgs.data(), &Dev_mem_rsz);
        bm_image_get_contiguous_device_mem(MAX_BATCH, Converto_imgs.data(), &Dev_mem_cvt);
        bm_image_get_contiguous_device_mem(MAX_BATCH, Input_Imgs.data(), &Dev_mem_input);

        inputs[0]->set_device_mem(Dev_mem_cvt);

        // output info
        auto &outputs = Net->Outputs();
        OutputTensorNum = outputs.size(); // 输出数据形式的数量，比如输出图是一种，输出一个数值是一种。
        OutputTensors.resize(OutputTensorNum);
        RltAddrs.resize(OutputTensorNum);
        if (OutputTensorNum == 1)
        {
            OutputTensors[0] = (bm_tensor_t *)outputs[0]->tensor();
            outputDims.Channel = OutputTensors[0]->shape.dims[1]; // 输出类别数，比如黑点和小结构就是两种类别
            outputDims.Height = OutputTensors[0]->shape.dims[2];  // 该种输出形式的结果数据的宽高
            outputDims.Width = OutputTensors[0]->shape.dims[3];
            status = bm_mem_mmap_device_mem(Handle, (bm_device_mem_t *)&(OutputTensors[0]->device_mem), &RltAddrs[0]);
            BIT_ASSERT(status);
            BitInferData.InputDim.push_back(inputDims);
            BitInferData.OutputDim.push_back(outputDims);
            BitInferData.output_scale = Net->info()->output_scales[0];
            BitInferData.rltdata = (char *)RltAddrs[0];
        }
        else
        {
            for (int i = 0; i < OutputTensorNum; i++)
            {
                OutputTensors[i] = (bm_tensor_t *)outputs[i]->tensor();
                outputDims.Channel = OutputTensors[i]->shape.dims[1];
                outputDims.Height = OutputTensors[i]->shape.dims[2];
                outputDims.Width = OutputTensors[i]->shape.dims[3];
                status = bm_mem_mmap_device_mem(Handle, (bm_device_mem_t *)&(OutputTensors[i]->device_mem), &RltAddrs[i]);
                BIT_ASSERT(status);
                BitInferData.InputDim.push_back(inputDims);
                BitInferData.OutputDim.push_back(outputDims);
                BitInferData.output_scales.push_back(Net->info()->output_scales[i]);
                BitInferData.rltdatas.push_back((char *)RltAddrs[i]);
                std::cout << " Output Tensor " << i << " : [" << OutputTensors[i]->shape.dims[0] << ", " << OutputTensors[i]->shape.dims[1] << ", "
                          << OutputTensors[i]->shape.dims[2] << ", " << OutputTensors[i]->shape.dims[3] << "]" << std::endl;
            }
        }

        IsValid = true;
        return KE_OK;
    }

    bm_status_t memcpy_c2c_byte(bm_handle_t src_handle, bm_device_mem_t src, size_t src_offset,
                                bm_handle_t dst_handle, bm_device_mem_t dst, size_t dst_offset, size_t size)
    {
        assert(BM_MEM_TYPE_DEVICE == bm_mem_get_type(src));
        assert(BM_MEM_TYPE_DEVICE == bm_mem_get_type(dst));

        bm_device_mem_t src1 = src;
        bm_device_mem_t dst1 = dst;

        bm_mem_set_device_addr(&src1, bm_mem_get_device_addr(src1) + src_offset);
        bm_mem_set_device_size(&src1, size);

        // onstructor dst device mem
        bm_mem_set_device_addr(&dst1, bm_mem_get_device_addr(dst1) + dst_offset);
        bm_mem_set_device_size(&dst1, size);

        return bm_memcpy_c2c(src_handle, dst_handle, src1, dst1, 0);
    }

public:
    bool IsValid;
    int net_c, net_h, net_w;

    int frameId = 0;

    std::vector<bm_image> InputImgs;
    std::vector<bm_device_mem_t> InputMems;

    std::vector<bm_image> InputImgs_r;
    std::vector<bm_device_mem_t> InputMems_r;

    atomic_bool IsInfer;
    //    cv::Mat ResizeImage;
    Context Ctx;
    bm_handle_t Handle;
    Network *Net = nullptr;
    // bm_tensor_t *OutputTensor;
    std::vector<bm_tensor_t *> OutputTensors;
    int OutputTensorNum;
    csc_matrix_t Coefs;
    bmcv_convert_to_attr converto_attr;

    // resize and convert mem_t
    bm_device_mem_t Dev_mem_rsz, Dev_mem_cvt, Dev_mem_input;

    // init count by maxbatch size
    std::vector<bm_image> Input_Imgs;
    std::vector<bm_image> Resized_imgs;
    std::vector<bm_image> Converto_imgs;

    // init resize image for some special process
    bm_image SpecialResizeImage;

    // gpu mem buff
    std::vector<bm_device_mem_t> RgbMemBuff;

    // result data
    // unsigned long long RltAddr;
    std::vector<unsigned long long> RltAddrs;

    KBitlandAIEngineInferData BitInferData;

    std::chrono::high_resolution_clock::time_point time[6];
    float Sum[6] = {0};
    float Min[6] = {1000, 1000, 1000, 1000, 1000, 1000};
    float Max[6] = {0};
    float Ave[6] = {0};
    int Count = 0;
};

///////////////////////////////////////////////////////////////////////////////////////////////////////
KBitlandAIEngineInferEnity::KBitlandAIEngineInferEnity(KAIEngine *e) : KAIEngineInferEnity(e), p(0)
{
    CREATE_COMMON_PTR(p, KBitlandAIEngineInferEnityPrivate);
}

KBitlandAIEngineInferEnity::KBitlandAIEngineInferEnity(const string &id, KAIEngine *e)
    : KAIEngineInferEnity(e), p(0)
{
    setId(id);
    CREATE_COMMON_PTR(p, KBitlandAIEngineInferEnityPrivate);
}

KBitlandAIEngineInferEnity::~KBitlandAIEngineInferEnity()
{
    if (p->IsValid)
        p->unload();
    RELEASE_COMMON_PTR(p);
}

int KBitlandAIEngineInferEnity::bitInfer(void *src_handle, void *src_dev_t, int startpos, int width, int height, int idx)
{
    p->IsInfer = true;
    int sclen = width * height * 3;
    int offset = sclen * idx;

    bm_mem_flush_partial_device_mem((bm_handle_t)src_handle, (bm_device_mem_t *)src_dev_t, startpos, sclen);
    p->memcpy_c2c_byte((bm_handle_t)src_handle, *(bm_device_mem_t *)src_dev_t, startpos, p->Handle, p->Dev_mem_rsz, offset, sclen);

    // bm_status_t rlt = bmcv_image_vpp_convert(p->Handle, 1, p->Input_Imgs[idx], &p->Resized_imgs[idx]);

    // BIT_ASSERT(rlt);

    auto rlt = bmcv_image_convert_to(p->Handle, 1, p->converto_attr, &p->Resized_imgs[idx], &p->Converto_imgs[idx]);

    //    rlt = bmcv_image_vpp_basic(p->Handle, 1, &p->Resized_imgs[idx], &p->Converto_imgs[idx],
    //                               0, 0, 0, BMCV_INTER_LINEAR, CSC_USER_DEFINED_MATRIX, &p->Coefs);

    BIT_ASSERT(rlt);

    rlt = p->Net->Forward();
    for (int i = 0; i < p->OutputTensorNum; i++)
        bm_mem_invalidate_device_mem(p->Handle, (bm_device_mem_t *)(&p->OutputTensors[i]->device_mem));
    p->IsInfer = false;

    return 0;
}

int KBitlandAIEngineInferEnity::createHandle(const char *modelpath)
{
    int ret = KE_OK;
    if (p->IsValid)
    {
        auto funcLoad = [this, modelpath, &ret]()
        {
            p->IsValid = false;
            while (p->IsInfer)
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            p->unload();
            try
            {
                std::cout << "KBitlandAIEngineInferEnity::createHandle  in thread" << std::endl;
                ret = p->initHandle(modelpath);
            }
            catch (...)
            {
                return KE_DataError;
            }
        };
        std::packaged_task<void()> task(funcLoad);
        std::thread(std::move(task)).detach();

        return ret;
    }

    try
    {
        std::cout << "KBitlandAIEngineInferEnity::createHandle not in thread" << std::endl;
        ret = p->initHandle(modelpath);
    }
    catch (...)
    {
        return KE_DataError;
    }
    return ret;
}

bool KBitlandAIEngineInferEnity::isValid()
{
    return p->IsValid;
}

void convertMat(uchar *srcdata, uchar *dstdata, int w, int h)
{
    //    int clen = w * h;
    //    SimdDeinterleaveBgr(srcdata, w * 3, w, h,
    //                        dstdata + (clen << 1), w, dstdata + clen, w, dstdata, w);
}
void KBitlandAIEngineInferEnity::setframeId(int frameId)
{
    p->frameId = frameId;
}
int KBitlandAIEngineInferEnity::infer(KImage2 *srcImg)
{
    if (srcImg->width() != p->InputImgs[p->frameId].width || srcImg->height() != p->InputImgs[p->frameId].height)
    {
        bm_image_destroy(p->InputImgs[p->frameId]);
        bm_image_create(p->Handle, srcImg->height(), srcImg->width(), FORMAT_BGR_PACKED, DATA_TYPE_EXT_1N_BYTE, &p->InputImgs[p->frameId]);
    }
    unsigned long long devicebuff;
    unsigned long long databuff = (unsigned long long)srcImg->src().data;
    bm_mem_vir_to_phy(p->Handle, databuff, &devicebuff);
    bm_device_mem_t ImgBuff = bm_mem_from_device(devicebuff, srcImg->src().rows * srcImg->src().cols * 3); // 把ImgBuff和devicebuff链接起来
    bm_mem_flush_device_mem(p->Handle, &ImgBuff);
    bm_image_attach(p->InputImgs[p->frameId], &ImgBuff);

    auto rlt = bmcv_image_vpp_basic(p->Handle, 1, &p->InputImgs[p->frameId], &p->Resized_imgs[0]); // BGR_PACKED->BGR_PLANAR

    bmcv_image_convert_to(p->Handle, 1, p->converto_attr, &p->Resized_imgs[0], &p->Converto_imgs[0]);
    BIT_ASSERT(rlt);
    rlt = p->Net->Forward();
    for (int i = 0; i < p->OutputTensorNum; i++)
        bm_mem_invalidate_device_mem(p->Handle, (bm_device_mem_t *)(&p->OutputTensors[i]->device_mem));
    BIT_ASSERT(rlt);

    return 0;
}

// int KBitlandAIEngineInferEnity::infer(KImage2 *srcImg)
// {
//     unsigned char *input_data[3] = {srcImg->data(), srcImg->data() + srcImg->height() * srcImg->width(), srcImg->data() + 2 * srcImg->height() * srcImg->width()};
//     if (srcImg->width() != p->InputImgs[p->frameId].width || srcImg->height() != p->InputImgs[p->frameId].height)
//     {
//         bm_free_device(p->Handle, p->InputMems[p->frameId]);
//         bm_image_destroy(p->InputImgs[p->frameId]);
//         bm_image_create(p->Handle, srcImg->height(), srcImg->width(), FORMAT_BGR_PACKED, DATA_TYPE_EXT_1N_BYTE, &p->InputImgs[p->frameId]);
//         bm_image_alloc_contiguous_mem(1, &p->InputImgs[p->frameId], 0);
//         bm_image_get_contiguous_device_mem(1, &p->InputImgs[p->frameId], &p->InputMems[p->frameId]);
//     }
//     bm_image_copy_host_to_device(p->InputImgs[p->frameId], (void **)input_data);                   // toBMI and nocvtcolor(BGR_PACKED->BGR_PACKED)
//     auto rlt = bmcv_image_vpp_basic(p->Handle, 1, &p->InputImgs[p->frameId], &p->Resized_imgs[0]); // BGR_PACKED->BGR_PLANAR

//     bmcv_image_convert_to(p->Handle, 1, p->converto_attr, &p->Resized_imgs[0], &p->Converto_imgs[0]);
//     BIT_ASSERT(rlt);
//     rlt = p->Net->Forward();
//     for (int i = 0; i < p->OutputTensorNum; i++)
//         bm_mem_invalidate_device_mem(p->Handle, (bm_device_mem_t *)(&p->OutputTensors[i]->device_mem));
//     BIT_ASSERT(rlt);

//     return 0;
// }

// int KBitlandAIEngineInferEnity::infer(KImage2 *srcImg)
//{
//     cvtColor(srcImg->src(), srcImg->src(), COLOR_RGB2BGR);
//     bm_image tempImage;
//     //    status = bm_image_create(Handle, srcImg->height(), srcImg->width(), FORMAT_BGR_PACKED,
//     //                             DATA_TYPE_EXT_1N_BYTE, &tempImage);
//     cv::bmcv::toBMI(srcImg->src(), &tempImage);
//     auto rlt = bmcv_image_vpp_basic(p->Handle, 1, &tempImage, &p->Resized_imgs[0]);
//     bm_image_destroy(tempImage);

//    //    cv::bmcv::toBMI(srcImg->src(), &p->Input_Imgs[0]);
//    //    auto rlt = bmcv_image_vpp_basic(p->Handle, 1, &p->Input_Imgs[0], &p->Resized_imgs[0]);

//    bmcv_image_convert_to(p->Handle, 1, p->converto_attr, &p->Resized_imgs[0], &p->Converto_imgs[0]);
//    BIT_ASSERT(rlt);
//    rlt = p->Net->Forward();.
//    for (int i = 0; i < p->OutputTensorNum; i++)
//          bm_mem_invalidate_device_mem(p->Handle, (bm_device_mem_t *)(&p->OutputTensors[i]->device_mem));
//    BIT_ASSERT(rlt);

//    return 0;
//}

bm_image *KBitlandAIEngineInferEnity::bmImagePtr(int frameIndex) const
{
    return &p->InputImgs[frameIndex];
}

int KBitlandAIEngineInferEnity::infer(bm_image *srcBmImg)
{
    auto rlt = bmcv_image_vpp_basic(p->Handle, 1, srcBmImg, &p->Resized_imgs[0]); // BGR_PACKED->BGR_PLANAR
    bmcv_image_convert_to(p->Handle, 1, p->converto_attr, &p->Resized_imgs[0], &p->Converto_imgs[0]);
    BIT_ASSERT(rlt);
    rlt = p->Net->Forward();
    for (int i = 0; i < p->OutputTensorNum; i++)
        bm_mem_invalidate_device_mem(p->Handle, (bm_device_mem_t *)(&p->OutputTensors[i]->device_mem));
    BIT_ASSERT(rlt);
    return 0;
}

int KBitlandAIEngineInferEnity::infer(KImage2 *srcImg, KRect rect)
{
    bmcv_rect_t crop_r = {rect.x, rect.y, rect.width, rect.height};
    int crop_num = 1;
    unsigned char *input_data[3] = {srcImg->data(), srcImg->data() + srcImg->height() * srcImg->width(), srcImg->data() + 2 * srcImg->height() * srcImg->width()}; // toBMI&&nocvtcolor

    if (srcImg->width() != p->InputImgs_r[p->frameId].width || srcImg->height() != p->InputImgs_r[p->frameId].height)
    {
        bm_free_device(p->Handle, p->InputMems_r[p->frameId]);
        bm_image_destroy(p->InputImgs_r[p->frameId]);
        bm_image_create(p->Handle, srcImg->height(), srcImg->width(), FORMAT_BGR_PACKED, DATA_TYPE_EXT_1N_BYTE, &p->InputImgs_r[p->frameId]);
        bm_image_alloc_contiguous_mem(1, &p->InputImgs_r[p->frameId], 0);
        bm_image_get_contiguous_device_mem(1, &p->InputImgs_r[p->frameId], &p->InputMems_r[p->frameId]);
    }
    bm_image_copy_host_to_device(p->InputImgs_r[p->frameId], (void **)input_data);                                       // toBMI and nocvtcolor(BGR_PACKED->BGR_PACKED)
    auto rlt = bmcv_image_vpp_basic(p->Handle, 1, &p->InputImgs_r[p->frameId], &p->Resized_imgs[0], &crop_num, &crop_r); // BGR_PACKED->Croped_BGR_PACKED->BGR_PLANAR

    bmcv_image_convert_to(p->Handle, 1, p->converto_attr, &p->Resized_imgs[0], &p->Converto_imgs[0]);
    BIT_ASSERT(rlt);
    rlt = p->Net->Forward();
    for (int i = 0; i < p->OutputTensorNum; i++)
        bm_mem_invalidate_device_mem(p->Handle, (bm_device_mem_t *)(&p->OutputTensors[i]->device_mem));
    BIT_ASSERT(rlt);
    // std::cout<<"w = "<<p->Resized_imgs[0].width<<",h = "<<p->Resized_imgs[0].height<<std::endl;
    // bm1684x_vpp_write_bin(p->Resized_imgs[0],"Resz");
    return 0;
}

int KBitlandAIEngineInferEnity::infer(bm_image *srcImg, KRect rect)
{
    bmcv_rect_t crop_r = {rect.x, rect.y, rect.width, rect.height};
    int crop_num = 1;
    auto rlt = bmcv_image_vpp_basic(p->Handle, 1, srcImg, &p->Resized_imgs[0], &crop_num, &crop_r); // BGR_PACKED->Croped_BGR_PACKED->BGR_PLANAR
    bmcv_image_convert_to(p->Handle, 1, p->converto_attr, &p->Resized_imgs[0], &p->Converto_imgs[0]);
    BIT_ASSERT(rlt);
    rlt = p->Net->Forward();
    for (int i = 0; i < p->OutputTensorNum; i++)
        bm_mem_invalidate_device_mem(p->Handle, (bm_device_mem_t *)(&p->OutputTensors[i]->device_mem));
    BIT_ASSERT(rlt);
    //    std::cout<<"w = "<<p->Resized_imgs[0].width<<",h = "<<p->Resized_imgs[0].height<<std::endl;
    //    bm1684x_vpp_write_bin(p->Resized_imgs[0],"Resz");
    return 0;
}

// int KBitlandAIEngineInferEnity::infer_noClone(KImage2 *srcImg)
//{
//     int rlt = BM_SUCCESS;
//     cvtColor(srcImg->src(), srcImg->src(), COLOR_RGB2BGR);
//     rlt = cv::bmcv::toBMI(srcImg->src(), &p->Input_Imgs[0]);
//     BIT_ASSERT(rlt);
//     rlt = bmcv_image_vpp_basic(p->Handle, 1, &p->Input_Imgs[0], &p->Resized_imgs[0]);
//     BIT_ASSERT(rlt);
//     rlt = infer(&p->Resized_imgs[0]);
//     BIT_ASSERT(rlt);
//     return BM_SUCCESS;
// }

bool KBitlandAIEngineInferEnity::resizeInputImageToMat(int dstW, int dstH, Mat &m)
{
    if (dstW != p->SpecialResizeImage.width || dstH != p->SpecialResizeImage.height)
    {
        bm_image_destroy(p->SpecialResizeImage);
        bm_image_create(p->Handle, dstH, dstW, FORMAT_BGR_PLANAR, DATA_TYPE_EXT_1N_BYTE, &p->SpecialResizeImage);
        bm_image_alloc_dev_mem(p->SpecialResizeImage, BMCV_HEAP_ANY);
    }
    bm_status_t rlt = bmcv_image_vpp_basic(p->Handle, 1, &p->Input_Imgs[0], &p->SpecialResizeImage);
    if (BM_SUCCESS != rlt)
    {
        std::cout << "KBitlandAIEngineInferEnity::resizeInputImage-->bmcv_image_vpp_basic failed! **** " << std::endl;
        return false;
    }

    rlt = cv::bmcv::toMAT(&p->SpecialResizeImage, m);
    if (BM_SUCCESS != rlt)
        std::cout << "KBitlandAIEngineInferEnity::resizeInputImage-->cv::bmcv::toMat failed! **** " << std::endl;
    return rlt;
}

KAIEngineInferData *KBitlandAIEngineInferEnity::data()
{
    return &p->BitInferData;
}
