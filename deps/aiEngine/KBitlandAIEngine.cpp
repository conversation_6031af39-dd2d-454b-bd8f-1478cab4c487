﻿#include "KBitlandAIEngine.h"
#include "KBitlandAIEnity.h"
#include "common/KDefines.h"
#include <iostream>
#include <bmruntime_cpp.h>
#include "KOnnxEncrypt.h"

#define MAX_BATCH 1

using namespace bmruntime;
using namespace std;

class KBitlandAIEnginePrivate
{
public:
    KBitlandAIEnginePrivate() : IsEnabled(true), IsValid(false), RgbBuffAddr(0)
    {
    }

    ~KBitlandAIEnginePrivate()
    {
    }

    bool load(const char *modelpath)
    {
        //        KAIEngineIODimension inputDims;
        //        KAIEngineIODimension outputDims;

        Info.InputDim.clear();
        Info.OutputDim.clear();

        IsValid = false;

        bm_status_t status;
        std::string path = modelpath;
        std::cout << "---------------load:: model path= " << path << std::endl;
        if (path.find(".bmodel") == std::string::npos)
        {
            std::vector<char> dstData;
            int rlt = decode1(modelpath, dstData);
            if (rlt != KE_OK)
            {
                std::cout << "*************** decode1 kcloudnef failed, ret = " << rlt << std::endl;
                return -1;
            }
            std::cout << "---------------load:: before Ctx.load_bmodel from data " << std::endl;
            status = Ctx.load_bmodel(dstData.data(), dstData.size());
        }
        else
        {
            std::cout << "---------------load:: before Ctx.load_bmodel from model, path= " << path << std::endl;
            status = Ctx.load_bmodel(modelpath);
        }

        //        bm_status_t status = Ctx.load_bmodel(modelpath);
        if (BM_SUCCESS == status)
        {
            Net = new Network(Ctx, "cotton");
            Handle = Ctx.handle();

            //            auto &inputs = Net->Inputs();
            //            auto inputTensor = (bm_tensor_t *)inputs[0]->tensor();

            //            //input info
            //            inputDims.Channel = inputTensor->shape.dims[1];
            //            inputDims.Height = inputTensor->shape.dims[2];
            //            inputDims.Width = inputTensor->shape.dims[3];

            //            auto &outputs = Net->Outputs();
            //            auto outputTensor = (bm_tensor_t *)outputs[0]->tensor();

            //            //output info
            //            outputDims.Channel = outputTensor->shape.dims[1];
            //            outputDims.Height = outputTensor->shape.dims[2];
            //            outputDims.Width = outputTensor->shape.dims[3];

            IsValid = true;
            //            Info.InputDim.push_back(inputDims);
            //            Info.OutputDim.push_back(outputDims);

            ModelPath = modelpath;
        }

        return IsValid;
    }

    int unload()
    {
        if (IsValid)
        {
            if (RgbBuffAddr)
            {
                bm_mem_unmap_device_mem(Handle, (int8_t *)RgbBuffAddr, bm_mem_get_device_size(RgbBuff));
                bm_free_device(Handle, RgbBuff);
                RgbBuffAddr = 0;
            }
            if (Net)
            {
                delete Net;
                Net = nullptr;
            }
            IsValid = false;
        }

        return 0;
    }

    //malloc dev mem
    bool resizeBuff(int size)
    {
        if (RgbBuffAddr)
            return true;

        bm_status_t status;
        printf("**************************** resizeBuff size = %p \n", size);
        status = bm_malloc_device_byte_heap(Handle, &RgbBuff, 2, size);
        bm_mem_mmap_device_mem(Handle, &RgbBuff, &RgbBuffAddr);

        return (BM_SUCCESS == status);
    }

public:
    bool IsEnabled;
    bool IsValid;

    int MaxBatchSize;
    KAIEngineInfo Info;

    Context Ctx;
    bm_handle_t Handle;
    Network *Net = nullptr;
    bm_device_mem_t RgbBuff;
    unsigned long long RgbBuffAddr;

    string ModelPath;
};

///////////////////////////////////////
KBitlandAIEngine::KBitlandAIEngine(const std::string &id) : KAIEngine(id), p(0)
{
    CREATE_COMMON_PTR(p, KBitlandAIEnginePrivate);
}

KBitlandAIEngine::~KBitlandAIEngine()
{
    unload();
    KAIEnity1D &&es = enitys();

    for (KAIEngineInferEnity *e : es)
        removeEnity(e->id());

    RELEASE_COMMON_PTR(p);
}

bool KBitlandAIEngine::isValid()
{
    return p->IsValid;
}

bool KBitlandAIEngine::isEnable()
{
    return p->IsEnabled;
}

void KBitlandAIEngine::loadEngine(const string &path)
{
    p->load(path.c_str());
}

bool KBitlandAIEngine::createEngineBuff(int buffsize)
{
    if (p->RgbBuffAddr)
        return true;

    return p->resizeBuff(buffsize);
}

void *KBitlandAIEngine::buffHandle()
{
    return p->Handle;
}

void *KBitlandAIEngine::bmDevBuffPtr()
{
    if (!p->IsValid)
        return nullptr;

    return &p->RgbBuff;
}

uchar *KBitlandAIEngine::buffHeader(int offset)
{
    return p->IsValid ? ((uchar *)(p->RgbBuffAddr) + offset) : nullptr;
}

const KAIEngineInfo &KBitlandAIEngine::info() const
{
    return p->Info;
}

int KBitlandAIEngine::load(const std::string &path)
{
    if (p->load(path.c_str()))
    {
        KAIEnity1D &&es = enitys();
        for (KAIEngineInferEnity *e : es)
        {
            dynamic_cast<KBitlandAIEngineInferEnity *>(e)->createHandle(path.c_str());
        }

        return 0;
    }

    return 1;
}

int KBitlandAIEngine::unload()
{
    return p->unload();
}

KAIEngineInferEnity *KBitlandAIEngine::createEnity(const std::string &id)
{
    std::cout << "========= create enity :: " << path().c_str() << std::endl;
    KAIEngineInferEnity *enity = new KBitlandAIEngineInferEnity(id, this);
    if (!enity)
    {
        RELEASE_COMMON_PTR(enity);
        return 0;
    }

    int ret = dynamic_cast<KBitlandAIEngineInferEnity *>(enity)->createHandle(path().c_str());
    std::cout << "========= enity load path :: " << path().c_str()
              << ", retCode = " << ret << std::endl;
    if (KE_OK != ret)
    {
        //        delete enity;
        enity = nullptr;
    }
    setCreateEnityRetCode(ret);

    return enity;
}
