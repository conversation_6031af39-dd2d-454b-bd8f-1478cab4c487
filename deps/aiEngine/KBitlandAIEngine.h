#ifndef KBITLANDAIENGINE_H
#define KBITLANDAIENGINE_H

#include "KBitlandGlobal.h"
#include "algorithm/KAIEngine.h"

class KBitlandAIEnginePrivate;
///////////////////////////////////////////////////////////////////
/// \brief The KBitlandAIEngine class
class KBITLANDAIENGINE_EXPORT KBitlandAIEngine : public KAIEngine
{
public:
    KBitlandAIEngine(const std::string &id);
    ~KBitlandAIEngine();

public:
    bool isValid();
    bool isEnable();

    void loadEngine(const std::string &path);
    bool createEngineBuff(int buffsize);

    void *bmDevBuffPtr();
    void *buffHandle();

    uchar *buffHeader(int offset = 0);

    const KAIEngineInfo &info() const override;

protected:
    int load(const std::string &path) override;
    int unload() override;
    KAIEngineInferEnity *createEnity(const std::string &id) override;

public:
    KBitlandAIEnginePrivate *p;
};

#endif
