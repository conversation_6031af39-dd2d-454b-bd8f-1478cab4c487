#include "KMd5.h"
#include <string>

#ifdef _WIN32
#include <windows.h>

typedef struct {
	ULONG i[2];                          /* number of _bits_ handled mod 2^64 */
	ULONG buf[4];                                           /* scratch buffer */
	unsigned char fHandle[64];                                     /* input buffer */
	unsigned char digest[16];            /* actual digest after MD5Final call */
} MD5_CTX;
#define PROTO_LIST(list)    list


typedef void(WINAPI* PMD5Init) PROTO_LIST((MD5_CTX *));
typedef void(WINAPI* PMD5Update) PROTO_LIST((MD5_CTX *, const unsigned char *, unsigned int));
typedef void(WINAPI* PMD5Final)PROTO_LIST((MD5_CTX *));

HINSTANCE hDLL = LoadLibraryA("advapi32.dll");
PMD5Init MD5Init = (PMD5Init)GetProcAddress(hDLL, "MD5Init");;
PMD5Update MD5Update = (PMD5Update)GetProcAddress(hDLL, "MD5Update");;
PMD5Final MD5Final = (PMD5Final)GetProcAddress(hDLL, "MD5Final");;
#else
#include <openssl/md5.h>
unsigned char digest[16];
#endif

char* createMd5(char* dst, char* src, int bufferSize)
{
    if (!dst || bufferSize == 0)
	{
		return src;
	}

	MD5_CTX ctx;
    
#ifdef _WIN32
	MD5Init(&ctx);
	MD5Update(&ctx, (unsigned char*)src, bufferSize);
	MD5Final(&ctx);
    return hex2Str(dst, ctx.digest, 16);
#else
	MD5_Init(&ctx);
	MD5_Update(&ctx, (unsigned char*)src, bufferSize);
	MD5_Final(digest, &ctx);
    return hex2Str(dst, digest, 16);
#endif
}

char *hex2Str(char* dst, unsigned char *src, int Len)
{
	for (int i = 0; i < Len; i++)
	{
		dst[i * 2] = "0123456789ABCDEF"[src[i] >> 4];
		dst[i * 2 + 1] = "0123456789ABCDEF"[src[i] & 0x0F];
        //sprintf(dst + i * 2, "%02x", src[i]);
	}
    //dst[Len * 2] = '\0';

	return dst;
}
