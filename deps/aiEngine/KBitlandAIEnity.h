﻿#ifndef KBITLANDAIENITY_H
#define KBITLANDAIENITY_H

#include "KBitlandGlobal.h"
#include "algorithm/KAIEngine.h"
#include "KBitlandAIEngine.h"
//////////////////////////////////////////////////////////////////////
class KBitlandAIEngineInferData : public KAIEngineInferData
{
public:
    std::vector<KAIEngineIODimension> InputDim;  //输入维度
    std::vector<KAIEngineIODimension> OutputDim; //输出维度
    //result mem addr
    float output_scale;
    char *rltdata;

    std::vector<float> output_scales;
    std::vector<char *> rltdatas;
};

class KBitlandAIEngineInferEnityPrivate;
class KBITLANDAIENGINE_EXPORT KBitlandAIEngineInferEnity : public KAIEngineInferEnity
{
public:
    KBitlandAIEngineInferEnity(KAIEngine *e);
    KBitlandAIEngineInferEnity(const std::string &id, KAIEngine *e);
    ~KBitlandAIEngineInferEnity();

    bool isValid() override;

    void setframeId(int frameId);
    int infer(KImage2 *srcImg);
    int infer(bm_image *srcBmImg);
    int infer(KImage2 *srcImg, KRect rect);
    int infer(bm_image *srcImg, KRect rect);

    //bm_image *bmImagePtr() const;
    bm_image *bmImagePtr(int frameIndex = 0) const;
    bool resizeInputImageToMat(int dstW, int dstH, cv::Mat &m);

    KAIEngineInferData *data() override;

public:
    /**
     * @brief bitinfer
     * @param src_handle  图像源内存模型handle
     * @param src_dev_t   图像源内存指针
     * @param startpos	  图像源内存起始地址
     * @param width       图像宽度
     * @param height      图像高度
     * @param idx
     */
    int bitInfer(void *src_handle, void *src_dev_t, int startpos, int width, int height, int idx = 0);

    int createHandle(const char *modelpath);

private:
    KBitlandAIEngineInferEnityPrivate *p;
};

#endif // KBITLANDAIENITY_H
