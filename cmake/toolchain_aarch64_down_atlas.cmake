# # Arm Platform 
message("compiling for arrach64_down_atlas" )
set(CMAKE_C_COMPILER /usr/local/Ascend/ascend-toolkit/latest/toolkit/toolchain/hcc/bin/aarch64-target-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER  /usr/local/Ascend/ascend-toolkit/latest/toolkit/toolchain/hcc/bin/aarch64-target-linux-gnu-g++)
# set(CMAKE_PREFIX_PATH /usr/local/Ascend/)
# set(CMAKE_MODULE_PATH /usr/local/Ascend/pkgconfig)

set(EnvAtlasPath /usr/local/Ascend)
set(DownEnvPath ${EnvAtlasPath})

set(CMAKE_FIND_LIBRARY_PREFIXES "lib")
set(CMAKE_FIND_LIBRARY_SUFFIXES ".so" ".a" ".la")

set(ENV{PKG_CONFIG_PATH} "/usr/local/Ascend/pkgconfig:$ENV{PKG_CONFIG_PATH}")

set(Atlas_INCLUDE_DIRS ${EnvAtlasPath}/ascend-toolkit/latest/include)
set(Atlas_LIBRARY_DIRS ${EnvAtlasPath}/ascend-toolkit/latest/lib64)


find_package(PkgConfig REQUIRED)
# 查找pcap
pkg_check_modules(LIBPCAP REQUIRED libpcap)
# 查找opencv
pkg_check_modules(OpenCV REQUIRED opencv4)
# 查找openssl
pkg_check_modules(OpenSSL REQUIRED openssl)

# message(STATUS "**OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
# message(STATUS "**OpenCV_LIBRARY_DIRS: ${OpenCV_LIBRARY_DIRS}")
# message(STATUS "**OpenCV_LIBRARIES: ${OpenCV_LIBRARIES}")
# message(STATUS "**OpenSSL_INCLUDE_DIRS: ${OpenSSL_INCLUDE_DIRS}")
# message(STATUS "**OpenSSL_LIBRARY_DIRS: ${OpenSSL_LIBRARY_DIRS}")
# message(STATUS "**OpenSSL_LIBRARIES: ${OpenSSL_LIBRARIES}")
# message(STATUS "**LIBPCAP_INCLUDE_DIRS: ${LIBPCAP_INCLUDE_DIRS}")
# message(STATUS "**LIBPCAP_LIBRARY_DIRS: ${LIBPCAP_LIBRARY_DIRS}")
# message(STATUS "**LIBPCAP_LIBRARIES: ${LIBPCAP_LIBRARIES}")






