# Arm Platform 
# 下位机 1684
message("compiling for arrach64_down_bm1684 " )
set(bm1684Env /usr/local/1684Env)
set(CMAKE_C_COMPILER /usr/local/1684Env/aarch64Toolchain/gcc-linaro-7.5.0-2019.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc)
set(CMAKE_CXX_COMPILER /usr/local/1684Env/aarch64Toolchain/gcc-linaro-7.5.0-2019.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++)
set(CMAKE_PREFIX_PATH /usr/local/1684Env/bm1684)
set(CMAKE_MODULE_PATH /usr/local/1684Env/pkgconfig)

set(Env1684Path /usr/local/1684Env/bm1684)
set(DownEnvPath ${Env1684Path})

set(CMAKE_FIND_LIBRARY_PREFIXES "lib")
set(CMAKE_FIND_LIBRARY_SUFFIXES ".so" ".a" ".la")

set(Qt5_DIR "/usr/local/1684Env/Qt5.12.0/lib/cmake/Qt5/") 

set(ENV{PKG_CONFIG_PATH} "/usr/local/1684Env/pkgconfig:$ENV{PKG_CONFIG_PATH}")

find_package(PkgConfig REQUIRED)


# 查找pcap
pkg_check_modules(LIBPCAP REQUIRED libpcap)
# 查找opencv
pkg_check_modules(OpenCV REQUIRED opencv4)
# 查找openssl
pkg_check_modules(OpenSSL REQUIRED openssl)

# message(STATUS "**OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
# message(STATUS "**OpenCV_LIBRARY_DIRS: ${OpenCV_LIBRARY_DIRS}")
# message(STATUS "**OpenCV_LIBRARIES: ${OpenCV_LIBRARIES}")
# message(STATUS "**OpenSSL_INCLUDE_DIRS: ${OpenSSL_INCLUDE_DIRS}")
# message(STATUS "**OpenSSL_LIBRARY_DIRS: ${OpenSSL_LIBRARY_DIRS}")
# message(STATUS "**OpenSSL_LIBRARIES: ${OpenSSL_LIBRARIES}")
# message(STATUS "**LIBPCAP_INCLUDE_DIRS: ${LIBPCAP_INCLUDE_DIRS}")
# message(STATUS "**LIBPCAP_LIBRARY_DIRS: ${LIBPCAP_LIBRARY_DIRS}")
# message(STATUS "**LIBPCAP_LIBRARIES: ${LIBPCAP_LIBRARIES}")

#关闭一些编译警告 (注：有些警告不应关闭)
add_compile_options(-Wno-sign-compare)  #有符号和无符号类型比较
add_compile_options(-Wno-unused-function)
add_compile_options(-Wno-unused-parameter)
add_compile_options(-Wno-unused-variable)
add_compile_options(-Wno-unused-result)
add_compile_options(-Wno-unused-but-set-variable)
add_compile_options(-Wno-attributes)
add_compile_options(-Wno-reorder)
add_compile_options(-Wno-unknown-pragmas)
add_compile_options(-Wno-endif-labels)
add_compile_options(-Wno-switch)
add_compile_options(-Wno-comment)
add_compile_options(-Wno-pointer-arith)
add_compile_options(-Wno-misleading-indentation)
add_compile_options(-Wno-parentheses)
# add_compile_options(-Wno-delete-non-virtual-dtor)
add_compile_options(-Wno-return-local-addr)
add_compile_options(-Wall -Wextra )
# -Wall 启用所有常见警告
# -Wextra 启用额外警告 
# -Werror 将警告视为错误